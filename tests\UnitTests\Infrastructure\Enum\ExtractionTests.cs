using Infrastructure.Enum;
using Xunit;

namespace UnitTests.Infrastructure.Enum
{
    public class ExtractionTests
    {
        [Fact]
        public void Extraction_EnumValues_ShouldHaveCorrectNumericValues()
        {
            // Assert
            Assert.Equal(0, (int)Extraction.AsReported);
            Assert.Equal(1, (int)Extraction.TemplateBased);
        }

        [Fact]
        public void Extraction_EnumCount_ShouldHaveExpectedNumberOfValues()
        {
            // Act
            var allValues = System.Enum.GetValues<Extraction>();

            // Assert
            Assert.Equal(2, allValues.Length);
            Assert.Contains(Extraction.AsReported, allValues);
            Assert.Contains(Extraction.TemplateBased, allValues);
        }

        [Fact]
        public void Extraction_ToString_ShouldReturnCorrectNames()
        {
            // Assert
            Assert.Equal("AsReported", Extraction.AsReported.ToString());
            Assert.Equal("TemplateBased", Extraction.TemplateBased.ToString());
        }
    }
}