using Microsoft.AspNetCore.SignalR;

namespace Notification.Hubs
{
    /// <summary>
    /// Simple SignalR hub for sending real-time notifications
    /// </summary>
    public class NotificationHub : Hub
    {
        // This is a minimal implementation
        // The hub itself doesn't need any additional methods for basic functionality
        // SignalR will handle connections and disconnections automatically
    }
}
