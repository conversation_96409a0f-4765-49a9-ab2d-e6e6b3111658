using System.Linq.Expressions;

namespace Persistence.GenericRepository
{
    public interface IGenericRepository<TEntity>
    {
        IEnumerable<TEntity> Get();
        TEntity GetByID(object id);
        void Insert(TEntity entity);
        void InsertBulk(List<TEntity> entities);
        void Delete(object id);
        void Delete(TEntity entityToDelete);
        void Update(TEntity entityToUpdate);
        void UpdateBulk(List<TEntity> entityToUpdate);
        IEnumerable<TEntity> GetMany(Func<TEntity, bool> where);

        IQueryable<TEntity> GetManyQueryable(Func<TEntity, bool> where);
        IQueryable<TEntity> GetActiveRecords(Func<TEntity, bool> where = null);

        TEntity Get(Func<TEntity, Boolean> where);
        void Delete(Func<TEntity, Boolean> where);
        IEnumerable<TEntity> GetAll();
        IQueryable<TEntity> GetWithInclude(System.Linq.Expressions.Expression<Func<TEntity, bool>> predicate, params string[] include);
        bool Exists(object primaryKey);
        TEntity GetSingle(Func<TEntity, bool> predicate);

        TEntity GetFirst(Func<TEntity, bool> predicate);
        TEntity GetFirstOrDefault(Func<TEntity, bool> predicate);
        TEntity GetLastOrDefault(Func<TEntity, bool> predicate);

        bool ExistsAny(Func<TEntity, bool> predicate);
        #region async methods
        Task<IEnumerable<TEntity>> GetManyAsync(Func<TEntity, bool> where);
        Task<TEntity> GetFirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate);
        Task<TEntity> AddAsyn(TEntity entity);
        Task AddBulkAsyn(List<TEntity> entity);
        Task<int> CountAsync();
        Task<int> CountAsync(Expression<Func<TEntity, bool>> expression);
        Task<List<TEntity>> FindAllAsync(Expression<Func<TEntity, bool>> match);
        Task<TEntity> FindFirstAsync(Expression<Func<TEntity, bool>> match);
        Task<TEntity> FindSingleAsync(Expression<Func<TEntity, bool>> match);
        Task<TEntity> FindLastAsync(Expression<Func<TEntity, bool>> match);
        Task<ICollection<TEntity>> FindByAsyn(Expression<Func<TEntity, bool>> predicate);
        Task<bool> ExistsAsyncAny(Expression<Func<TEntity, bool>> predicate);
        Task<List<TEntity>> GetAllAsyn();
        Task<int> SaveAsync();
        IQueryable<TEntity> GetQueryable();
        #endregion
    }
}