using Newtonsoft.Json;
using System.Text.Json.Serialization;

namespace Infrastructure.Contract.Extract
{
    public class JobStatusResponse
    {
        [JsonProperty("isError")]
        public bool IsError { get; set; }
        
        [JsonProperty("timestamp")]
        public string Timestamp { get; set; } = string.Empty;
        
        [JsonProperty("job_id")]
        public Guid JobId { get; set; }
        
        [JsonProperty("time_elapsed")]
        public double? TimeElapsed { get; set; }
        
        [JsonProperty("message")]
        public string? Message { get; set; }
        
        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;
        
        [JsonProperty("data")]
        public object? Data { get; set; }
    }
}