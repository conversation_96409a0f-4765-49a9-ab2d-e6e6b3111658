using Amazon;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;
using Newtonsoft.Json.Linq;
using Serilog;
using System.Data.SqlClient;
using System.Text;

namespace API.Extensions;
public static class AwsSecretsManagerHelper
{
    public const string DEFAULT_REGION = "eu-west-1";
    private static string _connectionString = "";

    public static string GetConnectionString()
    {
        return _connectionString;
    }

    public static string UpdateConnectionString(string foliosureConnectionString, IConfiguration configuration)
    {
        if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == Environments.Development)
        {
            _connectionString = foliosureConnectionString;
            return foliosureConnectionString;
        }
        var secret = GetSecret($"{configuration["AWS:SecretKey"]}", DEFAULT_REGION);
        dynamic jObject = JObject.Parse(secret);
        var sqlConnectionStringBuilder = new SqlConnectionStringBuilder(foliosureConnectionString)
        {
            DataSource = jObject.host,
            UserID = jObject.username,
            Password = jObject.password,
            MultipleActiveResultSets = true,
            TrustServerCertificate = true
        };
        _connectionString = sqlConnectionStringBuilder.ConnectionString;
        return _connectionString;
    }

    public static string GetSecret(string secretName, string region)
    {
        IAmazonSecretsManager client = new AmazonSecretsManagerClient(RegionEndpoint.GetBySystemName(region));
        GetSecretValueRequest request = new()
        {
            SecretId = secretName,
            VersionStage = "AWSCURRENT" // VersionStage defaults to AWSCURRENT if unspecified.
        };
        string secret = string.Empty;
        try
        {
            GetSecretValueResponse response = client.GetSecretValueAsync(request).Result;
            if (response.SecretString != null)
            {
                secret = response.SecretString;
            }
            else
            {
                MemoryStream memoryStream = response.SecretBinary;
                StreamReader reader = new(memoryStream);
                secret = Encoding.UTF8.GetString(Convert.FromBase64String(reader.ReadToEnd()));
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "AwsSecretsManagerHelper:GetSecret:Exception: {Message}", ex.Message);
        }

        return secret;
    }
}