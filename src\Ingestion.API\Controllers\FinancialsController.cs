﻿using Ingestion.API.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Persistence.MongoDb;
namespace Ingestion.API.Controllers
{
    [ApiController]
    [Route("api")]
    [Authorize(Policy = JwtBearerDefaults.AuthenticationScheme)]
    public class FinancialsController(ILogger<FinancialsController> logger, IFinancialsRepository financialsRepository) : ControllerBase
    {
        private readonly ILogger<FinancialsController> _logger = logger;
        private readonly IFinancialsRepository _financialsRepository = financialsRepository;
        [HttpGet("pdf-highlights")]
        public async Task<ActionResult> GetPdfHighlights([FromQuery] GetPdfHighlightsRequest request)
        {
            _logger.LogInformation("Received GetPdfHighlights request");

            if (!ModelState.IsValid || !request.IsStateValid())
            {
                _logger.LogWarning("Invalid GetPdfHighlights request");
                return BadRequest("All parameters are required.");
            }
            var highlights = await _financialsRepository.GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(request.ProcessId, request.TableGroupLabel, request.PeriodDate);
            _logger.LogInformation("Returning highlights for ProcessId: {ProcessId}, TableGroupLabel: {TableGroupLabel}, PeriodDate: {PeriodDate}", request.ProcessId, request.TableGroupLabel, request.PeriodDate);
            return Ok(highlights);
        }
    }
}
