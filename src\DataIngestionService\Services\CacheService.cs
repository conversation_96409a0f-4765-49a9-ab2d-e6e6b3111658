using Microsoft.Extensions.Caching.Memory;
using System;

namespace DataIngestionService.Services
{
    public interface ICacheService
    {
        T GetOrSet<T>(string key, Func<T> getFunction, TimeSpan? expiration = null);
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getFunction, TimeSpan? expiration = null);
        void Remove(string key);
        void RemoveMany(params string[] keys);
    }

    public class CacheService : ICacheService
    {
        private readonly IMemoryCache _cache;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);

        public CacheService(IMemoryCache cache)
        {
            _cache = cache;
        }

        public T GetOrSet<T>(string key, Func<T> getFunction, TimeSpan? expiration = null)
        {
            if (!_cache.TryGetValue(key, out T value))
            {
                value = getFunction();
                var memoryCacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(expiration ?? _defaultExpiration);
                _cache.Set(key, value, memoryCacheEntryOptions);
            }
            return value;
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getFunction, TimeSpan? expiration = null)
        {
            if (!_cache.TryGetValue(key, out T value))
            {
                value = await getFunction();
                var memoryCacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(expiration ?? _defaultExpiration);
                _cache.Set(key, value, memoryCacheEntryOptions);
            }
            return value;
        }

        public void Remove(string key)
        {
            _cache.Remove(key);
        }

        public void RemoveMany(params string[] keys)
        {
            foreach (var key in keys)
            {
                _cache.Remove(key);
            }
        }
    }
}
