﻿using Amazon.S3.Model;
using Microsoft.AspNetCore.Http;
using System.Text;

namespace AWSS3.Interfaces;

/// <summary>
/// Interface for AWS S3 file upload operations.
/// </summary>
public interface IAWSS3Library
{
    /// <summary>
    /// Uploads a file to S3.
    /// </summary>
    /// <param name="key">The key under which the file is stored.</param>
    /// <param name="file">The stream of the file to upload.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task<string> UploadFileAsync(string key, IFormFile file);

    /// <summary>
    /// Retrieves a file from S3.
    /// </summary>
    /// <param name="key">The key of the file to retrieve.</param>
    /// <returns>A task that represents the asynchronous operation, containing the file stream.</returns>
    Task<string> GetFileAsync(string key);

    /// <summary>
    /// Deletes a file from S3.
    /// </summary>
    /// <param name="key">The key of the file to delete.</param>
    /// <returns>A task representing the asynchronous operation.</returns>
    Task DeleteFileAsync(string key);

    S3Setting GetBucketDetails();
    Task<StringBuilder> GetFileContentAsTextAsync(string filePath);
    
    /// <summary>
    /// Downloads a file object directly from S3 storage
    /// </summary>
    /// <param name="keyPath">The S3 key path of the file to download</param>
    /// <returns>The S3 GetObjectResponse containing the file stream and metadata, or null if the request fails</returns>
    Task<GetObjectResponse?> DownloadFileObject(string keyPath);
}

