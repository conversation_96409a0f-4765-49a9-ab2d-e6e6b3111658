using Infrastructure.Contract;
using Xunit;

namespace UnitTests.Infrastructure.Contract
{
    public class StatusResponseTests
    {
        [Fact]
        public void StatusResponse_DefaultValues_ShouldBeSetCorrectly()
        {
            // Act
            var statusResponse = new StatusResponse();

            // Assert
            Assert.False(statusResponse.IsSuccess);
            Assert.Equal(string.Empty, statusResponse.Message);
            Assert.Equal(Guid.Empty, statusResponse.Id);
        }

        [Fact]
        public void StatusResponse_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var testId = Guid.NewGuid();
            var testMessage = "Test successful operation";

            // Act
            var statusResponse = new StatusResponse
            {
                IsSuccess = true,
                Message = testMessage,
                Id = testId
            };

            // Assert
            Assert.True(statusResponse.IsSuccess);
            Assert.Equal(testMessage, statusResponse.Message);
            Assert.Equal(testId, statusResponse.Id);
        }
    }
}