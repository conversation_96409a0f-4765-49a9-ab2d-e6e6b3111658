﻿namespace Infrastructure.Contract
{
    public class KpiModel
    {
        public int PortfolioCompanyId { get; set; }
        public int KpiId { get; set; }
        public string KpiName { get; set; }
        public int? ParentKPIId { get; set; }
        public int? DisplayOrder { get; set; }
        public bool IsMapped { get; set; }
        public bool IsHeader { get; set; }
        public bool IsBoldKPI { get; set; }
        public int MappingKPIId { get; set; }
        public string KpiInfo { get; set; }
        public bool IsExtraction { get; set; }
        public string Synonym { get; set; }
        public string Definition { get; set; }
        public int MethodologyId { get; set; }
        public int ModuleId { get; set; }
        public int FundId { get; set; } = 0;

    }
}
