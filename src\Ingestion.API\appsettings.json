{"ConnectionStrings": {"DefaultDBConnection": "server=BAN-LG3F0HS3\\MURIGUR;database=foliosure;MultipleActiveResultSets=True;Integrated Security=True;TrustServerCertificate=True"}, "TokenConfigurations": {"Audience": "ExempleAudience", "Issuer": "Exemple<PERSON><PERSON>uer", "Minutes": 90, "FinalExpiration": 30, "IdleSession": 30, "SecretKey": "124523621452125942"}, "AWS": {"SecretKey": "nprd/foliosure/pod/dev"}, "IdentityServerConfig": {"Audience": "beat-foliosure-pod-pec-localhost-client-id_services", "Issuer": "https://test.beatapps.net/identity/test/beat/sts", "jwk_uri": "https://test.beatapps.net/identity/test/beat/sts/.well-known/openid-configuration/jwks"}, "Environment": "DEV", "ClientCode": "himera", "Serilog": {"MinimumLevel": {"Default": "Error", "Override": {"Microsoft": "Error", "Microsoft.AspNetCore": "Error", "System": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": ".\\Logs\\PEC-FS-.log", "restrictedToMinimumLevel": "Verbose", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {ElasticApmTraceId} {ElasticApmTransactionId} {Message:lj} <s:{SourceContext}>{NewLine}{Exception}", "buffered": false, "fileSizeLimitBytes": 10000000, "shared": true, "rollingInterval": "Day", "rollOnFileSizeLimit": true}}]}, "ElasticApm": {"SecretToken": "", "ServerUrls": "https://tools.beatapps.net/APM", "ServiceName": "bds-beat-foliosure-dev-api"}, "ServiceConfiguration": {"AWSS3": {"BucketName": "beat-foliosure-vault", "KeyPrefix": "feature/"}}, "AllowedHosts": "", "AllowedOrigins": "https://*.dev.beatapps.net,https://*.beatfoliosure.com,http://localhost:4200,http://localhost,http://localhost:7285,https://localhost:7285,https://*.test.beatapps.net,https://localhost:4200", "CspTrustedDomains": ["https://*.beatapps.net", "https://*.beatfoliosure.com", "http://localhost:4200", "http://localhost"], "JwtOption": {"TokenCancellationMode": true, "ExpiryTimeInMinute": 60}, "IsWorkflow": false, "IsPendoEnabled": false, "ServiceUrl": "https://localhost:5001/", "RevealBi": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "envSettings": {"environment": "<PERSON>", "nprdOrPrd": "nprd", "enableSMTPSecretManager": false, "DEFAULT_REGION": "eu-west-1"}, "EmailConfiguration": {"Email": "<EMAIL>", "Password": "", "UserName": "", "Host": "email-smtp.eu-west-1.amazonaws.com", "Port": "587", "EnvirnmentEmailKey": "beatflow@notification", "EnableSSL": true, "UseDefaultCredentials": false}, "ApiSettings": {"BaseApiUrl": "https://alexandria.beatapps.net/aiplatform-dev/nprd-dev/v2/ai", "ApiKey": "DmpKjdSdhtP5MCFot5YeTmQmygJTseNH44cOIOmYog"}, "MongoDb": {"ConnectionString": "mongodb://localhost:27017", "DatabaseName": "foliosure_ingestion_dev"}, "SQSSettings": {"RequestQueueUrl": "https://sqs.eu-west-1.amazonaws.com/633874203883/beat-foliosure-ingestion-dev", "ResponseQueueUrl": "https://sqs.eu-west-1.amazonaws.com/633874203883/beat-foliosure-ingestion-dev", "Region": "eu-west-1", "UseSQS": false}, "S3Setting": {"bucketname": "beat-foliosure-vault", "region": "eu-west-1", "keyPrefix": "feature", "folderPrefix": "Documents"}, "HealthChecksUI": {"HealthChecks": [{"Name": "Health Checks Dashboard", "Uri": "/healthcheck"}], "EvaluationTimeInSeconds": 5}}