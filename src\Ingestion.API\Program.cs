using API.Configuration;
using API.Extensions;
using API.Middlewares;
using Elastic.Apm.NetCoreAll;
using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Notification.Extensions;
using Serilog;

try
{
    var builder = WebApplication.CreateBuilder(args);

    // Configure configuration sources first
    builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
    builder.Configuration.AddJsonFile(
        $"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}.json",
        optional: true);
    builder.Configuration.AddEnvironmentVariables();
    builder.Configuration.AddCommandLine(args);

    // Configure Serilog
    Log.Logger = new LoggerConfiguration()
        .ReadFrom.Configuration(builder.Configuration)
        .Enrich.WithProperty("ApplicationName", "Ingestion.API")
        .Enrich.WithMachineName()
        .Enrich.WithEnvironmentName()
        .Enrich.FromLogContext()
        .CreateLogger();

    Log.Information("Starting Beat Foliosure Ingestion API");
    
    // Add Serilog to the application
    builder.Host.UseSerilog();
    // Configure services
    builder.Services.RegisterCommonServices(builder.Configuration);
    builder.Services.RegisterApplicationServices();
    builder.Services.ConfigureJobsUpdateService();
    // Add notification services with SignalR
    builder.Services.AddNotificationServices();
   
    builder.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(options =>
    {
        options.KeyLengthLimit = int.MaxValue; // or a more reasonable limit like 8192
        options.ValueLengthLimit = int.MaxValue; // or a more reasonable limit
        options.MultipartBodyLengthLimit = long.MaxValue; // or a more reasonable limit
        options.MultipartHeadersLengthLimit = int.MaxValue; // or a more reasonable limit
    });
    builder.WebHost.ConfigureKestrel(options =>
    {
        options.Limits.MaxRequestBodySize = int.MaxValue; // or a more reasonable limit
    });
    // Add health checks
    builder.Services.AddHealthChecks();
    builder.Services.AddHealthChecksUI().AddInMemoryStorage();

    var app = builder.Build();

    app.UseSwagger();
    app.UseSwaggerUI();

    // Configure Elastic APM before other middleware
    app.UseAllElasticApm(builder.Configuration);   

    app.UseHttpsRedirection();
    app.UseStaticFiles(); // Adding static files middleware
    
    // The order of these middleware components is critical for SignalR
    app.UseRouting(); // Add UseRouting middleware
    app.UseCors("AllowAll"); // CORS must be between UseRouting and UseEndpoints for SignalR
    app.UseMiddleware<GlobalExceptionHandler>();
    app.UseAuthentication();
    app.UseAuthorization();

    app.MapHealthChecks("/healthcheck", new HealthCheckOptions
    {
        Predicate = _ => true,
        ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
    });
    app.MapHealthChecksUI(options =>
    {
        options.UIPath = "/healthchecks-ui";
    });

    app.MapControllers();
    
    // Map the SignalR hub
    app.MapHub<Notification.Hubs.NotificationHub>("/notify");

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
    throw;
}
finally
{
    Log.CloseAndFlush();
}
