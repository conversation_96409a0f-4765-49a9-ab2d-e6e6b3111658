using Ingestion.API.Helpers;
using Moq;
using Persistence.GenericRepository;
using Persistence.Models;
using Persistence.UnitOfWork;
using System.Linq.Expressions;
using System.Security.Claims;
using Xunit;

namespace UnitTests.Helpers
{    public class HelperServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;        private readonly Mock<IGenericRepository<UserDetails>> _mockUserDetailsRepository;
        private readonly HelperService _helperService;

        public HelperServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockUserDetailsRepository = new Mock<IGenericRepository<UserDetails>>();
            _mockUnitOfWork.Setup(u => u.UserDetailsRepository).Returns(_mockUserDetailsRepository.Object);
            _helperService = new HelperService(_mockUnitOfWork.Object);
        }

        [Fact]
        public async Task GetCurrentUserId_WithNullUser_ReturnsNegativeOne()
        {
            // Act
            var result = await _helperService.GetCurrentUserId(null);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task GetCurrentUserId_WithPreferredUsernameClaimAndExistingUser_ReturnsUserId()
        {
            // Arrange
            const string email = "<EMAIL>";
            const int userId = 123;
            
            var claims = new List<Claim>
            {
                new Claim("preferred_username", email)
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);              UserDetails? userDetail = new UserDetails
            {
                UserID = userId,
                EmailID = email,
                IsDeleted = false,
                IsActive = true
            };            _mockUserDetailsRepository
                .Setup(r => r.FindFirstAsync(
                    It.Is<Expression<Func<UserDetails, bool>>>(x => true)))
                .ReturnsAsync(userDetail);

            // Act
            var result = await _helperService.GetCurrentUserId(principal);            // Assert
            Assert.Equal(userId, result);
            _mockUserDetailsRepository.Verify(
                r => r.FindFirstAsync(
                    It.IsAny<Expression<Func<UserDetails, bool>>>()), 
                Times.Once);
        }
        
        [Fact]
        public async Task GetCurrentUserId_WithPreferredUsernameClaimAndNonExistingUser_ReturnsZero()
        {
            // Arrange
            const string email = "<EMAIL>";
            
            var claims = new List<Claim>
            {
                new Claim("preferred_username", email)
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);
              _mockUserDetailsRepository
                .Setup(r => r.FindFirstAsync(
                    It.Is<Expression<Func<UserDetails, bool>>>(x => true)))
                .ReturnsAsync((UserDetails?)null);

            // Act
            var result = await _helperService.GetCurrentUserId(principal);

            // Assert
            Assert.Equal(0, result);
            _mockUserDetailsRepository.Verify(                r => r.FindFirstAsync(
                    It.IsAny<Expression<Func<UserDetails, bool>>>()), 
                Times.Once);
        }

        [Fact]
        public async Task GetCurrentUserId_WithNameIdentifierClaim_ReturnsUserId()
        {
            // Arrange
            const int userId = 123;
            
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, userId.ToString())
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = await _helperService.GetCurrentUserId(principal);

            // Assert
            Assert.Equal(userId, result);            // Verify repository was not called since the claim was already found
            _mockUserDetailsRepository.Verify(
                r => r.FindFirstAsync(
                    It.IsAny<Expression<Func<UserDetails, bool>>>()), 
                Times.Never);
        }

        [Fact]
        public async Task GetCurrentUserId_WithNoRelevantClaims_ReturnsZero()
        {
            // Arrange
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, "Test User") // Not a claim we're looking for
            };
            var identity = new ClaimsIdentity(claims);
            var principal = new ClaimsPrincipal(identity);

            // Act
            var result = await _helperService.GetCurrentUserId(principal);

            // Assert
            Assert.Equal(0, result);
        }
    }
}
