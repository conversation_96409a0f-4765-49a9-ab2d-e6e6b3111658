using AutoMapper;
using Contract;
using DataIngestionService.Helpers;
using Infrastructure.Contract;
using Infrastructure.DTOS.NonControllerDto;
using Persistence.Models;
using Persistence.Models.Specific;
using Xunit;

namespace UnitTests.Helpers
{
    public class MappingProfileTests
    {
        private readonly IMapper _mapper;

        public MappingProfileTests()
        {
            var configuration = new MapperConfiguration(cfg => cfg.AddProfile<MappingProfile>());
            _mapper = configuration.CreateMapper();
        }

        [Fact]
        public void Map_UploadDto_To_DataIngestionDocuments_ShouldMapCorrectly()
        {
            // Arrange
            var uploadDto = new UploadDto
            {
                Id = Guid.NewGuid(),
                ProcessId = Guid.NewGuid(),
                TenantId = Guid.NewGuid(),
                FeatureId = 1,
                CompanyId = 123,
                SourceId = 456,
                ExtractionType = "Manual"
            };

            // Act
            var result = _mapper.Map<DataIngestionDocuments>(uploadDto);

            // Assert
            Assert.Equal(uploadDto.Id, result.Id);
            Assert.Equal(uploadDto.ProcessId, result.ProcessId);
            Assert.Equal(uploadDto.TenantId, result.TenantId);
            Assert.False(result.IsDeleted);
            Assert.True((DateTime.UtcNow - result.CreatedOn).TotalSeconds < 1); // Created within the last second
        }

        [Fact]
        public void Map_UploadDto_To_DIMappingDocumentsDetails_ShouldMapCorrectly()
        {
            // Arrange
            var uploadDto = new UploadDto
            {
                FeatureId = 1,
                CompanyId = 123,
                ProcessId = Guid.NewGuid(),
                SourceId = 456,
                ExtractionType = "Template"
            };

            // Act
            var result = _mapper.Map<DIMappingDocumentsDetails>(uploadDto);

            // Assert
            Assert.Equal(uploadDto.FeatureId, result.FeatureId);
            Assert.Equal(uploadDto.CompanyId, result.CompanyId);
            Assert.Equal(uploadDto.ProcessId, result.ProcessId);
            Assert.Equal(uploadDto.SourceId, result.SourceTypeId);
            Assert.False(result.IsDeleted);
            Assert.True((DateTime.UtcNow - result.CreatedOn).TotalSeconds < 1);
        }

        [Fact]
        public void Map_SpecificDto_To_SpecificKpiDocument_ShouldHandleEmptyJobId()
        {
            // Arrange
            var specificDto = new SpecificDto
            {
                JobId = "", // Empty JobId should map to Guid.Empty
                CompanyId = "123",
                CompanyName = "Test Company",
                CurrencyCode = "USD",
                Unit = "Millions"
            };

            // Act
            var result = _mapper.Map<SpecificKpiDocument>(specificDto);

            // Assert
            Assert.Equal(Guid.Empty, result.JobId);
            Assert.Equal(specificDto.CompanyId, result.CompanyId);
            Assert.Equal(specificDto.CompanyName, result.CompanyName);
            Assert.Equal(specificDto.CurrencyCode, result.CurrencyCode);
            Assert.Equal(specificDto.Unit, result.Unit);
        }

        [Fact]
        public void Map_SpecificKpiDocument_To_SpecificDto_ShouldHandleEmptyJobId()
        {
            // Arrange
            var specificDocument = new SpecificKpiDocument
            {
                JobId = Guid.Empty, // Empty JobId should map to null string
                CompanyId = "123",
                CompanyName = "Test Company",
                CurrencyCode = "USD",
                Unit = "Millions"
            };

            // Act
            var result = _mapper.Map<SpecificDto>(specificDocument);

            // Assert
            Assert.Null(result.JobId);
            Assert.Equal(specificDocument.CompanyId, result.CompanyId);
            Assert.Equal(specificDocument.CompanyName, result.CompanyName);
            Assert.Equal(specificDocument.CurrencyCode, result.CurrencyCode);
            Assert.Equal(specificDocument.Unit, result.Unit);
        }
    }
}