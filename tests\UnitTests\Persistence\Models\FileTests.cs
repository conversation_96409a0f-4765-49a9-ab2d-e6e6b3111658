using Xunit;
using FileModel = Persistence.Models.File;

namespace UnitTests.Persistence.Models
{
    public class FileTests
    {
        [Fact]
        public void File_DefaultInitialization_ShouldHaveNullValues()
        {
            // Act
            var file = new FileModel();

            // Assert
            Assert.Null(file.Url);
            Assert.Null(file.FileName);
            Assert.Null(file.PageCount);
        }

        [Fact]
        public void File_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var file = new FileModel
            {
                Url = "https://example.com/document.pdf",
                FileName = "document.pdf",
                PageCount = 25
            };

            // Assert
            Assert.Equal("https://example.com/document.pdf", file.Url);
            Assert.Equal("document.pdf", file.FileName);
            Assert.Equal(25, file.PageCount);
        }

        [Fact]
        public void File_WithEmptyFileName_ShouldAcceptEmptyString()
        {
            // Act
            var file = new FileModel { FileName = string.Empty };

            // Assert
            Assert.Equal(string.Empty, file.FileName);
        }

        [Fact]
        public void File_WithZeroPageCount_ShouldAcceptZero()
        {
            // Act
            var file = new FileModel { PageCount = 0 };

            // Assert
            Assert.Equal(0, file.PageCount);
        }

        [Fact]
        public void File_WithNullPageCount_ShouldAcceptNull()
        {
            // Act
            var file = new FileModel { PageCount = null };

            // Assert
            Assert.Null(file.PageCount);
        }

        [Fact]
        public void File_WithLargePageCount_ShouldAcceptLargeNumbers()
        {
            // Act
            var file = new FileModel { PageCount = 10000 };

            // Assert
            Assert.Equal(10000, file.PageCount);
        }

        [Fact]
        public void File_WithLongUrl_ShouldAcceptLongUrls()
        {
            // Arrange
            var longUrl = "https://example.com/" + new string('a', 1000) + ".pdf";

            // Act
            var file = new FileModel { Url = longUrl };

            // Assert
            Assert.Equal(longUrl, file.Url);
        }
    }
}