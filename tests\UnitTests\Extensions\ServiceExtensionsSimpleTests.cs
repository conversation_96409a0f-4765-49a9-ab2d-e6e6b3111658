using API.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Caching.Memory;
using Moq;
using Xunit;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using DapperRepository;
using Persistence.DapperRepository;
using Persistence.UnitOfWork;
using Persistence.MongoDb;
using AWSS3.Interfaces;
using AWSS3;
using Amazon.S3;
using Amazon.SQS;
using Microsoft.EntityFrameworkCore;
using Persistence.DBEntitiesContext;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace UnitTests.Extensions
{
    public class ServiceExtensionsSimpleTests
    {
        [Fact]
        public void RegisterApplicationServices_ShouldReturnSameServiceCollection()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            var result = services.RegisterApplicationServices();

            // Assert
            Assert.Same(services, result);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureMemoryCache()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var memoryCacheDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IMemoryCache));
            Assert.NotNull(memoryCacheDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, memoryCacheDescriptor.Lifetime);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureRepositoryServices()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var dapperRepoDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IDapperGenericRepository));
            var repositoryFactoryDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IRepositoryFactory));
            var unitOfWorkDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IUnitOfWork));

            Assert.NotNull(dapperRepoDescriptor);
            Assert.Equal(ServiceLifetime.Transient, dapperRepoDescriptor.Lifetime);
            
            Assert.NotNull(repositoryFactoryDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, repositoryFactoryDescriptor.Lifetime);
            
            Assert.NotNull(unitOfWorkDescriptor);
            Assert.Equal(ServiceLifetime.Transient, unitOfWorkDescriptor.Lifetime);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureBusinessServices()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var transformationServiceDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ISpecificKpiTransformationService));
            var cacheServiceDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));

            Assert.NotNull(transformationServiceDescriptor);
            Assert.Equal(ServiceLifetime.Transient, transformationServiceDescriptor.Lifetime);
            
            Assert.NotNull(cacheServiceDescriptor);
            Assert.Equal(ServiceLifetime.Transient, cacheServiceDescriptor.Lifetime);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureAwsS3Services()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var awsS3Descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IAWSS3Library));
            Assert.NotNull(awsS3Descriptor);
            Assert.Equal(ServiceLifetime.Transient, awsS3Descriptor.Lifetime);
            Assert.Equal(typeof(AWSS3Library), awsS3Descriptor.ImplementationType);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureMongoDbServices()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var mongoDbDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IMongoDb));
            Assert.NotNull(mongoDbDescriptor);
            Assert.Equal(ServiceLifetime.Scoped, mongoDbDescriptor.Lifetime);
            Assert.Equal(typeof(MongoDbService), mongoDbDescriptor.ImplementationType);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureFileServices()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var extractServiceDescriptor = services.FirstOrDefault(d => d.ServiceType.Name == "IExtract");
            var fileServiceDescriptor = services.FirstOrDefault(d => d.ServiceType.Name == "IFileService");
            var jobsUpdateServiceDescriptor = services.FirstOrDefault(d => d.ServiceType.Name == "IJobsUpdateService");

            Assert.NotNull(extractServiceDescriptor);
            Assert.Equal(ServiceLifetime.Transient, extractServiceDescriptor.Lifetime);
            
            Assert.NotNull(fileServiceDescriptor);
            Assert.Equal(ServiceLifetime.Transient, fileServiceDescriptor.Lifetime);
            
            Assert.NotNull(jobsUpdateServiceDescriptor);
            Assert.Equal(ServiceLifetime.Transient, jobsUpdateServiceDescriptor.Lifetime);
        }

        [Fact]
        public void RegisterApplicationServices_ShouldConfigureGenericRepository()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();

            // Assert
            var genericRepoDescriptor = services.FirstOrDefault(d => d.ServiceType.IsGenericType && 
                d.ServiceType.GetGenericTypeDefinition() == typeof(IRepository<>));
            Assert.NotNull(genericRepoDescriptor);
            Assert.Equal(ServiceLifetime.Transient, genericRepoDescriptor.Lifetime);
        }

        [Fact]
        public void RegisterApplicationServices_WithNullServices_ShouldThrowArgumentNullException()
        {
            // Arrange
            IServiceCollection? nullServices = null;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => nullServices!.RegisterApplicationServices());
        }

        [Fact]
        public void RegisterApplicationServices_CalledMultipleTimes_ShouldAddServicesEachTime()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.RegisterApplicationServices();
            var countAfterFirst = services.Count;
            
            services.RegisterApplicationServices();
            var countAfterSecond = services.Count;

            // Assert
            Assert.True(countAfterSecond > countAfterFirst);
        }

        [Fact]
        public void RegisterCommonServices_MethodExists_ShouldBeAccessible()
        {
            // Verify the method signature and accessibility without actually calling it
            // to avoid MongoDB serializer registration conflicts
            
            // Act & Assert
            var method = typeof(ServiceExtensions).GetMethod("RegisterCommonServices");
            Assert.NotNull(method);
            Assert.True(method.IsStatic);
            Assert.True(method.IsPublic);
            
            var parameters = method.GetParameters();
            Assert.Equal(2, parameters.Length);
            Assert.Equal(typeof(IServiceCollection), parameters[0].ParameterType);
            Assert.Equal(typeof(IConfiguration), parameters[1].ParameterType);
            
            Assert.Equal(typeof(IServiceCollection), method.ReturnType);
        }
    }
}