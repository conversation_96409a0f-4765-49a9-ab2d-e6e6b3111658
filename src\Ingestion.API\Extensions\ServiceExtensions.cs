using Amazon.S3;
using Amazon.SQS;
using API.Filters.CustomAuthorization;
using AWSS3;
using AWSS3.Interfaces;
using DapperRepository;
using DataIngestionService;
using DataIngestionService.Helpers;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Serializers;
using Persistence.DapperRepository;
using Persistence.DBEntitiesContext;
using Persistence.MongoDb;
using Persistence.UnitOfWork;
using System.Diagnostics.CodeAnalysis;
using System.Text;

namespace API.Extensions;

[ExcludeFromCodeCoverage]
public static class ServiceExtensions
{
    public static IServiceCollection RegisterCommonServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure MongoDB GUID serialization globally
        ConfigureMongoDb();

        var connectionString = configuration.GetConnectionString("DefaultDBConnection");
        string connection = AwsSecretsManagerHelper.UpdateConnectionString(connectionString, configuration);
        services.AddDbContext<DBEntities>(options => options.UseSqlServer(connection, op => op.CommandTimeout(3600)));
        services.AddScoped<IDbConnectionFactory>((sp) => new DbConnectionFactory(connection));
        // Add health checks with SQL Server monitoring
        services.AddHealthChecks()
            .AddSqlServer(connection, name: "SQL Server", failureStatus: Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded);

        // Configure MongoDB
        services.ConfigureMongoDb(configuration);

        string allowedOriginsString = configuration.GetValue<string>("AllowedOrigins") ?? string.Empty;
        List<string> allowedDomains = allowedOriginsString.Split(',').ToList();
        HashSet<string> allowedDomainsSet = allowedDomains.ToHashSet();  
        services.AddCors(options =>
        {
            options.AddPolicy("AllowAll", builder =>
            {
                builder
                    .SetIsOriginAllowed(origin => allowedDomainsSet.Contains(origin.ToLower()))
                    .WithOrigins(allowedDomainsSet.ToArray())
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials(); // Required for SignalR
            });
        });
        IdentityServerConfig identityServerConfiguration = configuration.GetSection(nameof(IdentityServerConfig)).Get<IdentityServerConfig>();
        var tokenConfigurations = new TokenConfigurations();
        new ConfigureFromConfigurationOptions<TokenConfigurations>(
                configuration.GetSection("TokenConfigurations"))
            .Configure(tokenConfigurations);
        var key = Encoding.UTF8.GetBytes(tokenConfigurations.SecretKey);
        services.AddSingleton(tokenConfigurations);
        services.AddAWSService<IAmazonS3>();
        services.AddAWSService<IAmazonSQS>();
        services.Configure<AWSS3.S3Setting>(configuration.GetSection("S3Setting"));
        services.AddHttpClient();
        services.AddControllers();
        services.AddEndpointsApiExplorer();
        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v1", new OpenApiInfo { Title = "FolioSure API", Version = "1.0" });
            options.SwaggerDoc("v1.1", new OpenApiInfo { Title = "FolioSure API", Version = "1.0" });

            options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey
            });
            options.AddSecurityRequirement(new OpenApiSecurityRequirement() {
                    {
                        new OpenApiSecurityScheme {
                            Reference = new OpenApiReference {
                                    Type = ReferenceType.SecurityScheme,
                                        Id = "Bearer"
                                },
                                Scheme = "oauth2",
                                Name = "Bearer",
                                In = ParameterLocation.Header,
                        },
                        new List<string> ()
                    }
                });
        });
        services.AddHttpContextAccessor();
        services.AddDbContext<DBEntities>();
        services.AddAutoMapper(typeof(MappingProfile));
        services.AddAuthentication(authOptions =>
        {
            authOptions.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
            authOptions.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            authOptions.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;

        })
          .AddJwtBearer("identityserver", async bearerOptions =>
          {
              bearerOptions.Authority = identityServerConfiguration.Issuer;
              bearerOptions.TokenValidationParameters = new TokenValidationParameters
              {
                  ValidateIssuer = true,
                  ValidIssuer = identityServerConfiguration.Issuer,
                  ValidateAudience = true,
                  ValidAudience = identityServerConfiguration.Audience,
                  ValidateLifetime = true,
                  ValidateIssuerSigningKey = true,
                  ClockSkew = TimeSpan.FromMinutes(1),
                  IssuerSigningKey = await TokenSignatureExtension.GetJwk(configuration)
              };
              bearerOptions.Events = new JwtBearerEvents
              {
                  OnMessageReceived = context =>
                  {
                      var accessToken = context.Request.Query["access_token"];

                      // If the request is for our hub...
                      var path = context.HttpContext.Request.Path;
                      if (!string.IsNullOrEmpty(accessToken) &&
                          (path.ToString().Contains("/notify") || path.ToString().Contains("/file-upload-notification")))
                      {
                          // Read the token out of the query string
                          context.Token = accessToken;
                      }
                      return Task.CompletedTask;
                  },
                  OnAuthenticationFailed =context =>
                  {
                      return Task.CompletedTask;
                  }
              };
          })
          .AddJwtBearer("default", async bearerOptions =>
          {
              bearerOptions.Authority = identityServerConfiguration.Issuer;
              bearerOptions.RequireHttpsMetadata = false;
              bearerOptions.TokenValidationParameters = new TokenValidationParameters
              {
                  ValidateIssuer = true,
                  ValidIssuer = identityServerConfiguration.Issuer,
                  ValidateAudience = true,
                  ValidAudience = identityServerConfiguration.Audience,
                  ValidateLifetime = true,
                  ValidateIssuerSigningKey = true,
                  ClockSkew = TimeSpan.FromMinutes(1),
                  IssuerSigningKey = await TokenSignatureExtension.GetJwk(configuration)
              };
              bearerOptions.SaveToken = false;
          });
          
        // Add authorization with the Bearer policy
        
        services.AddTransient<IAuthorizationPolicyProvider, UserFeatureBasedPolicyProvider>();
        return services;
    }

    private static void ConfigureMongoDb()
    {
        // Configure MongoDB to use Standard GUID representation
        // Only register the GUID serializer once with GuidRepresentation.Standard
        BsonSerializer.RegisterSerializer(new GuidSerializer(GuidRepresentation.Standard));
    }

    public static IServiceCollection RegisterApplicationServices(this IServiceCollection services)
    {
        // Add memory cache
        services.AddMemoryCache();
        services.AddTransient<IDapperGenericRepository, DapperGenericRepository>();
        services.AddTransient<ISpecificKpiTransformationService, SpecificKpiTransformationService>();
        services.AddTransient<ICacheService, CacheService>();
        services.AddSingleton<IRepositoryFactory, RepositoryFactory>();
        services.AddScoped<IMongoDb, MongoDbService>();
        services.AddTransient<IHelperService,HelperService>();
        services.AddTransient<IUpload, UploadService>();
        services.AddTransient<IAWSS3Library, AWSS3Library>();
        services.AddTransient<IUnitOfWork, UnitOfWork>();
        services.AddTransient<IExtract, ExtractDocumentService>();
        services.AddTransient<IFileService, FileService>();
        services.AddTransient<IJobsUpdateService, JobsUpdateService>();
        services.AddTransient(typeof(IRepository<>), typeof(Repository<>));
        services.AddTransient<IFinancialsRepository, FinancialsRepository>();
        return services;
    }
}