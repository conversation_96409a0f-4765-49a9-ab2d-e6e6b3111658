using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class BaseEntityTests
    {
        [Fact]
        public void BaseEntity_DefaultInitialization_ShouldSetDefaultValues()
        {
            // Act
            var entity = new BaseEntity();

            // Assert
            Assert.True(entity.CreatedOn <= DateTime.UtcNow);
            Assert.True(entity.CreatedOn >= DateTime.UtcNow.AddMinutes(-1)); // Allow for small time differences
            Assert.Equal(0, entity.CreatedBy);
            Assert.Null(entity.ModifiedOn);
            Assert.Null(entity.ModifiedBy);
            Assert.False(entity.IsDeleted);
        }

        [Fact]
        public void BaseEntity_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var createdOn = DateTime.UtcNow.AddDays(-1);
            var modifiedOn = DateTime.UtcNow;

            // Act
            var entity = new BaseEntity
            {
                CreatedOn = createdOn,
                CreatedBy = 123,
                ModifiedOn = modifiedOn,
                ModifiedBy = 456,
                IsDeleted = true
            };

            // Assert
            Assert.Equal(createdOn, entity.CreatedOn);
            Assert.Equal(123, entity.CreatedBy);
            Assert.Equal(modifiedOn, entity.ModifiedOn);
            Assert.Equal(456, entity.ModifiedBy);
            Assert.True(entity.IsDeleted);
        }

        [Fact]
        public void BaseEntity_CreatedBy_ShouldAcceptPositiveValues()
        {
            // Act
            var entity = new BaseEntity { CreatedBy = 999 };

            // Assert
            Assert.Equal(999, entity.CreatedBy);
        }

        [Fact]
        public void BaseEntity_ModifiedBy_ShouldAcceptNullAndPositiveValues()
        {
            // Act
            var entity1 = new BaseEntity { ModifiedBy = null };
            var entity2 = new BaseEntity { ModifiedBy = 777 };

            // Assert
            Assert.Null(entity1.ModifiedBy);
            Assert.Equal(777, entity2.ModifiedBy);
        }

        [Fact]
        public void BaseEntity_ModifiedOn_ShouldAcceptNullAndDateTimeValues()
        {
            // Arrange
            var testDate = DateTime.UtcNow.AddHours(-5);

            // Act
            var entity1 = new BaseEntity { ModifiedOn = null };
            var entity2 = new BaseEntity { ModifiedOn = testDate };

            // Assert
            Assert.Null(entity1.ModifiedOn);
            Assert.Equal(testDate, entity2.ModifiedOn);
        }
    }
}