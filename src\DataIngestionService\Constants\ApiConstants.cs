using System;

namespace DataIngestionService.Constants
{
    /// <summary>
    /// Contains constants related to API endpoints and common values
    /// </summary>
    public static class ApiConstants
    {
              
        // API Settings Keys
        public const string API_SETTINGS_BASE_URL_KEY = "ApiSettings:BaseApiUrl";
        public const string API_SETTINGS_API_KEY_KEY = "ApiSettings:ApiKey";
        public const string ALEXANDRIA_API_KEY = "ApiSettings:ApiKey";

        // API Endpoints
        public const string EXTRACT_ENDPOINT = "/extract";
        public const string EXTRACT_UPLOAD_ENDPOINT = "/extract/upload";
        public const string JOB_STATUS_ENDPOINT_FORMAT = "/extract/{0}/data/status";
        public const string EXTRACTION_DATA_ENDPOINT_FORMAT = "/extract/{0}/data";
        
        // Headers
        public const string AUTHORIZATION_HEADER = "Authorization";
        public const string BEARER_PREFIX = "Bearer";
        public const string ACCEPT_HEADER = "accept";
        public const string ACCEPT_ALL = "*/*";
        public const string CONTENT_TYPE_HEADER = "Content-Type";
        public const string APPLICATION_JSON = "application/json";
        public const string API_KEY_HEADER = "x-api-key";
        public const string APP_KEY_NAME = "app-name";
        public const string APP_Name = "foliosure";


        // Status values
        public const string ERROR_STATUS = "ERROR";
        public const string ERROR_STATUS_LOWERCASE = "error";
        
        // Error messages
        public const string DESERIALIZE_ERROR = "Failed to deserialize response";
        public const string JOB_STATUS_ERROR_FORMAT = "Error checking job status: {0}";
        public const string DOCUMENT_UPLOAD_ERROR_FORMAT = "Error uploading documents: {0}";
        public const string DOCUMENT_EXTRACT_ERROR_FORMAT = "Error extracting documents: {0}";
        public const string EXTRACTION_DATA_ERROR_FORMAT = "Error fetching extraction data: {0}";
        
        public const string CHECK_JOB_STATUS_EXCEPTION_FORMAT = "Exception in CheckJobStatusAsync: {0}";
        public const string UPLOAD_DOCUMENTS_EXCEPTION_FORMAT = "Exception in UploadDocumentsAsync: {0}";
        public const string EXTRACT_DOCUMENTS_EXCEPTION_FORMAT = "Exception in ExtractDocumentsAsync: {0}";
        public const string FETCH_EXTRACTION_DATA_EXCEPTION_FORMAT = "Exception in FetchExtractionDataAsync: {0}";
        
        // Date Time Format
        public const string TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.ffffff";
        public const string STATUS_IN_PROGRESS = "progress";
        public const string STATUS_RUNNING = "running";
        public const string STATUS_COMPLETED = "Completed";
        public const string STATUS_FAILED = "Failed";
        public const string FileDraftBeforExtract = "File Draft before Extraction";
        public const string ExtractionInProgress = "Extraction in progress";
        /// </summary>
        public const string ExtractionCompleted = "Extraction Completed";
        public const string ExtractionFailed = "Extraction Failed";
        public const string DefaultAuthPolicy = "Bearer";
        public const string StatusInProgress = "In Progress";
        public const string AsIsExtraction="As Is Extraction";
        public const string SpecificKPI = "Specific KPI";

        public const string SPECIFIC_EXTRACTION_DATA = "/extract/kpi/{0}/output";
        public const string StaticInfo = "StaticInformation";
        public const string Company = "Company";
        public const string Fund = "Fund";
    }
}