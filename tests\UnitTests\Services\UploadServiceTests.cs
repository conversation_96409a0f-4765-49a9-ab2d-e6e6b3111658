using AutoMapper;
using AWSS3;
using AWSS3.Interfaces;
using Contract;
using DataIngestionService;
using DataIngestionService.Constants;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.GenericRepository;
using Persistence.Models;
using Persistence.UnitOfWork;
using System.Linq.Expressions;
using Xunit;
namespace UnitTests.Services
{
    public class UploadServiceTests
    {
        private readonly Mock<IAWSS3Library> _mockAwsS3Library;
        private readonly Mock<ILogger<UploadService>> _mockLogger;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ICacheService> _mockCacheService;
        private readonly Mock<IMongoDb> _mockMongoDb;
        private readonly Mock<IGenericRepository<DataIngestionDocuments>> _mockDataIngestionDocumentRepository;
        private readonly Mock<IGenericRepository<DIMappingDocumentsDetails>> _mockDIMappingDocumentsDetailsRepository;
        private readonly Mock<IGenericRepository<Jobs>> _mockJobsRepository;
        private readonly Mock<IGenericRepository<Status>> _mockStatusRepository;
        private readonly Mock<IGenericRepository<UserDetails>> _mockUserDetailsRepository;
        private readonly Mock<IGenericRepository<PortfolioCompanyDetails>> _mockPortfolioCompanyDetailsRepository;
        private readonly Mock<IGenericRepository<FundDetails>> _mockFundDetailsRepository;

        private readonly UploadService _service;
        private readonly AWSS3.S3Setting _s3Setting;

        public UploadServiceTests()
        {
            _mockAwsS3Library = new Mock<IAWSS3Library>();
            _mockLogger = new Mock<ILogger<UploadService>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockMapper = new Mock<IMapper>();
            _mockCacheService = new Mock<ICacheService>();
            _mockMongoDb = new Mock<IMongoDb>();
            _mockDataIngestionDocumentRepository = new Mock<IGenericRepository<DataIngestionDocuments>>();
            _mockDIMappingDocumentsDetailsRepository = new Mock<IGenericRepository<DIMappingDocumentsDetails>>();
            _mockJobsRepository = new Mock<IGenericRepository<Jobs>>();
            _mockStatusRepository = new Mock<IGenericRepository<Status>>();
            _mockUserDetailsRepository = new Mock<IGenericRepository<UserDetails>>();
            _mockPortfolioCompanyDetailsRepository = new Mock<IGenericRepository<PortfolioCompanyDetails>>();
            _mockFundDetailsRepository = new Mock<IGenericRepository<FundDetails>>();

            _s3Setting = new AWSS3.S3Setting
            {
                BucketName = "test-bucket",
                Region = "us-east-1",
                FolderPrefix = "test-folder",
                KeyPrefix = "test-prefix"
            };

            SetupUnitOfWorkRepositories();
            SetupAwsS3Library();

            _service = new UploadService(
                _mockAwsS3Library.Object,
                _mockLogger.Object,
                _mockUnitOfWork.Object,
                _mockMapper.Object,
                _mockCacheService.Object,
                _mockMongoDb.Object
            );
        }

        private void SetupUnitOfWorkRepositories()
        {
            _mockUnitOfWork.Setup(x => x.DataIngestionDocumentRepository).Returns(_mockDataIngestionDocumentRepository.Object);
            _mockUnitOfWork.Setup(x => x.DIMappingDocumentsDetailsRepository).Returns(_mockDIMappingDocumentsDetailsRepository.Object);
            _mockUnitOfWork.Setup(x => x.JobsRepository).Returns(_mockJobsRepository.Object);
            _mockUnitOfWork.Setup(x => x.StatusRepository).Returns(_mockStatusRepository.Object);
            _mockUnitOfWork.Setup(x => x.UserDetailsRepository).Returns(_mockUserDetailsRepository.Object);
            _mockUnitOfWork.Setup(x => x.PortfoiloCompanyDetailsRepository).Returns(_mockPortfolioCompanyDetailsRepository.Object);
            _mockUnitOfWork.Setup(x => x.FundDetailsRepository).Returns(_mockFundDetailsRepository.Object);
        }

        private void SetupAwsS3Library()
        {
            _mockAwsS3Library.Setup(x => x.GetBucketDetails()).Returns(_s3Setting);
            _mockAwsS3Library.Setup(x => x.UploadFileAsync(It.IsAny<string>(), It.IsAny<IFormFile>())).ReturnsAsync(string.Empty);
        }

        private static Mock<IFormFile> CreateMockFormFile(string fileName = "test.pdf", string contentType = "application/pdf", byte[]? content = null)
        {
            content ??= new byte[] { 1, 2, 3 };
            var mockFormFile = new Mock<IFormFile>();
            mockFormFile.Setup(f => f.FileName).Returns(fileName);
            mockFormFile.Setup(f => f.ContentType).Returns(contentType);
            mockFormFile.Setup(f => f.Length).Returns(content.Length);
            mockFormFile.Setup(f => f.OpenReadStream()).Returns(new MemoryStream(content));
            return mockFormFile;
        }

        [Fact]
        public async Task UploadFile_ShouldReturnUploadResponseDtoList_WhenCalledWithValidRequest()
        {
            // Arrange
            var userId = 1;
            var processId = Guid.NewGuid();
            var tenantId = Guid.NewGuid();
            var fileName = "test.pdf";
            var mockFormFile = CreateMockFormFile(fileName);

            var filePageDetails = new FilePageDetails { File = mockFormFile.Object, Errors = string.Empty };
            var uploadDto = new UploadDto
            {
                Id = Guid.NewGuid(),
                ProcessId = processId,
                TenantId = tenantId,
                Files = new List<FilePageDetails> { filePageDetails },
                ExtractionType = "TestType"
            };

            var dataIngestionDocument = new DataIngestionDocuments
            {
                Id = Guid.NewGuid(),
                ProcessId = processId,
                FileName = fileName,
                S3Path = "test-path",
                Extension = ".pdf"
            };

            _mockMapper.Setup(x => x.Map<DataIngestionDocuments>(It.IsAny<UploadDto>())).Returns(dataIngestionDocument);
            _mockDataIngestionDocumentRepository.Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>())).ReturnsAsync(dataIngestionDocument);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _mockMongoDb.Setup(x => x.AddIssuerDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(It.IsAny<UploadDto>())).Returns(new DIMappingDocumentsDetails { ProcessId = processId });

            // Act
            var result = await _service.UploadFile(uploadDto, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.IsType<List<UploadResponseDto>>(result);
            Assert.Equal(uploadDto.Id, result[0].DocumentId);
            Assert.Equal(processId, result[0].ProcessId);
            Assert.Contains("s3://", result[0].Url);

            // Verify interactions
            _mockAwsS3Library.Verify(x => x.UploadFileAsync(It.IsAny<string>(), It.IsAny<IFormFile>()), Times.Once);
            _mockDataIngestionDocumentRepository.Verify(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>()), Times.Once);
            _mockUnitOfWork.Verify(x => x.SaveAsync(), Times.AtLeastOnce);
        }

        [Fact]
        public async Task CreateDocumentMappingDetails_ShouldReturnProcessId_WhenMappingDoesNotExist()
        {
            // Arrange
            var userId = 1;
            var processId = Guid.NewGuid();
            var uploadDto = new UploadDto { ProcessId = processId, ExtractionType = "TestType" };
            _mockMongoDb.Setup(x => x.AddIssuerDetails(uploadDto, userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(uploadDto, userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(uploadDto)).Returns(new DIMappingDocumentsDetails { ProcessId = processId });
            _mockDataIngestionDocumentRepository
    .Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>()))
    .ReturnsAsync(new DataIngestionDocuments());
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.CreateDocumentMappingDetails(uploadDto, userId);

            // Assert
            Assert.Equal(processId, result);
        }

        [Fact]
        public async Task CreateDocumentMappingDetails_ShouldReturnEmptyGuid_WhenMappingExists()
        {
            // Arrange
            var userId = 1;
            var processId = Guid.NewGuid();
            var uploadDto = new UploadDto { ProcessId = processId, ExtractionType = "TestType" };
            _mockMongoDb.Setup(x => x.AddIssuerDetails(uploadDto, userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(uploadDto, userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(true);

            // Act
            var result = await _service.CreateDocumentMappingDetails(uploadDto, userId);

            // Assert
            Assert.Equal(Guid.Empty, result);
        }

        [Fact]
        public async Task GetUserList_ShouldReturnUserList()
        {
            // Arrange
            var users = new List<UserDetails> { new UserDetails { UserID = 1, FirstName = "A", LastName = "B" } };
            var mockUserRepo = new Mock<IGenericRepository<UserDetails>>();
            mockUserRepo.Setup(x => x.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<UserDetails, bool>>>())).ReturnsAsync(users);
            _mockUnitOfWork.Setup(x => x.UserDetailsRepository).Returns(mockUserRepo.Object);

            // Act
            var result = await _service.GetUserList();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
        }

        [Fact]
        public async Task GetDocumentProcessingStatusCount_ShouldReturnStatusCount()
        {
            // Arrange
            var statusInProgressId = Guid.NewGuid();
            var statusCompletedId = Guid.NewGuid();
            var statusFailedId = Guid.NewGuid();

            var statuses = new List<Status>
            {
                new Status { Id = statusInProgressId, Name = ApiConstants.StatusInProgress, IsDeleted = false },
                new Status { Id = statusCompletedId, Name = ApiConstants.STATUS_COMPLETED, IsDeleted = false },
                new Status { Id = statusFailedId, Name = ApiConstants.STATUS_FAILED, IsDeleted = false }
            };

            var jobs = new List<Jobs>
            {
                new Jobs { JobId = Guid.NewGuid(), ProcessId = Guid.NewGuid(), StatusId = statusInProgressId, IsDeleted = false },
                new Jobs { JobId = Guid.NewGuid(), ProcessId = Guid.NewGuid(), StatusId = statusCompletedId, IsDeleted = false },
                new Jobs { JobId = Guid.NewGuid(), ProcessId = Guid.NewGuid(), StatusId = statusFailedId, IsDeleted = false }
            };

            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Default", State = "Default", IsDeleted = false };

            _mockJobsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Jobs, bool>>>())).ReturnsAsync(jobs);
            _mockStatusRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Status, bool>>>())).ReturnsAsync(statuses);
            _mockStatusRepository.Setup(x => x.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<Status, bool>>>())).ReturnsAsync(defaultStatus);
            _mockCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<IEnumerable<Status>>>>(), It.IsAny<TimeSpan>()))
                .ReturnsAsync(statuses);
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);

            // Act
            var result = await _service.GetDocumentProcessingStatusCount();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.InProgressCount);
            Assert.Equal(1, result.CompletedCount);
            Assert.Equal(1, result.FailedCount);
        }

        [Fact]
        public async Task GetUploadedDocumentDetails_ShouldReturnDocumentDetails()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var companyId = 1;
            var fundId = 1;
            var userId = 1;
            var createdDate = DateTime.UtcNow;

            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails
                {
                    ProcessId = processId,
                    CompanyId = companyId,
                    FundId = fundId,
                    ExtractionType = "TestType",
                    CreatedBy = userId,
                    CreatedOn = createdDate,
                    IsDeleted = false
                }
            };

            var companies = new List<PortfolioCompanyDetails>
            {
                new PortfolioCompanyDetails
                {
                    PortfolioCompanyId = companyId,
                    CompanyName = "Test Company",
                    EncryptedPortfolioCompanyId = "encrypted123",
                    IsDeleted = false
                }
            };

            var funds = new List<FundDetails>
            {
                new FundDetails
                {
                    FundId = fundId,
                    FundName = "Test Fund",
                    EncryptedFundId = "encryptedFund123",
                    IsDeleted = false
                }
            };

            var jobs = new List<Jobs>
            {
                new Jobs
                {
                    ProcessId = processId,
                    JobId = Guid.NewGuid(),
                    Id = Guid.NewGuid(),
                    StatusId = Guid.NewGuid(),
                    IsDeleted = false
                }
            };

            var statuses = new List<Status>
            {
                new Status
                {
                    Id = jobs[0].StatusId,
                    Name = "In Progress",
                    State = "Processing",
                    IsDeleted = false
                }
            };

            var users = new List<UserDetails>
            {
                new UserDetails
                {
                    UserID = userId,
                    FirstName = "John",
                    LastName = "Doe",
                    IsDeleted = false
                }
            };

            var defaultStatus = new Status
            {
                Id = Guid.NewGuid(),
                Name = "Draft",
                State = "Draft",
                IsDeleted = false
            };

            // Setup queryable repositories
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.GetQueryable()).Returns(mappings.AsQueryable());
            _mockPortfolioCompanyDetailsRepository.Setup(x => x.GetQueryable()).Returns(companies.AsQueryable());
            _mockFundDetailsRepository.Setup(x => x.GetQueryable()).Returns(funds.AsQueryable());
            _mockJobsRepository.Setup(x => x.GetQueryable()).Returns(jobs.AsQueryable());
            _mockStatusRepository.Setup(x => x.GetQueryable()).Returns(statuses.AsQueryable());
            _mockUserDetailsRepository.Setup(x => x.GetQueryable()).Returns(users.AsQueryable());

            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);

            // Act
            var result = await _service.GetUploadedDocumentDetails();

            // Assert
            Assert.NotNull(result);
            var documentDetails = result.ToList();
            Assert.Single(documentDetails);
            var detail = documentDetails[0];
            Assert.Equal(processId, detail.ProcessId);
            Assert.Equal("Test Company", detail.CompanyName);
            Assert.Equal("Test Fund", detail.FundName);
            Assert.Equal("John Doe", detail.CreatedBy);
            Assert.Equal("TestType", detail.ExtractionType);
        }

        [Fact]
        public async Task GetFileStatus_ShouldReturnStatus()
        {
            // Arrange
            var status = new Status { Id = Guid.NewGuid(), Name = "Draft", State = "Draft" };
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(status);
            _mockUnitOfWork.Setup(x => x.StatusRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Status, bool>>>())).ReturnsAsync(status);

            // Act
            var result = await _service.GetFileStatus();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(status.Name, result.Name);
        }

        [Fact]
        public async Task GetProcessDetailsById_ShouldReturnProcessDetailsDto()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var companyId = 1;
            var fundId = 1;
            var jobId = Guid.NewGuid();
            var parentJobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var documentId = Guid.NewGuid();

            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails
                {
                    ProcessId = processId,
                    CompanyId = companyId,
                    FundId = fundId,
                    ExtractionType = "TestType",
                    CreatedBy = 1,
                    CreatedOn = DateTime.UtcNow,
                    IsDeleted = false
                }
            };

            var jobs = new List<Jobs>
            {
                new Jobs
                {
                    ProcessId = processId,
                    JobId = jobId,
                    ParentJobId = parentJobId,
                    Id = documentId,
                    StatusId = statusId,
                    IsDeleted = false
                }
            };

            var statuses = new List<Status>
            {
                new Status
                {
                    Id = statusId,
                    Name = "In Progress",
                    State = "Processing",
                    IsDeleted = false
                }
            };

            var documents = new List<DataIngestionDocuments>
            {
                new DataIngestionDocuments
                {
                    Id = documentId,
                    ProcessId = processId,
                    FileName = "test.pdf",
                    S3Path = "path/to/file.pdf",
                    Extension = ".pdf",
                    Type = "application/pdf",
                    IsDeleted = false
                }
            };

            var companies = new List<PortfolioCompanyDetails>
            {
                new PortfolioCompanyDetails
                {
                    PortfolioCompanyId = companyId,
                    CompanyName = "TestCo",
                    EncryptedPortfolioCompanyId = "enc123",
                    IsDeleted = false
                }
            };

            var funds = new List<FundDetails>
            {
                new FundDetails
                {
                    FundId = fundId,
                    FundName = "TestFund",
                    EncryptedFundId = "encFund123",
                    IsDeleted = false
                }
            };

            var defaultStatus = new Status
            {
                Id = Guid.NewGuid(),
                Name = "Draft",
                State = "Draft",
                IsDeleted = false
            };

            // Setup repository mocks
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(mappings);
            _mockJobsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Jobs, bool>>>())).ReturnsAsync(jobs);
            _mockDataIngestionDocumentRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(documents);
            _mockPortfolioCompanyDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>())).ReturnsAsync(companies);
            _mockFundDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<FundDetails, bool>>>())).ReturnsAsync(funds);
            
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);
            _mockCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<IEnumerable<Status>>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(statuses);
            _mockMongoDb.Setup(x => x.GetClassifiersByProcessId(It.IsAny<List<Guid>>())).ReturnsAsync(true);

            // Act
            var result = await _service.GetProcessDetailsById(processId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(processId, result.ProcessId);
            Assert.Equal(companyId.ToString(), result.CompanyId);
            Assert.Equal("TestCo", result.CompanyName);
            Assert.Equal("TestFund", result.FundName);
            Assert.Equal(jobId, result.JobId);
            Assert.Equal(parentJobId, result.ParentJobId);
            Assert.Equal("In Progress", result.Status);
            Assert.Equal("Processing", result.State);
            Assert.True(result.IsClassifiers);
            Assert.Single(result.Documents);
            Assert.Equal("TestType", result.ExtractionType);
        }

        [Fact]
        public async Task UpdateDocumentConfigurations_ShouldReturnTrue_WhenDocumentsExist()
        {
            // Arrange
            var userId = 1;
            var fileId = Guid.NewGuid();
            var fileConfig = new FileConfigurationDetails { FileId = fileId.ToString(), DocumentTypeId = 1, PeriodType = "Q1", Year = 2023, Month = "Jan", Quarter = "Q1" };
            var configs = new List<FileConfigurationDetails> { fileConfig };
            var documents = new List<DataIngestionDocuments> { new DataIngestionDocuments { Id = fileId } };
            _mockUnitOfWork.Setup(x => x.DataIngestionDocumentRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(documents);
            _mockUnitOfWork.Setup(x => x.DataIngestionDocumentRepository.UpdateBulk(It.IsAny<List<DataIngestionDocuments>>()));
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.UpdateDocumentConfigurations(configs, userId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task UpdateDocumentConfigurations_ShouldReturnFalse_WhenNoDocumentsExist()
        {
            // Arrange
            var userId = 1;
            var configs = new List<FileConfigurationDetails> { new FileConfigurationDetails { FileId = Guid.NewGuid().ToString() } };
            _mockUnitOfWork.Setup(x => x.DataIngestionDocumentRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(new List<DataIngestionDocuments>());

            // Act
            var result = await _service.UpdateDocumentConfigurations(configs, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetFileStatus_WithCacheException_ShouldPropagateException()
        {
            // Arrange
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>()))
                .ThrowsAsync(new Exception("Cache error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _service.GetFileStatus());
        }

        [Fact]
        public async Task GetDocumentProcessingStatusCount_WithEmptyJobs_ShouldReturnZeroCounts()
        {
            // Arrange
            var statuses = new List<Status>
            {
                new Status { Id = Guid.NewGuid(), Name = "InProgress", State = "Processing", IsDeleted = false },
                new Status { Id = Guid.NewGuid(), Name = "Completed", State = "Complete", IsDeleted = false },
                new Status { Id = Guid.NewGuid(), Name = "Failed", State = "Error", IsDeleted = false }
            };
            var jobs = new List<Jobs>(); // Empty jobs list
            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Default", State = "Default", IsDeleted = false };

            _mockJobsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Jobs, bool>>>())).ReturnsAsync(jobs);
            _mockCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<IEnumerable<Status>>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(statuses);
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);

            // Act
            var result = await _service.GetDocumentProcessingStatusCount();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.InProgressCount);
            Assert.Equal(0, result.CompletedCount);
            Assert.Equal(0, result.FailedCount);
        }

        [Fact]
        public async Task UpdateDocumentConfigurations_WithInvalidFileId_ShouldReturnFalse()
        {
            // Arrange
            var userId = 1;
            var fileConfig = new FileConfigurationDetails
            {
                FileId = "invalid-guid-format", // Invalid GUID
                DocumentTypeId = 1,
                PeriodType = "Q1"
            };
            var configs = new List<FileConfigurationDetails> { fileConfig };

            // Act
            var result = await _service.UpdateDocumentConfigurations(configs, userId);

            // Assert
            Assert.False(result);
        }

        #region Comprehensive Additional Tests

        [Fact]
        public async Task UploadFile_WithUpdateProcessId_ShouldConvertToGuid()
        {
            // Arrange
            var userId = 1;
            var updateProcessId = Guid.NewGuid().ToString();
            var mockFormFile = CreateMockFormFile();

            var uploadDto = new UploadDto
            {
                UpdateProcessId = updateProcessId,
                Files = new List<FilePageDetails> { new FilePageDetails { File = mockFormFile.Object, Errors = string.Empty } },
                ExtractionType = "TestType"
            };

            var dataIngestionDocument = new DataIngestionDocuments { Id = Guid.NewGuid() };

            _mockMapper.Setup(x => x.Map<DataIngestionDocuments>(It.IsAny<UploadDto>())).Returns(dataIngestionDocument);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(It.IsAny<UploadDto>())).Returns(new DIMappingDocumentsDetails());
            _mockDataIngestionDocumentRepository.Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>())).ReturnsAsync(dataIngestionDocument);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _mockMongoDb.Setup(x => x.AddIssuerDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);

            // Act
            var result = await _service.UploadFile(uploadDto, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            // Verify that ProcessId was set from UpdateProcessId
            Assert.NotEqual(Guid.Empty, uploadDto.ProcessId);
        }

        [Fact]
        public async Task UploadFile_WithMultipleFiles_ShouldReturnMultipleResponses()
        {
            // Arrange
            var userId = 1;
            var mockFormFile1 = CreateMockFormFile("file1.pdf");
            var mockFormFile2 = CreateMockFormFile("file2.pdf");

            var uploadDto = new UploadDto
            {
                Files = new List<FilePageDetails>
                {
                    new FilePageDetails { File = mockFormFile1.Object, Errors = string.Empty },
                    new FilePageDetails { File = mockFormFile2.Object, Errors = string.Empty }
                },
                ExtractionType = "TestType"
            };

            var dataIngestionDocument = new DataIngestionDocuments { Id = Guid.NewGuid() };

            _mockMapper.Setup(x => x.Map<DataIngestionDocuments>(It.IsAny<UploadDto>())).Returns(dataIngestionDocument);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(It.IsAny<UploadDto>())).Returns(new DIMappingDocumentsDetails());
            _mockDataIngestionDocumentRepository.Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>())).ReturnsAsync(dataIngestionDocument);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _mockMongoDb.Setup(x => x.AddIssuerDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);

            // Act
            var result = await _service.UploadFile(uploadDto, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            _mockAwsS3Library.Verify(x => x.UploadFileAsync(It.IsAny<string>(), It.IsAny<IFormFile>()), Times.Exactly(2));
            _mockDataIngestionDocumentRepository.Verify(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>()), Times.Exactly(2));
        }

        [Fact]
        public async Task UploadFile_WithFileErrors_ShouldParseAndStoreErrors()
        {
            // Arrange
            var userId = 1;
            var mockFormFile = CreateMockFormFile();
            var errors = "[\"Error 1\", \"Error 2\", \"\"]"; // JSON array with empty string

            var uploadDto = new UploadDto
            {
                Files = new List<FilePageDetails>
                {
                    new FilePageDetails { File = mockFormFile.Object, Errors = errors }
                },
                ExtractionType = "TestType"
            };

            var dataIngestionDocument = new DataIngestionDocuments { Id = Guid.NewGuid() };

            _mockMapper.Setup(x => x.Map<DataIngestionDocuments>(It.IsAny<UploadDto>())).Returns(dataIngestionDocument);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(It.IsAny<UploadDto>())).Returns(new DIMappingDocumentsDetails());
            _mockDataIngestionDocumentRepository.Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>())).ReturnsAsync(dataIngestionDocument);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _mockMongoDb.Setup(x => x.AddIssuerDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);

            // Act
            var result = await _service.UploadFile(uploadDto, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            
            // Verify that the document was called with parsed errors (empty strings removed)
            _mockDataIngestionDocumentRepository.Verify(x => x.AddAsyn(It.Is<DataIngestionDocuments>(doc => 
                doc.Errors == "Error 1,Error 2")), Times.Once);
        }

        [Fact]
        public async Task GetFileStatus_WithCustomState_ShouldReturnCorrectStatus()
        {
            // Arrange
            var customState = "CustomState";
            var expectedStatus = new Status { Id = Guid.NewGuid(), Name = "Custom", State = customState };
            
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(expectedStatus);

            // Act
            var result = await _service.GetFileStatus(customState);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedStatus.Name, result.Name);
            Assert.Equal(expectedStatus.State, result.State);
        }

        [Fact]
        public async Task GetProcessDetailsById_WithNoMappings_ShouldReturnEmptyProcessDetails()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var emptyMappings = new List<DIMappingDocumentsDetails>();

            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(emptyMappings);

            // Act
            var result = await _service.GetProcessDetailsById(processId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(processId, result.ProcessId);
            Assert.Empty(result.Documents);
            // When no mappings exist, it returns a new ProcessDetailsDto with only ProcessId set
            // All other string properties will be null by default
            Assert.Null(result.CompanyName);
            Assert.Null(result.FundName);
            Assert.Null(result.CompanyId);
            Assert.Null(result.EncryptedFundId);
            Assert.Null(result.ExtractionType);
        }

        [Fact]
        public async Task GetProcessDetailsById_WithNoCompanyId_ShouldSkipCompanyQuery()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails
                {
                    ProcessId = processId,
                    CompanyId = 0, // No company
                    FundId = 1,
                    ExtractionType = "TestType",
                    IsDeleted = false
                }
            };

            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Draft", State = "Draft" };

            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(mappings);
            _mockJobsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Jobs, bool>>>())).ReturnsAsync(new List<Jobs>());
            _mockCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<IEnumerable<Status>>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(new List<Status>());
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);
            _mockDataIngestionDocumentRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(new List<DataIngestionDocuments>());
            _mockFundDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<FundDetails, bool>>>())).ReturnsAsync(new List<FundDetails>());
            _mockMongoDb.Setup(x => x.GetClassifiersByProcessId(It.IsAny<List<Guid>>())).ReturnsAsync(false);

            // Act
            var result = await _service.GetProcessDetailsById(processId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(string.Empty, result.CompanyName);
            Assert.Equal(string.Empty, result.CompanyId);
            
            // Verify companies repository was not called since CompanyId is 0
            _mockPortfolioCompanyDetailsRepository.Verify(x => x.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>()), Times.Never);
        }

        [Fact]
        public async Task GetProcessDetailsById_WithNoFundId_ShouldSkipFundQuery()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails
                {
                    ProcessId = processId,
                    CompanyId = 1,
                    FundId = null, // No fund
                    ExtractionType = "TestType",
                    IsDeleted = false
                }
            };

            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Draft", State = "Draft" };

            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(mappings);
            _mockJobsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Jobs, bool>>>())).ReturnsAsync(new List<Jobs>());
            _mockCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<IEnumerable<Status>>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(new List<Status>());
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);
            _mockDataIngestionDocumentRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(new List<DataIngestionDocuments>());
            _mockPortfolioCompanyDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<PortfolioCompanyDetails, bool>>>())).ReturnsAsync(new List<PortfolioCompanyDetails>());
            _mockMongoDb.Setup(x => x.GetClassifiersByProcessId(It.IsAny<List<Guid>>())).ReturnsAsync(false);

            // Act
            var result = await _service.GetProcessDetailsById(processId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(string.Empty, result.FundName);
            Assert.Equal(0, result.FundId);
            
            // Verify fund repository was not called since FundId is null or 0
            _mockFundDetailsRepository.Verify(x => x.FindAllAsync(It.IsAny<Expression<Func<FundDetails, bool>>>()), Times.Never);
        }

        [Fact]
        public async Task GetUploadedDocumentDetails_WithLimitAndOffset_ShouldReturnCorrectSubset()
        {
            // Arrange
            var mappings = new List<DIMappingDocumentsDetails>();
            for (int i = 0; i < 20; i++)
            {
                mappings.Add(new DIMappingDocumentsDetails
                {
                    ProcessId = Guid.NewGuid(),
                    CompanyId = i + 1,
                    FundId = i + 1,
                    ExtractionType = $"Type{i}",
                    CreatedBy = 1,
                    CreatedOn = DateTime.UtcNow.AddDays(-i),
                    IsDeleted = false
                });
            }

            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Draft", State = "Draft" };

            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.GetQueryable()).Returns(mappings.AsQueryable());
            _mockPortfolioCompanyDetailsRepository.Setup(x => x.GetQueryable()).Returns(new List<PortfolioCompanyDetails>().AsQueryable());
            _mockFundDetailsRepository.Setup(x => x.GetQueryable()).Returns(new List<FundDetails>().AsQueryable());
            _mockJobsRepository.Setup(x => x.GetQueryable()).Returns(new List<Jobs>().AsQueryable());
            _mockStatusRepository.Setup(x => x.GetQueryable()).Returns(new List<Status>().AsQueryable());
            _mockUserDetailsRepository.Setup(x => x.GetQueryable()).Returns(new List<UserDetails>().AsQueryable());
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);

            // Act
            var result = await _service.GetUploadedDocumentDetails(limit: 5, offset: 5);

            // Assert
            Assert.NotNull(result);
            var documentDetails = result.ToList();
            Assert.Equal(5, documentDetails.Count); // Should return exactly 5 items
        }

        [Fact]
        public async Task GetUploadedDocumentDetails_WithOnlyFundData_ShouldReturnFundResults()
        {
            // Arrange
            var fundId = 1;
            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails
                {
                    ProcessId = Guid.NewGuid(),
                    CompanyId = 0, // No company
                    FundId = fundId,
                    ExtractionType = "FundType",
                    CreatedBy = 1,
                    CreatedOn = DateTime.UtcNow,
                    IsDeleted = false
                }
            };

            var funds = new List<FundDetails>
            {
                new FundDetails
                {
                    FundId = fundId,
                    FundName = "Test Fund Only",
                    IsDeleted = false
                }
            };

            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Draft", State = "Draft" };

            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.GetQueryable()).Returns(mappings.AsQueryable());
            _mockPortfolioCompanyDetailsRepository.Setup(x => x.GetQueryable()).Returns(new List<PortfolioCompanyDetails>().AsQueryable());
            _mockFundDetailsRepository.Setup(x => x.GetQueryable()).Returns(funds.AsQueryable());
            _mockJobsRepository.Setup(x => x.GetQueryable()).Returns(new List<Jobs>().AsQueryable());
            _mockStatusRepository.Setup(x => x.GetQueryable()).Returns(new List<Status>().AsQueryable());
            _mockUserDetailsRepository.Setup(x => x.GetQueryable()).Returns(new List<UserDetails>().AsQueryable());
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);

            // Act
            var result = await _service.GetUploadedDocumentDetails();

            // Assert
            Assert.NotNull(result);
            var documentDetails = result.ToList();
            Assert.Single(documentDetails);
            var detail = documentDetails[0];
            Assert.Equal("Test Fund Only", detail.FundName);
            Assert.Equal(string.Empty, detail.CompanyName); // No company
            Assert.Equal(fundId, detail.FundId);
        }

        [Fact]
        public async Task UpdateDocumentConfigurations_WithEmptyList_ShouldReturnFalse()
        {
            // Arrange
            var userId = 1;
            var emptyConfigs = new List<FileConfigurationDetails>();

            _mockDataIngestionDocumentRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(new List<DataIngestionDocuments>());

            // Act
            var result = await _service.UpdateDocumentConfigurations(emptyConfigs, userId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task UpdateDocumentConfigurations_WithPartialMatches_ShouldUpdateOnlyMatchingDocuments()
        {
            // Arrange
            var userId = 1;
            var fileId1 = Guid.NewGuid();
            var fileId2 = Guid.NewGuid();
            var fileId3 = Guid.NewGuid(); // This one won't have matching document

            var configs = new List<FileConfigurationDetails>
            {
                new FileConfigurationDetails { FileId = fileId1.ToString(), DocumentTypeId = 1, PeriodType = "Q1" },
                new FileConfigurationDetails { FileId = fileId2.ToString(), DocumentTypeId = 2, PeriodType = "Q2" },
                new FileConfigurationDetails { FileId = fileId3.ToString(), DocumentTypeId = 3, PeriodType = "Q3" }
            };

            var documents = new List<DataIngestionDocuments>
            {
                new DataIngestionDocuments { Id = fileId1, DocumentTypeId = 0 },
                new DataIngestionDocuments { Id = fileId2, DocumentTypeId = 0 }
                // fileId3 not present
            };

            _mockDataIngestionDocumentRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(documents);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.UpdateDocumentConfigurations(configs, userId);

            // Assert
            Assert.True(result);
            
            // Verify only 2 documents were updated (not the third one)
            _mockDataIngestionDocumentRepository.Verify(x => x.UpdateBulk(It.Is<List<DataIngestionDocuments>>(list => list.Count == 2)), Times.Once);
        }

        [Fact]
        public async Task GetUserList_WithDeletedUsers_ShouldReturnOnlyActiveUsers()
        {
            // Arrange
            var users = new List<UserDetails>
            {
                new UserDetails { UserID = 1, FirstName = "Active", LastName = "User", IsDeleted = false },
                new UserDetails { UserID = 2, FirstName = "Deleted", LastName = "User", IsDeleted = true }
            };

            _mockUserDetailsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<UserDetails, bool>>>())).ReturnsAsync(users.Where(u => !u.IsDeleted).ToList());

            // Act
            var result = await _service.GetUserList();

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("Active", result[0].FirstName);
        }

        [Fact]
        public async Task GetDocumentProcessingStatusCount_WithMixedStatusNames_ShouldCountCorrectly()
        {
            // Arrange
            var statusInProgressId = Guid.NewGuid();
            var statusCompletedId = Guid.NewGuid();
            var statusFailedId = Guid.NewGuid();
            var otherStatusId = Guid.NewGuid();

            var statuses = new List<Status>
            {
                new Status { Id = statusInProgressId, Name = ApiConstants.StatusInProgress, IsDeleted = false },
                new Status { Id = statusCompletedId, Name = ApiConstants.STATUS_COMPLETED, IsDeleted = false },
                new Status { Id = statusFailedId, Name = ApiConstants.STATUS_FAILED, IsDeleted = false },
                new Status { Id = otherStatusId, Name = "Other Status", IsDeleted = false }
            };

            var jobs = new List<Jobs>
            {
                new Jobs { StatusId = statusInProgressId, IsDeleted = false }, // In Progress
                new Jobs { StatusId = statusInProgressId, IsDeleted = false }, // In Progress
                new Jobs { StatusId = statusCompletedId, IsDeleted = false }, // Completed
                new Jobs { StatusId = statusFailedId, IsDeleted = false }, // Failed
                new Jobs { StatusId = otherStatusId, IsDeleted = false }, // Other (should use default status name)
                new Jobs { StatusId = Guid.NewGuid(), IsDeleted = false }  // Non-matching status (should use default status name)
            };

            var defaultStatus = new Status { Id = Guid.NewGuid(), Name = "Default", State = "Default" };

            // Setup repository calls
            _mockJobsRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Jobs, bool>>>())).ReturnsAsync(jobs);
            _mockStatusRepository.Setup(x => x.FindAllAsync(It.IsAny<Expression<Func<Status, bool>>>())).ReturnsAsync(statuses);
            _mockStatusRepository.Setup(x => x.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<Status, bool>>>())).ReturnsAsync(defaultStatus);
            
            // Setup cache calls to return the same data as the repositories would
            _mockCacheService.Setup(x => x.GetOrSetAsync(It.IsAny<string>(), It.IsAny<Func<Task<IEnumerable<Status>>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(statuses);
            _mockCacheService.Setup(x => x.GetOrSet(It.IsAny<string>(), It.IsAny<Func<Task<Status>>>(), It.IsAny<TimeSpan>())).ReturnsAsync(defaultStatus);

            // Act
            var result = await _service.GetDocumentProcessingStatusCount();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.InProgressCount);
            Assert.Equal(1, result.CompletedCount);
            Assert.Equal(1, result.FailedCount);
        }

        [Fact]
        public async Task CreateDocumentMappingDetails_WithNullMongoDbCalls_ShouldStillCreateMapping()
        {
            // Arrange
            var userId = 1;
            var processId = Guid.NewGuid();
            var uploadDto = new UploadDto { ProcessId = processId, ExtractionType = "TestType", FundId = 123 };
            
            _mockMongoDb.Setup(x => x.AddIssuerDetails(uploadDto, userId)).ReturnsAsync((string)null);
            _mockMongoDb.Setup(x => x.AddSubPageDetails(uploadDto, userId)).ReturnsAsync((string)null);
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(uploadDto)).Returns(new DIMappingDocumentsDetails { ProcessId = processId, FundId = 123 });
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.AddAsyn(It.IsAny<DIMappingDocumentsDetails>())).ReturnsAsync(new DIMappingDocumentsDetails { ProcessId = processId });
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);

            // Act
            var result = await _service.CreateDocumentMappingDetails(uploadDto, userId);

            // Assert
            Assert.Equal(processId, result);
            _mockDIMappingDocumentsDetailsRepository.Verify(x => x.AddAsyn(It.Is<DIMappingDocumentsDetails>(d => d.FundId == 123)), Times.Once);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public async Task UploadFile_WithEmptyOrNullFileErrors_ShouldNotSetErrors(string errors)
        {
            // Arrange
            var userId = 1;
            var mockFormFile = CreateMockFormFile();

            var uploadDto = new UploadDto
            {
                Files = new List<FilePageDetails>
                {
                    new FilePageDetails { File = mockFormFile.Object, Errors = errors }
                },
                ExtractionType = "TestType"
            };

            var dataIngestionDocument = new DataIngestionDocuments { Id = Guid.NewGuid() };

            _mockMapper.Setup(x => x.Map<DataIngestionDocuments>(It.IsAny<UploadDto>())).Returns(dataIngestionDocument);
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(It.IsAny<UploadDto>())).Returns(new DIMappingDocumentsDetails());
            _mockDataIngestionDocumentRepository.Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>())).ReturnsAsync(dataIngestionDocument);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _mockMongoDb.Setup(x => x.AddIssuerDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);

            // Act
            var result = await _service.UploadFile(uploadDto, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            
            // Verify that document Errors property is not set when file errors are empty/null
            _mockDataIngestionDocumentRepository.Verify(x => x.AddAsyn(It.Is<DataIngestionDocuments>(doc => 
                string.IsNullOrEmpty(doc.Errors))), Times.Once);
        }

        [Fact]
        public async Task UploadFile_WithDifferentFileExtensions_ShouldHandleCorrectly()
        {
            // Arrange
            var userId = 1;
            var mockPdfFile = CreateMockFormFile("test.PDF", "application/pdf"); // Uppercase extension
            var mockDocFile = CreateMockFormFile("test.DOC", "application/msword");

            var uploadDto = new UploadDto
            {
                Files = new List<FilePageDetails>
                {
                    new FilePageDetails { File = mockPdfFile.Object, Errors = string.Empty },
                    new FilePageDetails { File = mockDocFile.Object, Errors = string.Empty }
                },
                ExtractionType = "TestType"
            };

            var pdfDocument = new DataIngestionDocuments { Id = Guid.NewGuid(), Extension = ".pdf" };
            var docDocument = new DataIngestionDocuments { Id = Guid.NewGuid(), Extension = ".doc" };

            // Setup mapper to return different documents for each call
            var mapperCallCount = 0;
            _mockMapper.Setup(x => x.Map<DataIngestionDocuments>(It.IsAny<UploadDto>()))
                .Returns(() => mapperCallCount++ == 0 ? pdfDocument : docDocument);
            
            _mockMapper.Setup(x => x.Map<DIMappingDocumentsDetails>(It.IsAny<UploadDto>())).Returns(new DIMappingDocumentsDetails());
            _mockDataIngestionDocumentRepository.Setup(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>())).ReturnsAsync((DataIngestionDocuments doc) => doc);
            _mockUnitOfWork.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _mockMongoDb.Setup(x => x.AddIssuerDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockMongoDb.Setup(x => x.AddSubPageDetails(It.IsAny<UploadDto>(), userId)).ReturnsAsync("");
            _mockDIMappingDocumentsDetailsRepository.Setup(x => x.ExistsAsyncAny(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(false);

            // Act
            var result = await _service.UploadFile(uploadDto, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            
            // Verify documents were added
            _mockDataIngestionDocumentRepository.Verify(x => x.AddAsyn(It.IsAny<DataIngestionDocuments>()), Times.Exactly(2));
            
            // Verify that both documents were processed (this validates the extension conversion logic is working)
            _mockAwsS3Library.Verify(x => x.UploadFileAsync(It.Is<string>(s => s.Contains(".pdf")), It.IsAny<IFormFile>()), Times.Once);
            _mockAwsS3Library.Verify(x => x.UploadFileAsync(It.Is<string>(s => s.Contains(".doc")), It.IsAny<IFormFile>()), Times.Once);
        }

        #endregion
    }
} 