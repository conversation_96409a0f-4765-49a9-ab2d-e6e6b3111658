using DataIngestionService.Helpers;
using System;
using Xunit;

namespace UnitTests.Helpers
{
    public class CommonTests
    {
        [Theory]
        [InlineData(1, "Jan")]
        [InlineData(2, "Feb")]
        [InlineData(3, "Mar")]
        [InlineData(4, "Apr")]
        [InlineData(5, "May")]
        [InlineData(6, "Jun")]
        [InlineData(7, "Jul")]
        [InlineData(8, "Aug")]
        [InlineData(9, "Sep")]
        [InlineData(10, "Oct")]
        [InlineData(11, "Nov")]
        [InlineData(12, "Dec")]
        [InlineData(13, "")]
        [InlineData(0, "")]
        [InlineData(null, "")]
        public void GetMonthName_ReturnsCorrectAbbreviation(int? monthNo, string expected)
        {
            // Act
            var result = monthNo.GetMonthName();

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void ConvertToGuid_WithNullOrEmptyString_ReturnsNewGuid()
        {
            // Arrange
            string nullString = null;
            string emptyString = string.Empty;
            string whitespaceString = "   ";

            // Act
            var resultFromNull = nullString.ConvertToGuid();
            var resultFromEmpty = emptyString.ConvertToGuid();
            var resultFromWhitespace = whitespaceString.ConvertToGuid();

            // Assert
            Assert.NotEqual(Guid.Empty, resultFromNull);
            Assert.NotEqual(Guid.Empty, resultFromEmpty);
            Assert.NotEqual(Guid.Empty, resultFromWhitespace);
        }

        [Fact]
        public void ConvertToGuid_WithValidGuidString_ReturnsValidGuid()
        {
            // Arrange
            var expectedGuid = Guid.NewGuid();
            var guidString = expectedGuid.ToString();

            // Act
            var result = guidString.ConvertToGuid();

            // Assert
            Assert.Equal(expectedGuid, result);
        }

        [Fact]
        public void ConvertToGuid_WithInvalidGuidString_ReturnsNewGuid()
        {
            // Arrange
            var invalidGuidString = "not-a-guid";

            // Act
            var result = invalidGuidString.ConvertToGuid();

            // Assert
            Assert.NotEqual(Guid.Empty, result);
            // Ensure we got a new Guid, not the invalid one
            Assert.NotEqual(invalidGuidString, result.ToString());
        }
    }
}
