﻿namespace Ingestion.API.Helpers
{
    public class IdentityServerConfig
    {
        public string Issuer { get; set; }
        public string Audience { get; set; }
        public string IdentityServerClientId { get; set; }
        public string OidcApiName { get; set; }
        public string ApiScope { get; set; }
        public string ApiName { get; set; }
    }
    public class TokenConfigurations
    {
        public string Audience { get; set; }
        public string Issuer { get; set; }
        public int Minutes { get; set; }
        public int FinalExpiration { get; set; }
        public int IdleSession { get; set; }
        public string SecretKey { get; set; }
    }

}
