using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Infrastructure.DTOS.NonControllerDto
{
    public class ClassifierRequestDto
    {
        [JsonPropertyName("isError")]
        public bool IsError { get; set; }
        
        [JsonPropertyName("status")]
        public string Status { get; set; }
        
        [JsonPropertyName("job_id")]
        public string JobId { get; set; }
        
        [JsonPropertyName("timestamp")]
        public string Timestamp { get; set; }
        
        [JsonPropertyName("message")]
        public string Message { get; set; }
        
        [JsonPropertyName("files")]
        public List<ClassifierFileInfoDto> Files { get; set; }
        
        [JsonPropertyName("tables")]
        public List<ClassifierTableTypeDto> Tables { get; set; }
    }

    public class ClassifierFileInfoDto
    {
        [JsonPropertyName("file_name")]
        public string FileName { get; set; }
        
        [JsonPropertyName("s3_path")]
        public string S3Path { get; set; }
        
        [JsonPropertyName("source")]
        public string Source { get; set; }
        
        [JsonPropertyName("file_id")]
        public string FileId { get; set; }
    }

    public class ClassifierTableTypeDto
    {
        [JsonPropertyName("label")]
        public string Label { get; set; }
        
        [JsonPropertyName("suggestions")]
        public List<ClassifierTableSuggestionDto> Suggestions { get; set; }
    }

    public class ClassifierTableSuggestionDto
    {
        [JsonPropertyName("bbox")]
        public ClassifierBboxDto Bbox { get; set; }
        
        [JsonPropertyName("page")]
        public int Page { get; set; }
        
        [JsonPropertyName("file_id")]
        public string? FileId { get; set; }
        
        [JsonPropertyName("score")]
        public double Score { get; set; }
    }

    public class ClassifierBboxDto
    {
        [JsonPropertyName("x1")]
        public double X1 { get; set; }
        
        [JsonPropertyName("y1")]
        public double Y1 { get; set; }
        
        [JsonPropertyName("x2")]
        public double X2 { get; set; }
        
        [JsonPropertyName("y2")]
        public double Y2 { get; set; }
    }
}