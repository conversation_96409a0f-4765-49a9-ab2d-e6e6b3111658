using Ingestion.API.Helpers;
using System;
using Xunit;

namespace UnitTests.Helpers
{
    public class TokenConfigurationsTests
    {
        [Fact]
        public void TokenConfigurations_PropertiesSet_ReturnsCorrectValues()
        {
            // Arrange
            var tokenConfig = new TokenConfigurations
            {
                Audience = "test-audience",
                Issuer = "test-issuer",
                Minutes = 30,
                FinalExpiration = 60,
                IdleSession = 15
            };

            // Assert
            Assert.Equal("test-audience", tokenConfig.Audience);
            Assert.Equal("test-issuer", tokenConfig.Issuer);
            Assert.Equal(30, tokenConfig.Minutes);
            Assert.Equal(60, tokenConfig.FinalExpiration);
            Assert.Equal(15, tokenConfig.IdleSession);
        }

        [Fact]
        public void IdentityServerConfig_PropertiesSet_ReturnsCorrectValues()
        {
            // Arrange
            var identityConfig = new IdentityServerConfig
            {
                Issuer = "test-issuer",
                Audience = "test-audience",
                IdentityServerClientId = "test-client-id",
                OidcApiName = "test-oidc-api-name",
                ApiScope = "test-api-scope",
                ApiName = "test-api-name"
            };

            // Assert
            Assert.Equal("test-issuer", identityConfig.Issuer);
            Assert.Equal("test-audience", identityConfig.Audience);
            Assert.Equal("test-client-id", identityConfig.IdentityServerClientId);
            Assert.Equal("test-oidc-api-name", identityConfig.OidcApiName);
            Assert.Equal("test-api-scope", identityConfig.ApiScope);
            Assert.Equal("test-api-name", identityConfig.ApiName);
        }
    }
}
