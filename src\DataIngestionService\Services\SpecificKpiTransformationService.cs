using AutoMapper;
using DapperRepository;
using DataIngestionService.Constants;
using DataIngestionService.IServices;
using Infrastructure.Contract;
using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Enum;
using Infrastructure.Extentions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Persistence.Models.Specific;
using Persistence.MongoDb;
using Persistence.UnitOfWork;
using System.Reflection;
using System.Text.Json;

namespace DataIngestionService.Services
{
    public class SpecificKpiTransformationService : ISpecificKpiTransformationService
    {
        private readonly IRepository<SpecificKpiDocument> _specificKpiRepository;
        private readonly ILogger<SpecificKpiTransformationService> _logger;
        private readonly IMapper _mapper;
        private readonly IDapperGenericRepository _dapper;
        private readonly IUnitOfWork _unitOfWork;
        public SpecificKpiTransformationService(
            IRepositoryFactory repositoryFactory,
            ILogger<SpecificKpiTransformationService> logger,
            IWebHostEnvironment hostingEnvironment,
            IMapper mapper, IDapperGenericRepository dapper, IUnitOfWork unitOfWork)
        {
            _specificKpiRepository = repositoryFactory.GetRepository<SpecificKpiDocument>();
            _logger = logger;
            _mapper = mapper;
            _dapper = dapper;
            _unitOfWork = unitOfWork;
        }

        public async Task<SpecificDto> TransformDsSpecificToSpecific(string dsSpecificJson, Guid processId)
        {
            try
            {
                var dsSpecificDto = JsonSerializer.Deserialize<DsSpecificDto>(dsSpecificJson);
                if (dsSpecificDto == null)
                {
                    _logger.LogError("Failed to deserialize DsSpecific JSON");
                    throw new ArgumentException("Invalid DsSpecific JSON format");
                }
                string fundId = string.IsNullOrEmpty(dsSpecificDto.CompanyId) ? await GetFundIdByProcessId(processId) : dsSpecificDto.CompanyId;
                // Transform the DTO
                var specificDto = dsSpecificDto.ToSpecificDto(fundId);
                if (specificDto == null)
                {
                    _logger.LogError("Failed to transform DsSpecific to Specific");
                    throw new InvalidOperationException("Transformation failed");
                }
                await SaveSpecificKpiDocument(specificDto, processId);
                return specificDto;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing JSON. ProcessId: {ProcessId}, Input: {Input}, Error: {ErrorMessage}", processId, dsSpecificJson, ex.Message);
                throw new ArgumentException($"Invalid JSON format for ProcessId: {processId}", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error transforming DsSpecific to Specific. ProcessId: {ProcessId}, Input: {Input}, Error: {ErrorMessage}", processId, dsSpecificJson, ex.Message);
                throw new InvalidOperationException($"Error transforming DsSpecific to Specific. ProcessId: {processId}", ex);
            }
        }
        public async Task<string> SaveSpecificKpiDocument(SpecificDto specificDto, Guid processId, int userId = 0)
        {
            if (specificDto == null)
            {
                _logger.LogError("Failed to transform DsSpecific to Specific");
                throw new InvalidOperationException("Transformation failed");
            }
            SpecificKpiDocument? specificKpiDocument = MapDtoToDocument(specificDto);
            if (specificKpiDocument == null)
            {
                _logger.LogError("Failed to map SpecificDto to SpecificKpiDocument");
                throw new InvalidOperationException("Mapping failed");
            }

            specificKpiDocument.ProcessId = processId;
            specificKpiDocument.CreatedOn = DateTime.UtcNow;
            specificKpiDocument.CreatedBy = userId;
            specificKpiDocument.IsDeleted = false;
            await _specificKpiRepository.CreateAsync(specificKpiDocument);
            _logger.LogInformation("Specific KPI metadata saved successfully with ID: {Id}", specificKpiDocument.Id);

            return specificKpiDocument.Id;
        }
        public async Task<string> UpdateSpecificKpiDocument(SpecificDto specificDto, Guid processId, int userId)
        {
            if (specificDto == null)
            {
                _logger.LogError("Failed to transform DsSpecific to Specific");
                throw new InvalidOperationException("Transformation failed");
            }
            var existingDocument = await _specificKpiRepository.FindOneAsync(x => x.ProcessId == processId && !x.IsDeleted);
            // Find existing document
            if (existingDocument == null)
            {
                _logger.LogError("SpecificKpiDocument with Id not found for update");
                return string.Empty;
            }
            // Map updated fields from DTO
            var updatedDocument = MapDtoToDocument(specificDto);
            if (updatedDocument == null)
            {
                _logger.LogError("Failed to map SpecificDto to SpecificKpiDocument");
                throw new InvalidOperationException("Mapping failed");
            }
            updatedDocument.Id = existingDocument.Id;
            updatedDocument.ProcessId = processId;
            updatedDocument.ModifiedOn = DateTime.UtcNow;
            updatedDocument.ModifiedBy = userId;

            await _specificKpiRepository.UpdateAsync(existingDocument.Id, updatedDocument);
            _logger.LogInformation("Specific KPI metadata updated successfully with Id: {Id}", updatedDocument.Id);

            return updatedDocument.Id;
        }
        private SpecificKpiDocument? MapDtoToDocument(SpecificDto dto)
        {
            if (dto == null) return null;
            return _mapper.Map<SpecificKpiDocument>(dto);
        }
        public async Task<SpecificDto> GetSpecificKpiDocumentByProcessId(Guid processId)
        {
            var document = await _specificKpiRepository.FindOneAsync(x => x.ProcessId == processId && !x.IsDeleted);
            if (document == null)
            {
                _logger.LogWarning("SpecificKpiDocument not found for ProcessId: {ProcessId}", processId);
                return new SpecificDto();
            }
            await ProcessAllSectionsAndAssignKpis(document);
            return _mapper.Map<SpecificDto>(document);
        }
        private IEnumerable<string> ExtractAllCompanyIdsFromDocument(SpecificKpiDocument doc)
        {
            IEnumerable<string> ExtractIds(List<DataRow>? rows) =>
                rows?.Where(x => !string.IsNullOrEmpty(x.CompanyId)).Select(x => x.CompanyId) ?? [];
            var customSectionRows = doc.CustomSections?
        .Where(cs => cs.Data.Count > 0)
        .SelectMany(cs => cs.Data)
        .ToList();
            return ExtractIds(customSectionRows)
                .Distinct();
        }
        private List<KpiOption> BuildKpiOptionsForSectionByCompanyId(int moduleId, List<KpiModel> kpiModels, List<DataRow> dataRows)
        {
            var companyIds = GetDistinctCompanyIds(dataRows);
            return (
                     from k in kpiModels
                     where companyIds.Contains(k.PortfolioCompanyId) && k.ModuleId == moduleId
                     select new KpiOption
                     {
                         KpiId = k.KpiId,
                         Text = k.KpiName,
                         Value = k.KpiName,
                         KpiInfo = k.KpiInfo,
                         MappingId = k.MappingKPIId.ToString(),
                         MappingName = k.KpiName,
                         MethodologyId = k.MethodologyId,
                     }).OrderBy(x => x.KpiId).DistinctBy(x => x.KpiId).ToList();

        }
        private List<string> GetCustomSectionTypes(SpecificKpiDocument document)
        {
            return (document.CustomSections ?? new List<CustomSection>())
                .Select(x => x.SectionType)
                .ToList();
        }

        private async Task<List<ModuleDetails>> QueryModuleDetailsBySectionTypesAsync(SpecificKpiDocument document)
        {
            var customSectionTypes = GetCustomSectionTypes(document);
            return await _dapper.Query<ModuleDetails>(
                SqlConstants.QueryByGetModuleDetails,
                new { @module = string.Join(",", customSectionTypes) }
            );
        }
        private async Task<List<KpiModel>> FetchAllSectionKpisByCompanyIds(SpecificKpiDocument specificKpiDocument)
        {
            List<int> staticModuleIds = [];
            var queriedModuleDetails = await QueryModuleDetailsBySectionTypesAsync(specificKpiDocument);

            List<int> moduleIds = staticModuleIds
                .Concat(queriedModuleDetails.Select(m => m.ModuleId))
                .Distinct()
                .ToList();

            var allSectionCompanyIds = ExtractAllCompanyIdsFromDocument(specificKpiDocument).ToList();
            if (!allSectionCompanyIds.Any())
                return [];
            return await _dapper.Query<KpiModel>(
                SqlConstants.QueryByGetAllKpis,
                new { @companyIds = string.Join(",", allSectionCompanyIds), @moduleIds = string.Join(",", moduleIds) }
            );
        }
        /// <summary>
        /// Hrad Coded Code Need to be removed after refactoring
        /// </summary>
        /// <param name="specificKpiDocument"></param>
        /// <returns></returns>
        public async Task ProcessAllSectionsAndAssignKpis(SpecificKpiDocument specificKpiDocument)
        {
            if (specificKpiDocument == null)
                return;
            var queriedModuleDetails = await QueryModuleDetailsBySectionTypesAsync(specificKpiDocument);
            List<KpiModel> kpiModels = await FetchAllSectionKpisByCompanyIds(specificKpiDocument);
            List<SubPageFields> staticSectionFields = await FetchPageConfigStaticSectionFields();

            int fundId = Convert.ToInt32(specificKpiDocument.CompanyId);//need to remove once harccode removes
            List<KpiModel> fundFinancialsKpis = await FetchFundFinancialsByFundId(fundId);
            // Use the new method here
            ProcessCustomSections(specificKpiDocument.CustomSections, queriedModuleDetails, kpiModels, fundFinancialsKpis, staticSectionFields);
        }
        public void AssignKpisToSectionData(List<DataRow> dataRows, List<PeriodConfiguration> periodConfigurations, List<KpiModel> kpiModels, int moduleId = 0)
        {
            if (dataRows == null || !dataRows.Any())
                return;
            var companyIds = GetDistinctCompanyIds(dataRows);
            if (companyIds.Count > 0)
            {
                kpiModels = kpiModels.Where(k => companyIds.Contains(k.PortfolioCompanyId))
                    .ToList(); // Filter kpiModels based on companyIds
            }
            FetchCurrenciesByFundCompanyId(moduleId, dataRows, companyIds).Wait(); // Ensure currencies are fetched before processing
            foreach (var item in periodConfigurations)
            {
                // Order DocumentKpis by KpiId
                item.DocumentKpis = item.DocumentKpis.OrderBy(dk => dk.KpiId).ToList();
                if (item.SelectedKpis.Count == 0)
                {
                    item.SelectedKpis = (from k in kpiModels
                                            join dk in item.DocumentKpis on k.KpiId equals dk.KpiId
                                            where k.ModuleId == moduleId
                                            select new SelectedKpi
                                            {
                                                KpiId = k.KpiId,
                                                Text = k.KpiName,
                                                Value = k.KpiName,
                                                KpiInfo = k.KpiInfo,
                                                MappingId = k.MappingKPIId.ToString(),
                                                MappingName = k.KpiName,
                                                PeriodId = dk.PeriodId ?? item.PeriodId,
                                                MethodologyId = k.MethodologyId
                                            }
                                        ).OrderBy(sk => sk.KpiId)
                                        .DistinctBy(sk => sk.KpiId)
                                        .ToList();
                }
                item.Columns = item.DocumentKpis.Count;
            }
        }
        private static List<int> GetDistinctCompanyIds(List<DataRow> dataRows)
        {
            return dataRows?
                .Select(x => x.CompanyId)
                .Where(id => int.TryParse(id, out _))
                .Select(id => int.Parse(id))
                .Distinct()
                .ToList() ?? new List<int>();
        }
        public async Task<string> GetFundIdByProcessId(Guid processId)
        {
            var response = await _unitOfWork.DIMappingDocumentsDetailsRepository.GetFirstOrDefaultAsync(
              x => x.ProcessId == processId &&
                   !x.IsDeleted);
            return response?.FundId?.ToString() ?? string.Empty;
        }
        private async Task<List<SubPageFields>> FetchPageConfigStaticSectionFields(int subPageId = (int)PageConfigurationSubFeature.StaticInformation)
        {
            return await _dapper.Query<SubPageFields>(
                SqlConstants.QueryByGetPageConfigStaticFields,
                new { @SubPageId = subPageId }
            );
        }
        private async Task<List<KpiModel>> FetchFundFinancialsByFundId(int fundId = 0)
        {
            return await _dapper.Query<KpiModel>(
                SqlConstants.QueryByGetFundKpis,
                new { @fundId = fundId }
            );
        }
        private void BuildKpiOptionsForMasterSection(List<SubPageFields> subPageFields, List<KpiOption> MasterKpiOptions)
        {
            MasterKpiOptions.AddRange(
                subPageFields.OrderBy(x => x.FieldId).Select(x => new KpiOption
                {
                    KpiId = x.FieldId,
                    KpiInfo = string.Empty,
                    Value = x.AliasName,
                    Text = x.AliasName,
                    MappingId = x.FieldId.ToString(),
                    MappingName = x.AliasName
                })
                );

        }
        public void AssignStaticSectionData(List<DataRow> dataRows, List<PeriodConfiguration> periodConfigurations, List<SubPageFields> subPageFields)
        {
            if (dataRows == null || !dataRows.Any())
                return;
            foreach (var item in periodConfigurations)
            {
                item.DocumentKpis = item.DocumentKpis.OrderBy(dk => dk.KpiId).ToList();
                item.SelectedKpis = (
                    from k in subPageFields
                    join dk in item.DocumentKpis on k.FieldId equals dk.KpiId
                    select new SelectedKpi
                    {
                        KpiId = k.FieldId,
                        Text = k.AliasName,
                        Value = k.AliasName,
                        KpiInfo = string.Empty,
                        MappingId = k.FieldId.ToString(),
                        MappingName = k.AliasName,
                        PeriodId = dk.PeriodId ?? item.PeriodId
                    }
                ).OrderBy(sk => sk.KpiId).ToList();
                item.Columns = item.DocumentKpis.Count;
            }
        }
        private static int FindFundId(List<DataRow> dataRows)
        {
            if (dataRows == null || dataRows.Count == 0)
                return 0;
            var fundIdStr = dataRows[0].FundId;
            if (int.TryParse(fundIdStr, out int fundId))
                return fundId;

            return 0;
        }
        private static List<int> GetDistinctFundIds(List<DataRow> dataRows)
        {
            return dataRows?
                .Select(x => x.FundId)
                .Where(id => int.TryParse(id, out _))
                .Select(id => int.Parse(id))
                .Distinct()
                .ToList() ?? new List<int>();
        }
        private List<KpiOption> BuildKpiOptionsForSectionByFundId(int moduleId, List<KpiModel> kpiModels, List<DataRow> dataRows)
        {
            var fundIds = GetDistinctFundIds(dataRows);
            return (
                     from k in kpiModels
                     where fundIds.Contains(k.FundId) && k.ModuleId == moduleId
                     select new KpiOption
                     {
                         KpiId = k.KpiId,
                         Text = k.KpiName,
                         Value = k.KpiName,
                         KpiInfo = k.KpiInfo,
                         MappingId = k.MappingKPIId.ToString(),
                         MappingName = k.KpiName,
                         MethodologyId = k.MethodologyId
                     }).OrderBy(x => x.KpiId).DistinctBy(x => x.KpiId).ToList();

        }
        private async Task FetchCurrenciesByFundCompanyId(int moduleId, List<DataRow> dataRows, List<int> companyIds)
        {
            List<CurrencyModel> currencyModels = [];
            int fundId = FindFundId(dataRows);
            if (moduleId == (int)KpiModuleType.FundFinancials)
            {
                currencyModels = await _dapper.Query<CurrencyModel>(
                    SqlConstants.QueryByGetFundCurrencies,
                    new { fundId }
                );
            }
            else
            {

                currencyModels = await _dapper.Query<CurrencyModel>(
                    SqlConstants.QueryByGetCompanyCurrencies,
                    new { @companyIds = string.Join(",", companyIds) }
                );
            }
        }
        private void ProcessCustomSections(List<CustomSection> customSections, List<ModuleDetails> queriedModuleDetails, List<KpiModel> kpiModels, List<KpiModel> fundFinancialsKpis, List<SubPageFields> staticSectionFields)
        {
            foreach (var collection in customSections)
            {
                var section = queriedModuleDetails.FirstOrDefault(x => x.PageConfigFieldName == collection.SectionType);
                if (string.Equals(collection.SectionType, ApiConstants.StaticInfo, StringComparison.OrdinalIgnoreCase))
                    BuildKpiOptionsForMasterSection(staticSectionFields, collection.Options);
                if (section is null)
                    continue;
                switch (section.Type)
                {
                    case ApiConstants.Company:
                        AssignKpisToSectionData(collection.Data, collection.Periods, kpiModels, section.ModuleId);
                        collection.Options = BuildKpiOptionsForSectionByCompanyId(section.ModuleId, kpiModels, collection.Data);
                        break;
                    case ApiConstants.Fund:
                        AssignKpisToSectionData(collection.Data, collection.Periods, fundFinancialsKpis, section.ModuleId);
                        collection.Options = BuildKpiOptionsForSectionByFundId(section.ModuleId, fundFinancialsKpis, collection.Data);
                        break;
                }
            }
        }
    }
}

