﻿namespace Persistence.Models
{
    public class PortfolioCompanyDetails : BaseEntity
    {
        [Key]
        public int PortfolioCompanyId { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public string? Website { get; set; }
        public string? BussinessDescription { get; set; }
        public int? SectorId { get; set; }
        public int? SubSectorId { get; set; }
        public int ReportingCurrencyId { get; set; }
        public string? Status { get; set; }
        public string? StockExchange_Ticker { get; set; }
        public int? HeadquarterId { get; set; }
        public bool IsActive { get; set; }
        public string? EncryptedPortfolioCompanyId { get; set; }
        public string? FinancialYearEnd { get; set; }
        public string? ImagePath { get; set; }
        public string? MasterCompanyName { get; set; }
        public int? GroupId { get; set; }
        public string? CompanyLegalName { get; set; }
        public DateTime? PCInvestmentDate { get; set; }

    }
}
