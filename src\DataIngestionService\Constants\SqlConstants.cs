﻿namespace DataIngestionService.Constants
{
    public static class SqlConstants
    {
        public static readonly string QueryByGetAllKpis = "Exec GetAllMappedKpi @companyIds=@companyIds,@moduleIds=@moduleIds";
        public static readonly string QueryByGetPageConfigStaticFields = "SELECT FieldID FieldId,Name ,AliasName,SubPageID SubPageId,SequenceNo,DataTypeId,IsCustom FROM M_SubPageFields WHERE isDeleted=0 AND isActive=1 AND SubPageID=@SubPageId";
        public static readonly string QueryByGetFundKpis = "SELECT mfsk.FundId, mfsk.KpiID AS KpiId, msk.KPI AS KpiName, mfsk.ParentKpiId AS ParentKpiId, mfsk.DisplayOrder, CAST(1 AS BIT) AS IsMapped, msk.IsHeader, msk.IsBoldKPI, mfsk.MappingFundSectionKpiId AS MappingKPIId, msk.KpiInfo, mfsk.IsExtraction, msk.Synonym, mfsk.Definition, msk.MethodologyID, msk.ModuleId FROM MappingFundSectionKpi mfsk INNER JOIN MFundSectionKpi msk ON mfsk.KpiID = msk.FundSectionKpiId WHERE mfsk.IsExtraction = 1 AND mfsk.FundId = @fundId AND mfsk.IsDeleted = 0 AND msk.IsDeleted = 0";
        public static readonly string QueryByGetFundCurrencies = " SELECT Pc.FundID AS FundId, PCCurrency.CurrencyCode FROM FundDetails Pc LEFT JOIN M_Currency PCCurrency ON PCCurrency.CurrencyID = Pc.CurrencyID AND PCCurrency.IsDeleted = 0 WHERE Pc.IsDeleted = 0 AND Pc.FundID=@fundId";
        public static readonly string QueryByGetCompanyCurrencies = "SELECT Pc.PortfolioCompanyID AS PortfolioCompanyId, PCCurrency.CurrencyCode FROM PortfolioCompanyDetails Pc LEFT JOIN M_Currency PCCurrency ON PCCurrency.CurrencyID = Pc.ReportingCurrencyID AND PCCurrency.IsDeleted = 0 WHERE Pc.IsDeleted = 0 AND Pc.PortfolioCompanyID IN( SELECT value FROM STRING_SPLIT(@companyIds, ','))";
        public static readonly string QueryByGetModuleDetails = "SELECT ModuleId,PageConfigFieldName,Type FROM(SELECT ModuleId,PageConfigFieldName,'Fund' Type FROM MFundKpiModules WHERE PageConfigFieldName<>'' AND IsDeleted=0 UNION ALL SELECT ModuleId,PageConfigFieldName,'Company' Type FROM M_KpiModules WHERE PageConfigFieldName<>'' AND IsDeleted=0)as Module WHERE Module.PageConfigFieldName IN( SELECT value FROM STRING_SPLIT(@module, ','))";
    }
}
