﻿using Infrastructure.DTOS.NonControllerDto;

namespace Infrastructure.DTOS.Master
{
    public class DSFinancialsDto
    {
        public bool isError { get; set; }
        public string timestamp { get; set; }
        public string message { get; set; }
        public string version { get; set; }
        public string job_id { get; set; }
        public string company_id { get; set; }
        public string company_name { get; set; }

        public string country { get; set; }
        public string sector { get; set; }
        public string industry { get; set; }
        public string ticker { get; set; }
        public Guid? template_id { get; set; }
        public string currency_unit { get; set; }
        public List<DSFileDetails> files { get; set; }
        public List<DSTableGroup> table_groups { get; set; }
        public bool isPublished { get; set; }
        public string output_mode { get; set; }
        public string output_s3_path { get; set; }
    }
}

