using Amazon.S3.Model;
using Infrastructure.Contract;
using Infrastructure.Contract.BlobStorage;

namespace DataIngestionService.IServices
{
    public interface IFileService
    {
        /// <summary>
        /// Downloads a file from S3 storage
        /// </summary>
        /// <param name="query">The query containing the S3 key of the file to download</param>
        /// <returns>A response containing the file contents and filename</returns>
        Task<BlobStorageResponse> GetDocumentFromBlobStorage(BlobStorageRequest query);
        Task<bool> DeleteFile(DeleteDocument deleteDocument, int userId);
        Task<BlobStorageResponse> GetDocumentsByProcessId(Guid processId);
        Task<GetObjectResponse?> DownloadFileObject(string keyPath);
    }
}