using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Persistence.Models;
using Persistence.Models.Classifier;
using Persistence.MongoDb;
using Persistence.UnitOfWork;
using System.Linq.Expressions;
using Xunit;

namespace UnitTests.Services
{
    public class MongoDbServiceTests
    {
        private readonly Mock<IRepositoryFactory> _mockRepositoryFactory;
        private readonly Mock<IRepository<IssuerModel>> _mockIssuerRepository;
        private readonly Mock<IRepository<Classifier>> _mockClassifierRepository;
        private readonly Mock<IRepository<FundKpiModel>> _mockFundKpiRepository;
        private readonly Mock<ILogger<MongoDbService>> _mockLogger;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly MongoDbService _service;

        public MongoDbServiceTests()
        {
            _mockRepositoryFactory = new Mock<IRepositoryFactory>();
            _mockIssuerRepository = new Mock<IRepository<IssuerModel>>();
            _mockClassifierRepository = new Mock<IRepository<Classifier>>();
            _mockFundKpiRepository = new Mock<IRepository<FundKpiModel>>();
            _mockLogger = new Mock<ILogger<MongoDbService>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();

            _mockRepositoryFactory.Setup(x => x.GetRepository<IssuerModel>()).Returns(_mockIssuerRepository.Object);
            _mockRepositoryFactory.Setup(x => x.GetRepository<Classifier>()).Returns(_mockClassifierRepository.Object);
            _mockRepositoryFactory.Setup(x => x.GetRepository<FundKpiModel>()).Returns(_mockFundKpiRepository.Object);
            
            _service = new MongoDbService(_mockRepositoryFactory.Object, _mockLogger.Object);
        }

        [Fact]
        public async Task AddIssuerDetails_ShouldCreateIssuerDetails_AssertInsteadOfVerify()
        {

            var request = new UploadDto
            {
                CompanyIssuers = JsonConvert.SerializeObject(new List<IssuerKPIModel> { new IssuerKPIModel { Id = 1, FieldId = 2, Text = "TestIssuer" } }),
                FundId = 1,
                ProcessId = Guid.NewGuid(),
                ExtractionType = "test" // Set required property as string
            };
            int userId = 123;
            bool createCalled = false;
            IssuerModel? createdModel = null;
            _mockIssuerRepository.Setup(x => x.CreateAsync(It.IsAny<IssuerModel>())).Callback<IssuerModel>(m => { createCalled = true; createdModel = m; }).Returns(Task.CompletedTask);


            var result = await _service.AddIssuerDetails(request, userId);


            Assert.True(string.IsNullOrEmpty(result));
        }

        [Fact]
        public async Task AddSubPageDetails_ShouldCreateFundKpiDetails_AssertInsteadOfVerify()
        {

            var request = new UploadDto
            {
                FundKpiDetails = JsonConvert.SerializeObject(new List<SubPageFieldsModel> { new SubPageFieldsModel { FieldId = 1, SubPageId = 2, Name = "TestField", AliasName = "Alias", IsCustom = false, DataTypeId = 0 } }),
                ProcessId = Guid.NewGuid(),
                ExtractionType = "test" // Set required property as string
            };
            int userId = 456;
            bool createCalled = false;
            FundKpiModel? createdModel = null;
            _mockFundKpiRepository.Setup(x => x.CreateAsync(It.IsAny<FundKpiModel>())).Callback<FundKpiModel>(m => { createCalled = true; createdModel = m; }).Returns(Task.CompletedTask);


            var result = await _service.AddSubPageDetails(request, userId);


            Assert.True(string.IsNullOrEmpty(result));
        }

        [Fact]
        public async Task GetClassifiersByProcessId_ShouldReturnFalseIfNoClassifiers()
        {

            var processIds = new List<Guid> { Guid.NewGuid() };
            _mockClassifierRepository.Setup(x => x.GetAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Classifier, bool>>>()))
                .ReturnsAsync(new List<Classifier>());


            var result = await _service.GetClassifiersByProcessId(processIds);


            Assert.False(result);
        }

        [Fact]
        public async Task GetClassifiersByProcessId_ShouldReturnTrueIfAllComplete()
        {

            var processIds = new List<Guid> { Guid.NewGuid() };
            var classifiers = new List<Classifier> { new Classifier { TableSuggestionResponse = new(), IsDeleted = false, ProcessId = processIds[0] } };
            _mockClassifierRepository.Setup(x => x.GetAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Classifier, bool>>>()))
                .ReturnsAsync(classifiers);


            var result = await _service.GetClassifiersByProcessId(processIds);


            Assert.True(result);
        }
        [Fact]
        public async Task GetFundKpiDetails_ReturnsEmptyList_WhenNoFundKpiDetailsFound()
        {

            var processId = Guid.NewGuid();
            var mockFundKpiRepo = new Mock<IRepository<FundKpiModel>>();
            var mockLogger = new Mock<ILogger<MongoDbService>>();
            var mockRepositoryFactory = new Mock<IRepositoryFactory>();
            var mockUnitOfWork = new Mock<IUnitOfWork>();

            // Setup repository factory to return the mocked repository
            mockRepositoryFactory.Setup(f => f.GetRepository<FundKpiModel>()).Returns(mockFundKpiRepo.Object);
            mockRepositoryFactory.Setup(f => f.GetRepository<IssuerModel>()).Returns(new Mock<IRepository<IssuerModel>>().Object);
            mockRepositoryFactory.Setup(f => f.GetRepository<Classifier>()).Returns(new Mock<IRepository<Classifier>>().Object);
           
            // Setup repository to return empty list
            mockFundKpiRepo.Setup(r => r.GetAllAsync(It.IsAny<Expression<Func<FundKpiModel, bool>>>()))
                .ReturnsAsync(new List<FundKpiModel>());

            var service = new MongoDbService(mockRepositoryFactory.Object, mockLogger.Object);


            var result = await service.GetFundKpiDetails(processId);


            Assert.NotNull(result);
            Assert.Empty(result);
        }
        [Fact]
        public async Task AddSubPageDetails_ReturnsEmptyString_WhenFundKpiDetailsIsNullOrEmpty()
        {

            var request = new UploadDto
            {
                CompanyIssuers = null,
                ProcessId = Guid.NewGuid(),
                ExtractionType = "test" // Set required property as string
            };
            int userId = 456;
            FundKpiModel? createdModel = null;
            _mockFundKpiRepository.Setup(x => x.CreateAsync(It.IsAny<FundKpiModel>())).Callback<FundKpiModel>(m => { createdModel = m; }).Returns(Task.CompletedTask);

            var result = await _service.AddSubPageDetails(request, userId);


            Assert.True(string.IsNullOrEmpty(result));
        }
        [Fact]
        public async Task GetIssuerDetails_ReturnsEmptyList_WhenNoIssuerDetailsFound()
        {

            var processId = Guid.NewGuid();
            var mockIssuerRepo = new Mock<IRepository<IssuerModel>>();
            var mockLogger = new Mock<ILogger<MongoDbService>>();
            var mockRepositoryFactory = new Mock<IRepositoryFactory>();
            var mockUnitOfWork = new Mock<IUnitOfWork>();

            // Setup repository factory to return the mocked repository
            mockRepositoryFactory.Setup(f => f.GetRepository<IssuerModel>()).Returns(mockIssuerRepo.Object);
            mockRepositoryFactory.Setup(f => f.GetRepository<Classifier>()).Returns(new Mock<IRepository<Classifier>>().Object);
            mockRepositoryFactory.Setup(f => f.GetRepository<FundKpiModel>()).Returns(new Mock<IRepository<FundKpiModel>>().Object);
           
            // Setup repository to return empty list
            mockIssuerRepo.Setup(r => r.GetAllAsync(It.IsAny<Expression<Func<IssuerModel, bool>>>()))
                .ReturnsAsync(new List<IssuerModel>());

            var service = new MongoDbService(mockRepositoryFactory.Object, mockLogger.Object);


            var result = await service.GetIssuerDetails(processId);


            Assert.NotNull(result);
            Assert.Empty(result);
        }
        [Fact]
        public async Task GetIssuerDetails_ShouldReturnIssuerKPIModels_AssertInsteadOfVerify()
        {

            var processId = Guid.NewGuid();
            var issuerList = new List<IssuerModel> { new IssuerModel { IssuerKPIModel = new List<IssuerKPIModel> { new IssuerKPIModel { Id = 1, FieldId = 2, Text = "Issuer1" } }, ProcessId = processId } };
            _mockIssuerRepository.Setup(x => x.GetAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<IssuerModel, bool>>>())).ReturnsAsync(issuerList);


            var result = await _service.GetIssuerDetails(processId);


            Assert.Single(result);
            Assert.Equal("Issuer1", result[0].Text);
        }

        [Fact]
        public async Task GetFundKpiDetails_ShouldReturnSubPageFieldsModels_AssertInsteadOfVerify()
        {

            var processId = Guid.NewGuid();
            var fundKpiList = new List<FundKpiModel> { new FundKpiModel { SubPageFieldsModel = new List<SubPageFieldsModel> { new SubPageFieldsModel { FieldId = 1, SubPageId = 2, Name = "Field1", AliasName = "Alias", IsCustom = false, DataTypeId = 0 } }, ProcessId = processId } };
            _mockFundKpiRepository.Setup(x => x.GetAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<FundKpiModel, bool>>>())).ReturnsAsync(fundKpiList);


            var result = await _service.GetFundKpiDetails(processId);


            Assert.Single(result);
            Assert.Equal("Field1", result[0].Name);
        }

        [Fact]
        public async Task GetClassifiersByProcessId_ShouldReturnFalseIfAnyClassifierIncomplete()
        {
            var processIds = new List<Guid> { Guid.NewGuid() };
            var classifiers = new List<Classifier>
                {
                    new() { TableSuggestionResponse = null!, IsDeleted = false, ProcessId = processIds[0] }, // Incomplete
                    new() { TableSuggestionResponse = new TableSuggestionResponse(), IsDeleted = false, ProcessId = processIds[0] } // Complete
                };
            _mockClassifierRepository
                .Setup(x => x.GetAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Classifier, bool>>>()))
                .ReturnsAsync(classifiers);
            var result = await _service.GetClassifiersByProcessId(processIds);
            Assert.False(result);
        }
    }
}
