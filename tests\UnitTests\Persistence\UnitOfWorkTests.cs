using Microsoft.EntityFrameworkCore;
using Moq;
using Persistence.DBEntitiesContext;
using Persistence.UnitOfWork;
using Xunit;

namespace UnitTests.Persistence
{
    public class UnitOfWorkTests
    {
        [Fact]
        public void UnitOfWork_AutoDetectChangesEnabled_ShouldSetTrackingBehaviorCorrectly()
        {
            // Arrange
            var options = new DbContextOptionsBuilder<DBEntities>()
                .UseSqlite("Data Source=:memory:")
                .Options;
            var context = new DBEntities(options);
            context.Database.OpenConnection();
            context.Database.EnsureCreated();
            var unitOfWork = new UnitOfWork(context);

            // Act
            unitOfWork.AutoDetectChangesEnabled = true;

            // Assert
            Assert.True(unitOfWork.AutoDetectChangesEnabled);
            Assert.Equal(QueryTrackingBehavior.TrackAll, context.ChangeTracker.QueryTrackingBehavior);
            
            context.Database.CloseConnection();
        }

        [Fact]
        public void UnitOfWork_AutoDetectChangesDisabled_ShouldSetNoTrackingBehavior()
        {
            // Arrange
            var options = new DbContextOptionsBuilder<DBEntities>()
                .UseSqlite("Data Source=:memory:")
                .Options;
            var context = new DBEntities(options);
            context.Database.OpenConnection();
            context.Database.EnsureCreated();
            var unitOfWork = new UnitOfWork(context);

            // Act
            unitOfWork.AutoDetectChangesEnabled = false;

            // Assert
            Assert.False(unitOfWork.AutoDetectChangesEnabled);
            Assert.Equal(QueryTrackingBehavior.NoTracking, context.ChangeTracker.QueryTrackingBehavior);
            
            context.Database.CloseConnection();
        }

        [Fact]
        public void UnitOfWork_Repositories_ShouldInitializeLazily()
        {
            // Arrange
            var options = new DbContextOptionsBuilder<DBEntities>()
                .UseSqlite("Data Source=:memory:")  // Use SQLite for in-memory testing
                .Options;
            var context = new DBEntities(options);
            context.Database.OpenConnection();
            context.Database.EnsureCreated();
            var unitOfWork = new UnitOfWork(context);

            // Act & Assert
            Assert.NotNull(unitOfWork.DataIngestionDocumentRepository);
            Assert.NotNull(unitOfWork.JobsRepository);
            Assert.NotNull(unitOfWork.StatusRepository);
            Assert.NotNull(unitOfWork.DIMappingDocumentsDetailsRepository);
            Assert.NotNull(unitOfWork.PortfoiloCompanyDetailsRepository);
            Assert.NotNull(unitOfWork.UserDetailsRepository);
            Assert.NotNull(unitOfWork.FundDetailsRepository);

            // Verify same instance is returned on subsequent calls
            Assert.Same(unitOfWork.DataIngestionDocumentRepository, unitOfWork.DataIngestionDocumentRepository);
            
            context.Database.CloseConnection();
        }

        [Fact]
        public void UnitOfWork_Dispose_ShouldNotThrow()
        {
            // Arrange
            var options = new DbContextOptionsBuilder<DBEntities>()
                .UseSqlite("Data Source=:memory:")
                .Options;
            var context = new DBEntities(options);
            context.Database.OpenConnection();
            context.Database.EnsureCreated();
            var unitOfWork = new UnitOfWork(context);

            // Act & Assert
            var exception = Record.Exception(() => unitOfWork.Dispose());
            Assert.Null(exception);
        }
    }
}