using DataIngestionService.Constants;
using DataIngestionService.Helpers;
using DataIngestionService.IServices;
using Infrastructure.Contract;
using Microsoft.Extensions.Logging;
using Notification.Models;
using Notification.Services;
using Persistence.Models;
using Persistence.UnitOfWork;

namespace DataIngestionService.Services
{
    public class JobsUpdateService(ILogger<JobsUpdateService> logger, IUnitOfWork unitOfWork, IExtract extract, ICacheService cacheService, NotificationSender notificationSender) : IJobsUpdateService
    {
        private readonly ILogger<JobsUpdateService> _logger = logger;
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private readonly IExtract _extract = extract;
        private readonly ICacheService _cacheService = cacheService;
        private readonly NotificationSender _notificationSender= notificationSender;
        private const string STATUS_CACHE_KEY = "AllStatuses";

        /// <summary>
        /// Gets the status entity that represents "in progress" status
        /// </summary>
        /// <returns>The in progress status entity</returns>      
        public async Task<List<Status>> GetStatus()
        {
            return await _cacheService.GetOrSetAsync(STATUS_CACHE_KEY,
                async () => await _unitOfWork.StatusRepository.FindAllAsync(x => !x.IsDeleted),
                TimeSpan.FromDays(1));
        }

        /// <summary>
        /// Retrieves all jobs that are currently in progress
        /// </summary>
        /// <returns>A collection of jobs with "in progress" status</returns>
        private async Task<List<Jobs>> GetJobs()
        {
            return await _unitOfWork.JobsRepository.FindAllAsync(x => !x.IsDeleted);
        }
        public async Task<List<DIMappingDocumentsDetails>> GetExtractionTypes(List<Guid> processIds)
        {
            return await _unitOfWork.DIMappingDocumentsDetailsRepository.FindAllAsync(x => !x.IsDeleted && processIds.Contains(x.ProcessId));
        }

        public async Task<bool> UpdateJobsStatus(string jwtToken, CancellationToken cancellationToken = default)
        {
            try
            {
                var statuses = await GetStatus();
                statuses = statuses.Where(x => ExtractHelper.GetAllExtractionStatuses().Contains(x.State)).ToList();
                var statusProgress = statuses.Find(x => x.State == ApiConstants.ExtractionInProgress && x.Name.Contains(ApiConstants.STATUS_IN_PROGRESS, StringComparison.CurrentCultureIgnoreCase));
                var inProgressJobs = await GetJobs();
                inProgressJobs = [.. inProgressJobs.Where(x => x.StatusId == statusProgress?.Id)];
                if (!inProgressJobs.Any())
                {
                    _logger.LogInformation("No in-progress jobs found to update");
                    return true;
                }
                var extractionTypes = await GetExtractionTypes([.. inProgressJobs.Select(x=>x.ProcessId)]);
                List<Jobs> jobsToUpdate = [];
                foreach (var job in inProgressJobs)
                {
                    var responseStatus = await _extract.CheckJobStatusAsync(job.JobId, jwtToken, cancellationToken);
                    if (responseStatus.Status.ToLower() == ApiConstants.STATUS_RUNNING)
                    {
                        responseStatus.Status = ApiConstants.STATUS_IN_PROGRESS;
                    }
                    var statusResponse = statuses.FirstOrDefault(x => x.Name.ToLower().Contains(responseStatus.Status.ToLower()));
                    if (responseStatus.Status.ToLower() == ApiConstants.STATUS_COMPLETED.ToLower())
                    {
                        string extractionType = extractionTypes.Find(x => x.ProcessId == job.ProcessId)?.ExtractionType ?? string.Empty;
                        var responseExtraction = extractionType == ApiConstants.SpecificKPI ?
                            await _extract.FetchSpecificExtractionData(job.JobId,job.ProcessId , jwtToken, cancellationToken) :
                            await _extract.FetchExtractionDataAsync(job.JobId, job.ProcessId, jwtToken, cancellationToken);
                        if (responseExtraction.IsSuccessStatusCode)
                        {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                            UpdateJob(job, statuses.FirstOrDefault(x => x.State == ApiConstants.ExtractionCompleted).Id);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                            jobsToUpdate.Add(job);
                        }
                        else if (!responseExtraction.IsSuccessStatusCode)
                        {
#pragma warning disable CS8602 // Dereference of a possibly null reference.
                            UpdateJob(job, statuses.FirstOrDefault(x => x.State == ApiConstants.ExtractionFailed).Id);
#pragma warning restore CS8602 // Dereference of a possibly null reference.
                            jobsToUpdate.Add(job);
                        }
                    }
                    else if (statusResponse?.Name == ApiConstants.STATUS_FAILED)
                    {
                        UpdateJob(job, statusResponse.Id);
                        jobsToUpdate.Add(job);
                    }
                }
                if (jobsToUpdate.Count > 0)
                {
                    _unitOfWork.JobsRepository.UpdateBulk(jobsToUpdate);
                    await _unitOfWork.SaveAsync();
                    await SendNotification(jobsToUpdate);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating jobs");
                throw;
            }
        }

        private static void UpdateJob(Jobs job, Guid statusId)
        {
            job.StatusId = statusId;
            job.ModifiedOn = DateTime.UtcNow;
        }

        public async Task<bool> JobExists(Guid processId)
        {
            return await _unitOfWork.JobsRepository.ExistsAsyncAny(x => x.ProcessId == processId && !x.IsDeleted);
        }

        public async Task<StatusResponse> UpdateJob(CreateJobs jobRequest, int userId)
        {
            var existingJob = await _unitOfWork.JobsRepository.GetFirstOrDefaultAsync(
            x => x.ProcessId == jobRequest.ProcessId &&
                 !x.IsDeleted);
            if (existingJob == null)
            {
                _logger.LogWarning("Job not found for update: JobId={JobId}, ProcessId={ProcessId}",
                    jobRequest.JobId, jobRequest.ProcessId);
                throw new KeyNotFoundException($"Job with ID {jobRequest.JobId} and ProcessId {jobRequest.ProcessId} not found");
            }
            existingJob.StatusId = jobRequest.StatusId;
            existingJob.JobId = jobRequest.JobId;
            existingJob.ModifiedOn = DateTime.UtcNow;
            existingJob.ModifiedBy = userId;
            _unitOfWork.JobsRepository.Update(existingJob);
            await _unitOfWork.SaveAsync();

            _logger.LogInformation("Job updated successfully: JobId={JobId}, ProcessId={ProcessId}",
                existingJob.JobId, existingJob.ProcessId);
            return new StatusResponse
            {
                IsSuccess = true,
                Message = "Job updated successfully",
                Id = existingJob.Id
            };
        }

        public async Task<StatusResponse> CreateJob(CreateJobs jobs, int userId)
        {
            try
            {
                if (await JobExists(jobs.ProcessId))
                {
                    return await UpdateJob(jobs, userId);
                }
                var job = new Jobs
                {
                    StatusId = jobs.StatusId,
                    ProcessId = jobs.ProcessId,
                    TenantId = jobs.TenantId,
                    ParentJobId = jobs.ParentJobId,
                    JobId = jobs.JobId,
                    CreatedOn = DateTime.UtcNow,
                    CreatedBy = userId,
                    IsDeleted = false
                };
                await _unitOfWork.JobsRepository.AddAsyn(job);
                await _unitOfWork.SaveAsync();
                return new StatusResponse
                {
                    IsSuccess = true,
                    Message = "Job created successfully",
                    Id = job.Id
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating job");
                throw;
            }
        }

        public async Task<StatusResponse> UpdateJobStatus(StatusUpdateModel statusUpdate)
        {
            try
            {
                var statuses = await GetStatus();
                var existingJob = await _unitOfWork.JobsRepository.GetFirstOrDefaultAsync(
                    x => x.ProcessId == Guid.Parse(statusUpdate.ProcessId) && !x.IsDeleted);

                if (existingJob == null)
                {
                    _logger.LogWarning("Job not found for update: ProcessId={ProcessId}", statusUpdate.ProcessId);
                    throw new KeyNotFoundException($"Job with ProcessId {statusUpdate.ProcessId} not found");
                }
                var dataIngestionStatuses = ExtractHelper.GetAllDataIngestionStatuses();
                var matchingStatus = statuses.Find(x => dataIngestionStatuses.Contains(x.State) && x.Name == statusUpdate.Status);

                if (matchingStatus == null)
                {
                    _logger.LogWarning("No matching status found for update: Status={Status}", statusUpdate.Status);
                    throw new InvalidOperationException($"No matching status found for {statusUpdate.Status}");
                }
                existingJob.StatusId = matchingStatus.Id;
                existingJob.ModifiedOn = DateTime.UtcNow;
                existingJob.ModifiedBy = 3;
                _unitOfWork.JobsRepository.Update(existingJob);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Job updated successfully: JobId={JobId}, ProcessId={ProcessId}",
                    existingJob.JobId, existingJob.ProcessId);

                return new StatusResponse
                {
                    IsSuccess = true,
                    Message = "Status updated successfully",
                    Id = existingJob.Id
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating job status for ProcessId={ProcessId}", statusUpdate.ProcessId);
                throw;
            }
        }
        public async Task SendNotification(List<Jobs> jobs)
        {
            var notification = new NotificationMessage
            {
                Type = NotificationType.Info,
                Title = "Notification",
                Message = "Job updated successfully",
                Timestamp = DateTime.UtcNow,
                Data= [.. jobs.Select(job=>new JobStatus { JobId= job.JobId,StatusId= job.StatusId})]
            };
            await _notificationSender.SendToAllAsync(notification);
        }
    }
}