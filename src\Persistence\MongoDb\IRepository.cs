﻿using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Persistence.MongoDb
{
    public interface IRepository<T> where T : class
    {
        // Retrieves all documents from the collection
        Task<IEnumerable<T>> GetAllAsync();
        // Retrieves all documents from the collection with a filter
        Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>> filter);
        // Retrieves a single document by its ID
        Task<T?> GetByIdAsync(string id);
        // Inserts a new document into the collection
        Task CreateAsync(T entity);
        // Updates an existing document by its ID
        Task UpdateAsync(string id, T entity);
        // Deletes a document by its ID
        Task DeleteAsync(string id);
        Task BulkInsertAsync(IEnumerable<T> entities);
        Task BulkUpdateAsync(IDictionary<string, T> entitiesWithIds);
        Task<T?> FindOneAsync(Expression<Func<T, bool>> filter);
    }
}
