using AutoMapper;
using DapperRepository;
using DataIngestionService.Helpers;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.DTOS.NonControllerDto;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.Models;
using Persistence.MongoDb;
using Persistence.UnitOfWork;
using System.Linq.Expressions;
using System.Text.Json;
using Xunit;
using SpecificKpiDocument = Persistence.Models.Specific.SpecificKpiDocument;
using Persistence.Models.Specific;
using Infrastructure.DTOS.Master;
using Infrastructure.Contract;

namespace UnitTests
{
    public class SpecificKpiTransformationServiceTests
    {
        private readonly Mock<IRepository<SpecificKpiDocument>> _specificKpiRepositoryMock;
        private readonly Mock<ILogger<SpecificKpiTransformationService>> _loggerMock;
        private readonly Mock<IWebHostEnvironment> _hostingEnvironmentMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IDapperGenericRepository> _dapperMock;
        private readonly Mock<IUnitOfWork> _unitOfWorkMock;
        private readonly SpecificKpiTransformationService _service;

        public SpecificKpiTransformationServiceTests()
        {
            _specificKpiRepositoryMock = new Mock<IRepository<SpecificKpiDocument>>();
            _loggerMock = new Mock<ILogger<SpecificKpiTransformationService>>();
            _hostingEnvironmentMock = new Mock<IWebHostEnvironment>();
            _mapperMock = new Mock<IMapper>();
            _dapperMock = new Mock<IDapperGenericRepository>();
            _unitOfWorkMock = new Mock<IUnitOfWork>();
            var config = new MapperConfiguration(cfg => {
                cfg.AddProfile(new MappingProfile());
            });
            var mapper = config.CreateMapper();
            var repositoryFactoryMock = new Mock<IRepositoryFactory>();
            repositoryFactoryMock.Setup(r => r.GetRepository<SpecificKpiDocument>())
                .Returns(_specificKpiRepositoryMock.Object);

            _service = new SpecificKpiTransformationService(
                repositoryFactoryMock.Object,
                _loggerMock.Object,
                _hostingEnvironmentMock.Object,
                mapper,
                _dapperMock.Object,
                _unitOfWorkMock.Object
            );
        }

        [Fact]
        public async Task TransformDsSpecificToSpecific_ValidJson_ReturnsSpecificDto()
        {
            string folderName = Path.Combine("Mocks");
            string webRootPath = Path.Combine(Directory.GetCurrentDirectory(), folderName);
            string fileNameWithPath = Path.Combine(webRootPath, "DsSpecific.json");
            string dsSpecificJson = System.IO.File.ReadAllText(fileNameWithPath);
            var dsSpecificDto = JsonSerializer.Deserialize<DsSpecificDto>(dsSpecificJson);
            var processId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.DIMappingDocumentsDetailsRepository.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>()))
                .ReturnsAsync(new DIMappingDocumentsDetails { FundId = 1 });

            _specificKpiRepositoryMock.Setup(r => r.CreateAsync(It.IsAny<SpecificKpiDocument>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _service.TransformDsSpecificToSpecific(dsSpecificJson, processId);
            Assert.IsType<SpecificDto>(result);
        }

        [Fact]
        public async Task TransformDsSpecificToSpecific_InvalidJson_ThrowsArgumentException()
        {
            // Arrange
            var invalidJson = "{ invalid json }";
            var processId = Guid.NewGuid();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(
                () => _service.TransformDsSpecificToSpecific(invalidJson, processId));
            
            Assert.Contains("Invalid JSON format for ProcessId", exception.Message);
        }

        [Fact]
        public async Task TransformDsSpecificToSpecific_NullJson_ThrowsInvalidOperationException()
        {
            // Arrange
            var processId = Guid.NewGuid();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.TransformDsSpecificToSpecific(null, processId));
            
            Assert.Contains("Error transforming DsSpecific to Specific", exception.Message);
        }

        [Fact]
        public async Task SaveSpecificKpiDocument_ValidDto_ReturnsDocumentId()
        {
            // Arrange
            var specificDto = new SpecificDto { CompanyId = "123" };
            var processId = Guid.NewGuid();
            var userId = 1;
            var expectedDocumentId = "507f1f77bcf86cd799439011";

            var mappedDocument = new SpecificKpiDocument { Id = expectedDocumentId, CompanyId = "123" };
            _mapperMock.Setup(m => m.Map<SpecificKpiDocument>(specificDto)).Returns(mappedDocument);
            _specificKpiRepositoryMock.Setup(r => r.CreateAsync(It.IsAny<SpecificKpiDocument>()))
                .Returns(Task.CompletedTask)
                .Callback<SpecificKpiDocument>(doc => doc.Id = expectedDocumentId);

            // Act
            var result = await _service.SaveSpecificKpiDocument(specificDto, processId, userId);

            // Assert
            Assert.Equal(expectedDocumentId, result);
            _specificKpiRepositoryMock.Verify(r => r.CreateAsync(It.Is<SpecificKpiDocument>(d => 
                d.ProcessId == processId && d.CreatedBy == userId && !d.IsDeleted)), Times.Once);
        }

        [Fact]
        public async Task SaveSpecificKpiDocument_NullDto_ThrowsInvalidOperationException()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var userId = 1;

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.SaveSpecificKpiDocument(null, processId, userId));
            
            Assert.Equal("Transformation failed", exception.Message);
        }

        [Fact]
        public async Task UpdateSpecificKpiDocument_ValidDto_ReturnsDocumentId()
        {
            // Arrange
            var specificDto = new SpecificDto { CompanyId = "123" };
            var processId = Guid.NewGuid();
            var userId = 1;
            var existingDocument = new SpecificKpiDocument { Id = "507f1f77bcf86cd799439011", ProcessId = processId };
            var updatedDocument = new SpecificKpiDocument { Id = "507f1f77bcf86cd799439011" };

            _specificKpiRepositoryMock.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDocument, bool>>>()))
                .ReturnsAsync(existingDocument);
            _mapperMock.Setup(m => m.Map<SpecificKpiDocument>(specificDto)).Returns(updatedDocument);
            _specificKpiRepositoryMock.Setup(r => r.UpdateAsync(It.IsAny<string>(), It.IsAny<SpecificKpiDocument>()))
                .Returns(Task.CompletedTask);

            // Act
            var result = await _service.UpdateSpecificKpiDocument(specificDto, processId, userId);

            // Assert
            Assert.Equal(existingDocument.Id, result);
            _specificKpiRepositoryMock.Verify(r => r.UpdateAsync(existingDocument.Id, It.Is<SpecificKpiDocument>(d => 
                d.ProcessId == processId && d.ModifiedBy == userId)), Times.Once);
        }

        [Fact]
        public async Task UpdateSpecificKpiDocument_DocumentNotFound_ReturnsEmptyString()
        {
            // Arrange
            var specificDto = new SpecificDto { CompanyId = "123" };
            var processId = Guid.NewGuid();
            var userId = 1;

            _specificKpiRepositoryMock.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDocument, bool>>>()))
                .ReturnsAsync((SpecificKpiDocument?)null);

            // Act
            var result = await _service.UpdateSpecificKpiDocument(specificDto, processId, userId);

            // Assert
            Assert.Equal(string.Empty, result);
            _specificKpiRepositoryMock.Verify(r => r.UpdateAsync(It.IsAny<string>(), It.IsAny<SpecificKpiDocument>()), Times.Never);
        }

        [Fact]
        public async Task UpdateSpecificKpiDocument_NullDto_ThrowsInvalidOperationException()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var userId = 1;

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(
                () => _service.UpdateSpecificKpiDocument(null, processId, userId));
            
            Assert.Equal("Transformation failed", exception.Message);
        }

        [Fact]
        public async Task GetSpecificKpiDocumentByProcessId_DocumentExists_ReturnsSpecificDto()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var document = new SpecificKpiDocument { Id = "507f1f77bcf86cd799439011", ProcessId = processId, CompanyId = "123" };
            var expectedDto = new SpecificDto { CompanyId = "123" };

            _specificKpiRepositoryMock.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDocument, bool>>>()))
                .ReturnsAsync(document);
            _mapperMock.Setup(m => m.Map<SpecificDto>(It.IsAny<SpecificKpiDocument>())).Returns(expectedDto);

            // Mock the Dapper calls that ProcessAllSectionsAndAssignKpis might make
            _dapperMock.Setup(d => d.Query<global::Infrastructure.Contract.ModuleDetails>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new List<global::Infrastructure.Contract.ModuleDetails>());
            _dapperMock.Setup(d => d.Query<global::Infrastructure.Contract.KpiModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new List<global::Infrastructure.Contract.KpiModel>());
            _dapperMock.Setup(d => d.Query<global::Infrastructure.Contract.SubPageFields>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new List<global::Infrastructure.Contract.SubPageFields>());

            // Act
            var result = await _service.GetSpecificKpiDocumentByProcessId(processId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedDto.CompanyId, result.CompanyId);
        }

        [Fact]
        public async Task GetSpecificKpiDocumentByProcessId_DocumentNotFound_ReturnsEmptyDto()
        {
            // Arrange
            var processId = Guid.NewGuid();

            _specificKpiRepositoryMock.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDocument, bool>>>()))
                .ReturnsAsync((SpecificKpiDocument?)null);

            // Act
            var result = await _service.GetSpecificKpiDocumentByProcessId(processId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<SpecificDto>(result);
        }

        [Fact]
        public async Task GetFundIdByProcessId_DocumentExists_ReturnsFundId()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var expectedFundId = 123;
            var mappingDocument = new DIMappingDocumentsDetails { FundId = expectedFundId };

            _unitOfWorkMock.Setup(u => u.DIMappingDocumentsDetailsRepository.GetFirstOrDefaultAsync(
                It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>()))
                .ReturnsAsync(mappingDocument);

            // Act
            var result = await _service.GetFundIdByProcessId(processId);

            // Assert
            Assert.Equal(expectedFundId.ToString(), result);
        }

        [Fact]
        public async Task GetFundIdByProcessId_DocumentNotFound_ReturnsEmptyString()
        {
            // Arrange
            var processId = Guid.NewGuid();

            _unitOfWorkMock.Setup(u => u.DIMappingDocumentsDetailsRepository.GetFirstOrDefaultAsync(
                It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>()))
                .ReturnsAsync((DIMappingDocumentsDetails?)null);

            // Act
            var result = await _service.GetFundIdByProcessId(processId);

            // Assert
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public void AssignKpisToSectionData_WithValidData_AssignsKpisCorrectly()
        {
            // Arrange
            var dataRows = new List<DataRow>
            {
                new DataRow { CompanyId = "123" },
                new DataRow { CompanyId = "456" }
            };
            var periodConfigurations = new List<PeriodConfiguration>
            {
                new PeriodConfiguration 
                { 
                    PeriodId = "1",
                    DocumentKpis = new List<SelectedKpi> 
                    { 
                        new SelectedKpi { KpiId = 1, PeriodId = "1" },
                        new SelectedKpi { KpiId = 2, PeriodId = "1" }
                    },
                    SelectedKpis = new List<SelectedKpi>()
                }
            };
            var kpiModels = new List<global::Infrastructure.Contract.KpiModel>
            {
                new global::Infrastructure.Contract.KpiModel { KpiId = 1, KpiName = "Test KPI 1", PortfolioCompanyId = 123, ModuleId = 1 },
                new global::Infrastructure.Contract.KpiModel { KpiId = 2, KpiName = "Test KPI 2", PortfolioCompanyId = 123, ModuleId = 1 }
            };

            _dapperMock.Setup(d => d.Query<global::Infrastructure.Contract.CurrencyModel>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(new List<global::Infrastructure.Contract.CurrencyModel>());

            // Act
            _service.AssignKpisToSectionData(dataRows, periodConfigurations, kpiModels, 1);

            // Assert
            Assert.Equal(2, periodConfigurations[0].SelectedKpis.Count);
            Assert.Equal(2, periodConfigurations[0].Columns);
        }

        [Fact]
        public void AssignKpisToSectionData_WithNullDataRows_DoesNotAssignKpis()
        {
            // Arrange
            var periodConfigurations = new List<PeriodConfiguration>();
            var kpiModels = new List<global::Infrastructure.Contract.KpiModel>();

            // Act
            _service.AssignKpisToSectionData(null, periodConfigurations, kpiModels, 1);

            // Assert - No exception should be thrown
            Assert.Empty(periodConfigurations);
        }

        [Fact]
        public void AssignKpisToSectionData_WithEmptyDataRows_DoesNotAssignKpis()
        {
            // Arrange
            var dataRows = new List<DataRow>();
            var periodConfigurations = new List<PeriodConfiguration>();
            var kpiModels = new List<global::Infrastructure.Contract.KpiModel>();

            // Act
            _service.AssignKpisToSectionData(dataRows, periodConfigurations, kpiModels, 1);

            // Assert - No exception should be thrown
            Assert.Empty(periodConfigurations);
        }

        [Fact]
        public void AssignStaticSectionData_WithValidData_AssignsCorrectly()
        {
            // Arrange
            var dataRows = new List<DataRow>
            {
                new DataRow { CompanyId = "123" }
            };
            var periodConfigurations = new List<PeriodConfiguration>
            {
                new PeriodConfiguration 
                { 
                    PeriodId = "1",
                    DocumentKpis = new List<SelectedKpi> 
                    { 
                        new SelectedKpi { KpiId = 1, PeriodId = "1" }
                    },
                    SelectedKpis = new List<SelectedKpi>()
                }
            };
            var subPageFields = new List<global::Infrastructure.Contract.SubPageFields>
            {
                new global::Infrastructure.Contract.SubPageFields { FieldId = 1, AliasName = "Test Field" }
            };

            // Act
            _service.AssignStaticSectionData(dataRows, periodConfigurations, subPageFields);

            // Assert
            Assert.Single(periodConfigurations[0].SelectedKpis);
            Assert.Equal("Test Field", periodConfigurations[0].SelectedKpis[0].Text);
            Assert.Equal(1, periodConfigurations[0].Columns);
        }

        [Fact]
        public void AssignStaticSectionData_WithNullDataRows_DoesNotThrow()
        {
            // Arrange
            var periodConfigurations = new List<PeriodConfiguration>();
            var subPageFields = new List<global::Infrastructure.Contract.SubPageFields>();

            // Act & Assert - No exception should be thrown
            _service.AssignStaticSectionData(null, periodConfigurations, subPageFields);
        }

        [Fact]
        public async Task ProcessAllSectionsAndAssignKpis_WithNullDocument_DoesNotThrow()
        {
            // Act & Assert - No exception should be thrown
            await _service.ProcessAllSectionsAndAssignKpis(null);
        }
    }
}
