using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class SubPageFieldsModelTests
    {
        [Fact]
        public void SubPageFieldsModel_DefaultInitialization_ShouldHaveDefaultValues()
        {
            // Act
            var model = new SubPageFieldsModel();

            // Assert
            Assert.Equal(0, model.FieldId);
            Assert.Equal(0, model.SubPageId);
            Assert.Null(model.Name);
            Assert.Null(model.AliasName);
            Assert.False(model.IsCustom);
            Assert.Equal(0, model.DataTypeId);
        }

        [Fact]
        public void SubPageFieldsModel_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var model = new SubPageFieldsModel
            {
                FieldId = 101,
                SubPageId = 202,
                Name = "Revenue Field",
                AliasName = "Total Revenue",
                IsCustom = true,
                DataTypeId = 5
            };

            // Assert
            Assert.Equal(101, model.FieldId);
            Assert.Equal(202, model.SubPageId);
            Assert.Equal("Revenue Field", model.Name);
            Assert.Equal("Total Revenue", model.AliasName);
            Assert.True(model.IsCustom);
            Assert.Equal(5, model.DataTypeId);
        }

        [Fact]
        public void SubPageFieldsModel_WithEmptyStrings_ShouldAcceptEmptyValues()
        {
            // Act
            var model = new SubPageFieldsModel
            {
                Name = string.Empty,
                AliasName = string.Empty
            };

            // Assert
            Assert.Equal(string.Empty, model.Name);
            Assert.Equal(string.Empty, model.AliasName);
        }

        [Fact]
        public void SubPageFieldsModel_WithNegativeIds_ShouldAcceptNegativeValues()
        {
            // Act
            var model = new SubPageFieldsModel
            {
                FieldId = -1,
                SubPageId = -2,
                DataTypeId = -3
            };

            // Assert
            Assert.Equal(-1, model.FieldId);
            Assert.Equal(-2, model.SubPageId);
            Assert.Equal(-3, model.DataTypeId);
        }

        [Fact]
        public void SubPageFieldsModel_WithLargeIds_ShouldAcceptLargeValues()
        {
            // Act
            var model = new SubPageFieldsModel
            {
                FieldId = 999999,
                SubPageId = 888888,
                DataTypeId = 777777
            };

            // Assert
            Assert.Equal(999999, model.FieldId);
            Assert.Equal(888888, model.SubPageId);
            Assert.Equal(777777, model.DataTypeId);
        }

        [Fact]
        public void SubPageFieldsModel_WithCustomFlag_ShouldToggleCorrectly()
        {
            // Act
            var model1 = new SubPageFieldsModel { IsCustom = true };
            var model2 = new SubPageFieldsModel { IsCustom = false };

            // Assert
            Assert.True(model1.IsCustom);
            Assert.False(model2.IsCustom);
        }

        [Fact]
        public void SubPageFieldsModel_WithLongNames_ShouldAcceptLongStrings()
        {
            // Arrange
            var longName = new string('N', 500);
            var longAlias = new string('A', 500);

            // Act
            var model = new SubPageFieldsModel
            {
                Name = longName,
                AliasName = longAlias
            };

            // Assert
            Assert.Equal(longName, model.Name);
            Assert.Equal(longAlias, model.AliasName);
        }

        [Fact]
        public void SubPageFieldsModel_WithSameNameAndAlias_ShouldAllowSameValues()
        {
            // Act
            var model = new SubPageFieldsModel
            {
                Name = "SameName",
                AliasName = "SameName"
            };

            // Assert
            Assert.Equal("SameName", model.Name);
            Assert.Equal("SameName", model.AliasName);
        }

        [Fact]
        public void SubPageFieldsModel_WithZeroIds_ShouldAcceptZeroValues()
        {
            // Act
            var model = new SubPageFieldsModel
            {
                FieldId = 0,
                SubPageId = 0,
                DataTypeId = 0
            };

            // Assert
            Assert.Equal(0, model.FieldId);
            Assert.Equal(0, model.SubPageId);
            Assert.Equal(0, model.DataTypeId);
        }
    }
}