﻿using Contract;
using Infrastructure.Contract;

namespace DataIngestionService
{
    public interface IUpload
    {
        Task<List<UploadResponseDto>> UploadFile(UploadDto request,int userId);
        Task<IEnumerable<DocumentDetailDto>> GetUploadedDocumentDetails(int limit = 10, int offset = 0);
        Task<ProcessDetailsDto> GetProcessDetailsById(Guid processId);
        Task<StatusCount> GetDocumentProcessingStatusCount();
        Task<bool> UpdateDocumentConfigurations(List<FileConfigurationDetails> request, int userId);
    }
}
