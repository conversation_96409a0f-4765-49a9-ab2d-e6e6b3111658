namespace Persistence.Models;

public class DataIngestionDocuments:BaseEntity
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    public Guid ProcessId { get; set; }

    [Required]
    public string S3Path { get; set; }

    [Required]
    public string FileName { get; set; }

    [Required]
    public string Type { get; set; }

    [Required]
    public Guid TenantId { get; set; }
    [Required]
    public string Extension {  get; set; }
    public string? Errors { get; set; }
    public int? DocumentTypeId { get; set; }
    public string? PeriodType { get; set; }
    public int? Year { get; set; }
    public string? Month { get; set; }
    public string? Quarter { get; set; }
}
