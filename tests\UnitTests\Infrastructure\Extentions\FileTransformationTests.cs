using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Enum;
using Infrastructure.Extentions;
using Xunit;

namespace UnitTests.Infrastructure.Extentions
{
    public class FileTransformationTests
    {
        [Fact]
        public void ToFinancialsData_ShouldTransformDSFinancialsDtoCorrectly()
        {
            // Arrange
            var financialsDto = new DSFinancialsDto
            {
                job_id = "testJobId",
                company_id = "123",
                company_name = "Test Company",
                files = new List<DSFileDetails>
                {
                    new DSFileDetails
                    {
                        s3_path = "s3://test/file.pdf",
                        file_name = "test_file.pdf",
                        page_count = 10
                    }
                },
                table_groups = new List<DSTableGroup>
                {
                    new DSTableGroup
                    {
                        label = "Income Statement",
                        tables = new List<DSTable>()
                    }
                },
                currency_unit = "USD",
                template_id = Guid.NewGuid(),
                ticker = "TEST",
                country = "USA",
                sector = "Technology",
                industry = "Software",
                isPublished = true
            };

            // Act
            var result = financialsDto.ToFinancialsData();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(financialsDto.job_id, result.Id);
            Assert.Equal(financialsDto.company_id, result.CompanyId);
            Assert.Equal(financialsDto.company_name, result.CompanyName);
            Assert.Equal(Extraction.AsReported, result.TypeofExtraction);
            Assert.NotNull(result.Files);
            Assert.Single(result.Files);
            Assert.Equal(financialsDto.currency_unit, result.CurrencyUnit);
            Assert.Equal(financialsDto.template_id, result.TemplateId);
            Assert.Equal(financialsDto.ticker, result.Ticker);
            Assert.Equal(financialsDto.country, result.Country);
            Assert.Equal(financialsDto.sector, result.Sector);
            Assert.Equal(financialsDto.industry, result.Industry);
        }

        [Fact]
        public void ToA1File_ShouldTransformDSFileDetailsCorrectly()
        {
            // Arrange
            var fileDetails = new DSFileDetails
            {
                s3_path = "s3://test/path/file.pdf",
                file_name = "test_file.pdf",
                page_count = 5
            };

            // Act
            var result = fileDetails.ToA1File();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(fileDetails.s3_path, result.Url);
            Assert.Equal(fileDetails.file_name, result.FileName);
            Assert.Equal(fileDetails.page_count, result.PageCount);
        }

        [Fact]
        public void ToA1TableGroup_ShouldTransformDSTableGroupCorrectly()
        {
            // Arrange
            var tableGroup = new DSTableGroup
            {
                label = "Balance Sheet",
                tables = new List<DSTable>()
            };

            // Act
            var result = tableGroup.ToA1TableGroup();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(tableGroup.label, result.Label);
            Assert.NotNull(result.Tables);
        }

        [Fact]
        public void GetUniqueId_ShouldReturnValidHash()
        {
            // Arrange
            string input = "test string";

            // Act
            string result = input.GetUniqueId();

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            Assert.True(result.Length > 0);
            
            // Test idempotency
            string result2 = input.GetUniqueId();
            Assert.Equal(result, result2);
        }
    }
}
