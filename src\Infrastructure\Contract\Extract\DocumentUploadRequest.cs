using System.Text.Json.Serialization;

namespace Infrastructure.Contract.Extract
{
    public class DocumentUploadRequest
    {
        [JsonPropertyName("files")]
        public List<DocumentFile> Files { get; set; } = new();
    }

    public class DocumentFile
    {
        [JsonPropertyName("file_name")]
        public string FileName { get; set; } = string.Empty;

        [JsonPropertyName("s3_path")]
        public string S3Path { get; set; } = string.Empty;

        [JsonPropertyName("source")]
        public string Source { get; set; } = "upload";

        [JsonPropertyName("file_id")]
        public Guid FileId { get; set; }
    }
}