﻿using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Enum;
using System.Security.Cryptography;
using System.Text;

namespace Infrastructure.Extentions
{
    public static class Tranformations
    {
        public static FinancialsData ToFinancialsData(this DSFinancialsDto financialsDto)
        {
            return new FinancialsData
            {
                Id = financialsDto.job_id,
                CompanyId = financialsDto.company_id,
                CompanyName = financialsDto.company_name,
                TypeofExtraction = Extraction.AsReported, // Assuming a default value
                Files = financialsDto.files.Select(f => f.ToA1File()).ToList(),
                TableGroups = financialsDto.table_groups.Select(t => t.ToA1TableGroup()).ToList(),
                CurrencyUnit = financialsDto.currency_unit,
                TemplateId = financialsDto.template_id,
                Ticker = financialsDto.ticker,
                Country = financialsDto.country,
                Sector = financialsDto.sector,
                Industry = financialsDto.industry,
                IsPublished = false,
                CurrencyCode = null,
                CurrencyId = 0,
            };
        }
        public static A1File ToA1File(this DSFileDetails fileDetails)
        {
            return new A1File
            {
                Url = fileDetails.s3_path,
                FileName = fileDetails.file_name,
                PageCount = fileDetails.page_count
            };
        }

        public static A1TableGroup ToA1TableGroup(this DSTableGroup tableGroup)
        {
            return new A1TableGroup
            {
                Label = tableGroup.label,
                Tables = tableGroup.tables.Select(t => t.ToA1Table()).ToList()
            };
        }

        public static A1Table ToA1Table(this DSTable table)
        {
            return new A1Table
            {
                Id = table.id,
                Name = table.name,
                Columns = table.sections.ToA1SectionColumns(),
                Rows = table.sections.ToA1SectionRows(),

            };
        }        

        public static List<A1TableColumn> ToA1SectionColumns(this List<DSSection> sections)
        {
            List<A1TableColumn> columns = [];
            sections.ForEach(s => s.rows.ForEach(c => columns.AddRange(c.ToA1Column(s))));
            return columns.Where(x => !string.IsNullOrEmpty(x.ColumnKey)).GroupBy(c => c.ColumnKey).Select(g => g.First()).ToList();
        }

        public static List<A1TableColumn> ToA1Column(this DSRow row, DSSection section)
        {
            return row.cells.Select(c => c.ToA1Column()).ToList();
        }
        public static string GetUniqueId(this string value)
        {
            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(value ?? ""));
                // Take first 16 bytes to maintain similar length to previous MD5 hash
                byte[] truncatedHash = new byte[16];
                Array.Copy(hash, truncatedHash, 16);
                Guid result = new Guid(truncatedHash);
                return "uid" + result.ToString().Replace("-", "");
            }
        }

        public static A1TableColumn ToA1Column(this DSCell cell)
        {
            return new A1TableColumn
            {
                ColumnKey = cell.column_index.GetUniqueId(),
                DsKey = cell.column_key.GetUniqueId(),
                MonthEnding = cell.column_key.DecodeColumnKey(3),
                PeriodDate = cell.column_key.DecodeColumnKey(2),
                PeriodInfo = cell.column_key.DecodeColumnKey(0),
                FilingType = StatementType.Original,
                ReportingPeriod = cell.column_key.DecodeColumnKey(3).GetReportingPeriod(),
            };
        }

        public static ReportingPeriod GetReportingPeriod(this string value)
        {
            return value switch
            {
                "12-months" => ReportingPeriod.Annual,
                "9-months" => ReportingPeriod.NineMonth,
                "6-months" => ReportingPeriod.HalfYearly,
                "3-months" => ReportingPeriod.Quarter,
                _ => ReportingPeriod.Unknown
            };
        }

        public static string DecodeColumnKey(this string value, int index)
        {
            string[] splits = value.Split('|');
            return splits.Length > index ? splits[index] : null;
        }

        public static List<A1TableRow> ToA1SectionRows(this List<DSSection> sections)
        {
            List<A1TableRow> rows = [];
            sections.ForEach(s => rows.AddRange(s.rows.Select(r => r.ToA1Row(s))));
            return rows;
        }

        public static A1TableRow ToA1Row(this DSRow row, DSSection section)
        {
            return new A1TableRow
            {
                Label = row.label.ToA1Label(),
                Cells = row.cells.Select(c => c.ToA1Cell()).ToList()
            };
        }

        public static A1RowLabel ToA1Label(this DSLabel label)
        {
            return new A1RowLabel
            {
                Text = label.text,
                Style = label.style,
                Id = label.defined_name,
                Mapping = label.mapping,
                MappingId = label.mapping_id,
                MappingScore = label.mapping_score
            };
        }

        public static A1Cell ToA1Cell(this DSCell cell)
        {
            return new A1Cell
            {
                ColumnKey = cell.column_index.GetUniqueId(),
                DsKey = cell.column_key.GetUniqueId(),
                Comments = cell.comment.texts,
                PdfHighlight = cell.ToA1PdfHighlight(),
                Source = cell.file_id,
                Value = cell.value.value,
                Format = cell.value.format,
                Type = cell.value.type,
                Date = cell.value.date,
            };
        }

        public static A1PdfHighlight ToA1PdfHighlight(this DSCell cell)
        {
            return new A1PdfHighlight
            {
                PageNumber = cell.page,
                Bounds = cell.bbox.ToA1Bounds(),
                Text = cell.value.value,
                PageHeight = cell.bbox.ph,
                PageWidth = cell.bbox.pw,

            };
        }

        public static List<float> ToA1Bounds(this DSBbox bbox)
        {
            return
            [
                bbox.x1,
                bbox.y1,
                bbox.x2,
                bbox.y2,
            ];
        }
    }
}
