﻿
namespace Persistence.Models
{
    public partial class Financials : BaseEntity // Declaring the Financials class which inherits from BaseEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Financials"/> class.
        /// </summary>
        public Financials() // Constructor for the Financials class
        {
        }

        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        [Key] // Data annotation to specify the primary key
        public Guid Id { get; set; } // Property to get or set the unique identifier for the Financials

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        [MaxLength(100)] // Data annotation to specify the maximum length of the name
        public string Name { get; set; } // Property to get or set the name of the Financials
        public Guid ProcessID { get; set; }
        public Guid JobID { get; set; }
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public int TypeofExtraction { get; set; }
        public List<TableGroup> TableGroups { get; set; } //Tabs
        public List<File> Files { get; set; }

        public string Ticker { get; set; }
        public Guid? TemplateId { get; set; }
        public string CurrencyUnit { get; set; }
        public string Country { get; set; }
        public string Sector { get; set; }
        public string Industry { get; set; }
        public bool? IsPublished { get; set; }
        public string? CurrencyCode { get; set; }
        public int? CurrencyId { get; set; }
    }
}
