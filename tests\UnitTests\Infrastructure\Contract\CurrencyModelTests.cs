using Infrastructure.Contract;
using Xunit;

namespace UnitTests.Infrastructure.Contract
{
    public class CurrencyModelTests
    {
        [Fact]
        public void CurrencyModel_DefaultValues_ShouldBeSetCorrectly()
        {
            // Act
            var currencyModel = new CurrencyModel();

            // Assert
            Assert.Equal(string.Empty, currencyModel.CurrencyCode);
            Assert.Equal(0, currencyModel.PortfolioCompanyId);
            Assert.Equal(0, currencyModel.FundId);
        }

        [Fact]
        public void CurrencyModel_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var currencyModel = new CurrencyModel
            {
                CurrencyCode = "USD",
                PortfolioCompanyId = 12345,
                FundId = 67890
            };

            // Assert
            Assert.Equal("USD", currencyModel.CurrencyCode);
            Assert.Equal(12345, currencyModel.PortfolioCompanyId);
            Assert.Equal(67890, currencyModel.FundId);
        }

        [Fact]
        public void CurrencyModel_WithNegativeIds_ShouldAcceptNegativeValues()
        {
            // Act
            var currencyModel = new CurrencyModel
            {
                CurrencyCode = "EUR",
                PortfolioCompanyId = -1,
                FundId = -999
            };

            // Assert
            Assert.Equal("EUR", currencyModel.CurrencyCode);
            Assert.Equal(-1, currencyModel.PortfolioCompanyId);
            Assert.Equal(-999, currencyModel.FundId);
        }

        [Fact]
        public void CurrencyModel_WithNullCurrencyCode_ShouldAcceptNull()
        {
            // Act
            var currencyModel = new CurrencyModel
            {
                CurrencyCode = null!
            };

            // Assert
            Assert.Null(currencyModel.CurrencyCode);
        }
    }
}