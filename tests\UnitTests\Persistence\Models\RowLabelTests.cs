using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class RowLabelTests
    {
        [Fact]
        public void RowLabel_DefaultInitialization_ShouldHaveDefaultValues()
        {
            // Act
            var rowLabel = new RowLabel();

            // Assert
            Assert.Null(rowLabel.Text);
            Assert.False(rowLabel.IsBold);
            Assert.False(rowLabel.IsEmptyRow);
            Assert.Null(rowLabel.Style);
            Assert.Null(rowLabel.Id);
            Assert.Null(rowLabel.Mapping);
            Assert.Null(rowLabel.MappingId);
            Assert.Null(rowLabel.MappingScore);
        }

        [Fact]
        public void RowLabel_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var rowLabel = new RowLabel
            {
                Text = "Revenue Line Item",
                IsBold = true,
                IsEmptyRow = false,
                Style = "header-style",
                Id = "row-001",
                Mapping = "revenue-mapping",
                MappingId = 123,
                MappingScore = 0.95f
            };

            // Assert
            Assert.Equal("Revenue Line Item", rowLabel.Text);
            Assert.True(rowLabel.IsBold);
            Assert.False(rowLabel.IsEmptyRow);
            Assert.Equal("header-style", rowLabel.Style);
            Assert.Equal("row-001", rowLabel.Id);
            Assert.Equal("revenue-mapping", rowLabel.Mapping);
            Assert.Equal(123, rowLabel.MappingId);
            Assert.Equal(0.95f, rowLabel.MappingScore);
        }

        [Fact]
        public void RowLabel_WithEmptyRow_ShouldAcceptEmptyRowFlag()
        {
            // Act
            var rowLabel = new RowLabel
            {
                IsEmptyRow = true,
                IsBold = false
            };

            // Assert
            Assert.True(rowLabel.IsEmptyRow);
            Assert.False(rowLabel.IsBold);
        }

        [Fact]
        public void RowLabel_WithEmptyStrings_ShouldAcceptEmptyValues()
        {
            // Act
            var rowLabel = new RowLabel
            {
                Text = string.Empty,
                Style = string.Empty,
                Id = string.Empty,
                Mapping = string.Empty
            };

            // Assert
            Assert.Equal(string.Empty, rowLabel.Text);
            Assert.Equal(string.Empty, rowLabel.Style);
            Assert.Equal(string.Empty, rowLabel.Id);
            Assert.Equal(string.Empty, rowLabel.Mapping);
        }

        [Fact]
        public void RowLabel_WithNullMappingValues_ShouldAcceptNulls()
        {
            // Act
            var rowLabel = new RowLabel
            {
                MappingId = null,
                MappingScore = null
            };

            // Assert
            Assert.Null(rowLabel.MappingId);
            Assert.Null(rowLabel.MappingScore);
        }

        [Fact]
        public void RowLabel_WithNegativeMappingId_ShouldAcceptNegativeValues()
        {
            // Act
            var rowLabel = new RowLabel { MappingId = -1 };

            // Assert
            Assert.Equal(-1, rowLabel.MappingId);
        }

        [Fact]
        public void RowLabel_WithZeroMappingScore_ShouldAcceptZero()
        {
            // Act
            var rowLabel = new RowLabel { MappingScore = 0.0f };

            // Assert
            Assert.Equal(0.0f, rowLabel.MappingScore);
        }

        [Fact]
        public void RowLabel_WithHighMappingScore_ShouldAcceptValuesAboveOne()
        {
            // Act
            var rowLabel = new RowLabel { MappingScore = 1.5f };

            // Assert
            Assert.Equal(1.5f, rowLabel.MappingScore);
        }

        [Fact]
        public void RowLabel_WithLongText_ShouldAcceptLongStrings()
        {
            // Arrange
            var longText = new string('A', 1000);

            // Act
            var rowLabel = new RowLabel { Text = longText };

            // Assert
            Assert.Equal(longText, rowLabel.Text);
        }
    }
}