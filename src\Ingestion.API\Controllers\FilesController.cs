using DataIngestionService.IServices;
using Infrastructure.Contract;
using Infrastructure.Contract.BlobStorage;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Amazon.S3.Model;

namespace API.Controllers
{
    /// <summary>
    /// Controller for handling file operations such as downloading and deleting files from blob storage
    /// </summary>
    [ApiController]
    [Route("api")]
    [Authorize(Policy = JwtBearerDefaults.AuthenticationScheme)]
    public class FilesController(
        IFileService downloadFileService,IHelperService helperService,
        ILogger<FilesController> logger) : ControllerBase
    {
        private readonly IFileService _downloadFileService = downloadFileService;
        private readonly ILogger<FilesController> _logger = logger;
        private readonly IHelperService _helperService = helperService;

        /// <summary>
        /// Downloads a file from blob storage using POST method
        /// </summary>
        /// <param name="request">The request containing the file key</param>
        /// <returns>The file content and metadata as a BlobStorageResponse</returns>
        [HttpPost("download-file")]
        public async Task<ActionResult<BlobStorageResponse>> GetDocumentFromBlobStorage(BlobStorageRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            try
            {
                return Ok(await _downloadFileService.GetDocumentFromBlobStorage(request));
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "Error downloading file with key {Key}: {Message}", request.Key, exception.Message);
                return StatusCode(500, new { message = "Error occurred retrieving the file" });
            }
        }

        /// <summary>
        /// Downloads a file directly from S3 storage using GET method with a query string parameter
        /// </summary>
        /// <param name="fileKey">The S3 key of the file to download</param>
        /// <returns>The file as a stream with the appropriate content type</returns>
        [HttpGet("download-file")]
        [AllowAnonymous]
        public async Task<ActionResult<BlobStorageResponse>> DownloadFileByKey([FromQuery] string fileKey)
        {
            if (string.IsNullOrEmpty(fileKey))
            {
                return BadRequest("File key is required");
            }
            
            try
            {
                GetObjectResponse response = await _downloadFileService.DownloadFileObject(fileKey);
                if (response == null)
                {
                    return NotFound($"File with key '{fileKey}' was not found");
                }
                return File(response.ResponseStream, response.Headers.ContentType);
            }
            catch (Exception exception)
            {
                _logger.LogError(exception, "Error downloading file with key {Key}: {Message}", fileKey, exception.Message);
                return StatusCode(500, new { message = "Error occurred retrieving the file" });
            }
        }

        /// <summary>
        /// Deletes a file from blob storage
        /// </summary>
        /// <param name="request">The request containing the file ID and key to delete</param>
        /// <returns>Success or failure status</returns>
        [HttpPost("delete-file")]
        public async Task<ActionResult> DeleteFile(DeleteDocument request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            int userId = await _helperService.GetCurrentUserId(User);
            return Ok(await _downloadFileService.DeleteFile(request, userId));
        }

        /// <summary>
        /// Downloads a file associated with a specific process ID
        /// </summary>
        /// <param name="processId">The process ID associated with the file</param>
        /// <returns>The file content and metadata as a BlobStorageResponse</returns>
        [HttpPost("download-file/{processId}")]
        public async Task<ActionResult<BlobStorageResponse>> GetDocumentsByProcessId(Guid processId)
        {
            return Ok(await _downloadFileService.GetDocumentsByProcessId(processId));
        }
    }
}
