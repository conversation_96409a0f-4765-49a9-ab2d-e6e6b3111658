using Dapper;
using DapperRepository;
using Moq;
using Persistence.DapperRepository;
using System.Data;
using Xunit;

public class DapperGenericRepositoryTests : IDisposable
{
    private readonly Mock<IDbConnectionFactory> _mockFactory;
    private readonly Mock<IDbConnection> _mockConnection;
    private readonly DapperGenericRepository _repository;

    public DapperGenericRepositoryTests()
    {
        _mockFactory = new Mock<IDbConnectionFactory>();
        _mockConnection = new Mock<IDbConnection>();
        _mockFactory.Setup(f => f.CreateConnection()).Returns(_mockConnection.Object);
        _repository = new DapperGenericRepository(_mockFactory.Object);
    }

    [Fact]
    public void GetConnectionString_ReturnsConnectionString()
    {
        _mockConnection.Setup(c => c.ConnectionString).Returns("TestConnectionString");
        var value = _repository.GetConnectionString();
        Assert.Equal("TestConnectionString", value);
    }

    [Fact]
    public void Dispose_DisposesConnection()
    {
        _repository.Dispose();
        _mockConnection.Verify(c => c.Dispose(), Times.Once);
    }

    public void Dispose()
    {
        _repository.Dispose();
    }
} 