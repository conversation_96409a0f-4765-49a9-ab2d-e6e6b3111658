﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Persistence.Models
{
    public partial class DbAudit : BaseEntity // Declaring the DbAudit class which inherits from BaseEntity
    {
        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public Guid Id { get; set; } // Property to store the unique identifier for the audit record

        /// <summary>
        /// Gets or sets the audit datetime UTC.
        /// </summary>
        /// <value>
        /// The audit datetime UTC.
        /// </value>
        public DateTime AuditDatetimeUtc { get; set; } // Property to store the date and time of the audit in UTC

        /// <summary>
        /// Gets or sets the type of the audit.
        /// </summary>
        /// <value>
        /// The type of the audit.
        /// </value>
        public string AuditType { get; set; } // Property to store the type of the audit

        /// <summary>
        /// Gets or sets the audit user.
        /// </summary>
        /// <value>
        /// The audit user.
        /// </value>
        public string AuditUser { get; set; } // Property to store the user who performed the audit

        /// <summary>
        /// Gets or sets the name of the table.
        /// </summary>
        /// <value>
        /// The name of the table.
        /// </value>
        public string TableName { get; set; } // Property to store the name of the table that was audited

        /// <summary>
        /// Gets or sets the key values.
        /// </summary>
        /// <value>
        /// The key values.
        /// </value>
        public string KeyValues { get; set; } // Property to store the key values of the audited record

        /// <summary>
        /// Gets or sets the old values.
        /// </summary>
        /// <value>
        /// The old values.
        /// </value>
        public string OldValues { get; set; } // Property to store the old values before the audit

        /// <summary>
        /// Gets or sets the new values.
        /// </summary>
        /// <value>
        /// The new values.
        /// </value>
        public string NewValues { get; set; } // Property to store the new values after the audit

        /// <summary>
        /// Gets or sets the changed columns.
        /// </summary>
        /// <value>
        /// The changed columns.
        /// </value>
        public string ChangedColumns { get; set; } // Property to store the columns that were changed during the audit
    }
}
