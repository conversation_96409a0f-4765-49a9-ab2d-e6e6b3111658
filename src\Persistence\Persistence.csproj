﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
		<PackageReference Include="MongoDB.Driver" Version="3.2.1" />
		<PackageReference Include="MongoDB.Bson" Version="3.2.1" />
		<PackageReference Include="System.Composition" Version="8.0.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
		<PackageReference Include="System.Runtime.Caching" Version="8.0.1" />
		<PackageReference Include="System.Text.Json" Version="8.0.5" />
		<PackageReference Include="Dapper" Version="2.1.35" />
	</ItemGroup>
</Project>
