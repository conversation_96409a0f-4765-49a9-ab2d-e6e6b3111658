using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class TableColumnTests
    {
        [Fact]
        public void TableColumn_DefaultInitialization_ShouldHaveNullValues()
        {
            // Act
            var column = new TableColumn();

            // Assert
            Assert.Null(column.ColumnKey);
            Assert.Null(column.Title);
            Assert.Equal(0, column.FilingType);
            Assert.Equal(0, column.ReportingPeriod);
            Assert.Null(column.MonthEnding);
            Assert.Null(column.PeriodDate);
            Assert.Null(column.PeriodInfo);
            Assert.Null(column.ColumnIndex);
        }

        [Fact]
        public void TableColumn_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var column = new TableColumn
            {
                ColumnKey = "Revenue",
                Title = "Total Revenue",
                FilingType = 1,
                ReportingPeriod = 2023,
                MonthEnding = "December",
                PeriodDate = "2023-12-31",
                PeriodInfo = "Q4 2023",
                ColumnIndex = "A1"
            };

            // Assert
            Assert.Equal("Revenue", column.ColumnKey);
            Assert.Equal("Total Revenue", column.Title);
            Assert.Equal(1, column.FilingType);
            Assert.Equal(2023, column.ReportingPeriod);
            Assert.Equal("December", column.MonthEnding);
            Assert.Equal("2023-12-31", column.PeriodDate);
            Assert.Equal("Q4 2023", column.PeriodInfo);
            Assert.Equal("A1", column.ColumnIndex);
        }

        [Fact]
        public void TableColumn_WithEmptyStrings_ShouldAcceptEmptyValues()
        {
            // Act
            var column = new TableColumn
            {
                ColumnKey = string.Empty,
                Title = string.Empty,
                MonthEnding = string.Empty,
                PeriodDate = string.Empty,
                PeriodInfo = string.Empty,
                ColumnIndex = string.Empty
            };

            // Assert
            Assert.Equal(string.Empty, column.ColumnKey);
            Assert.Equal(string.Empty, column.Title);
            Assert.Equal(string.Empty, column.MonthEnding);
            Assert.Equal(string.Empty, column.PeriodDate);
            Assert.Equal(string.Empty, column.PeriodInfo);
            Assert.Equal(string.Empty, column.ColumnIndex);
        }

        [Fact]
        public void TableColumn_WithNegativeNumbers_ShouldAcceptNegativeValues()
        {
            // Act
            var column = new TableColumn
            {
                FilingType = -1,
                ReportingPeriod = -2023
            };

            // Assert
            Assert.Equal(-1, column.FilingType);
            Assert.Equal(-2023, column.ReportingPeriod);
        }

        [Fact]
        public void TableColumn_WithLargeNumbers_ShouldAcceptLargeValues()
        {
            // Act
            var column = new TableColumn
            {
                FilingType = 999999,
                ReportingPeriod = 2050
            };

            // Assert
            Assert.Equal(999999, column.FilingType);
            Assert.Equal(2050, column.ReportingPeriod);
        }
    }
}