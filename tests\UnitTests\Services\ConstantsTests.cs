using Xunit;
using DataIngestionService.Constants;

namespace UnitTests.Services
{
    public class ConstantsTests
    {
        [Fact]
        public void ApiConstants_Values_Are_Correct()
        {
            Assert.Equal("ApiSettings:BaseApiUrl", ApiConstants.API_SETTINGS_BASE_URL_KEY);
            Assert.Equal("ApiSettings:ApiKey", ApiConstants.API_SETTINGS_API_KEY_KEY);
            Assert.Equal("/extract", ApiConstants.EXTRACT_ENDPOINT);
            Assert.Equal("/extract/upload", ApiConstants.EXTRACT_UPLOAD_ENDPOINT);
            Assert.Equal("/extract/{0}/data/status", ApiConstants.JOB_STATUS_ENDPOINT_FORMAT);
            Assert.Equal("/extract/{0}/data", ApiConstants.EXTRACTION_DATA_ENDPOINT_FORMAT);
            Assert.Equal("Authorization", ApiConstants.AUTHORIZATION_HEADER);
            Assert.Equal("Bearer", ApiConstants.BEARER_PREFIX);
            Assert.Equal("accept", ApiConstants.ACCEPT_HEADER);
            Assert.Equal("*/*", ApiConstants.ACCEPT_ALL);
            Assert.Equal("application/json", ApiConstants.APPLICATION_JSON);
            Assert.Equal("x-api-key", ApiConstants.API_KEY_HEADER);
            Assert.Equal("app-name", ApiConstants.APP_KEY_NAME);
            Assert.Equal("foliosure", ApiConstants.APP_Name);
            Assert.Equal("ERROR", ApiConstants.ERROR_STATUS);
            Assert.Equal("error", ApiConstants.ERROR_STATUS_LOWERCASE);
            Assert.Equal("Failed to deserialize response", ApiConstants.DESERIALIZE_ERROR);
            Assert.Equal("Error checking job status: {0}", ApiConstants.JOB_STATUS_ERROR_FORMAT);
            Assert.Equal("Error uploading documents: {0}", ApiConstants.DOCUMENT_UPLOAD_ERROR_FORMAT);
            Assert.Equal("Error extracting documents: {0}", ApiConstants.DOCUMENT_EXTRACT_ERROR_FORMAT);
            Assert.Equal("Error fetching extraction data: {0}", ApiConstants.EXTRACTION_DATA_ERROR_FORMAT);
            Assert.Equal("Exception in CheckJobStatusAsync: {0}", ApiConstants.CHECK_JOB_STATUS_EXCEPTION_FORMAT);
            Assert.Equal("Exception in UploadDocumentsAsync: {0}", ApiConstants.UPLOAD_DOCUMENTS_EXCEPTION_FORMAT);
            Assert.Equal("Exception in ExtractDocumentsAsync: {0}", ApiConstants.EXTRACT_DOCUMENTS_EXCEPTION_FORMAT);
            Assert.Equal("Exception in FetchExtractionDataAsync: {0}", ApiConstants.FETCH_EXTRACTION_DATA_EXCEPTION_FORMAT);
            Assert.Equal("yyyy-MM-dd HH:mm:ss.ffffff", ApiConstants.TIMESTAMP_FORMAT);
            Assert.Equal("progress", ApiConstants.STATUS_IN_PROGRESS);
            Assert.Equal("running", ApiConstants.STATUS_RUNNING);
            Assert.Equal("Completed", ApiConstants.STATUS_COMPLETED);
            Assert.Equal("Failed", ApiConstants.STATUS_FAILED);
            Assert.Equal("File Draft before Extraction", ApiConstants.FileDraftBeforExtract);
            Assert.Equal("Extraction in progress", ApiConstants.ExtractionInProgress);
            Assert.Equal("Extraction Completed", ApiConstants.ExtractionCompleted);
            Assert.Equal("Extraction Failed", ApiConstants.ExtractionFailed);
            Assert.Equal("Bearer", ApiConstants.DefaultAuthPolicy);
            Assert.Equal("In Progress", ApiConstants.StatusInProgress);
            Assert.Equal("As Is Extraction", ApiConstants.AsIsExtraction);
        }

        [Fact]
        public void StandardKpiConstrans_Values_Are_Correct()
        {
            Assert.Equal("TradingRecords", StandardKpiConstrans.TradingRecords);
            Assert.Equal("CreditKPI", StandardKpiConstrans.CreditKPI);
            Assert.Equal("MasterData", StandardKpiConstrans.MasterData);
            Assert.Equal("FundSize", Fundkpis.FundSize);
            Assert.Equal("Total Commitement", FundkpisMappingName.FundSize);
            Assert.Equal("Gross IRR", InvestmentKpis.GrossIRR);
            Assert.Equal("IRR", InvestmentKpisMappingName.GrossIRR);
            Assert.Equal("Revenue (sales)", FinancialsKpis.RevenueSales);
            Assert.Equal("Revenue TTM", FinancialsKpisMappingName.RevenueSales);
            Assert.Equal("Issuer (PC) Name", MasterKpis.IssuerPCName);
            Assert.Equal("Position", MasterKpisMappingName.IssuerPCName);
        }

        [Fact]
        public void StandardKpiConstrans_FundKpiList_Contains_Expected_Values()
        {
            Assert.Contains(Fundkpis.FundSize, StandardKpiConstrans.FundKpiList);
            Assert.Contains(Fundkpis.NAV, StandardKpiConstrans.FundKpiList);
            Assert.Contains(Fundkpis.GrossIRR, StandardKpiConstrans.FundKpiList);
        }

        [Fact]
        public void StandardKpiConstrans_InvestmentKpiList_Contains_Expected_Values()
        {
            Assert.Contains(InvestmentKpis.GrossIRR.ToLower(), StandardKpiConstrans.InvestmentKpiList);
            Assert.Contains(InvestmentKpis.GrossTVPI.ToLower(), StandardKpiConstrans.InvestmentKpiList);
        }

        [Fact]
        public void StandardKpiConstrans_FinancialKpiList_Contains_Expected_Values()
        {
            Assert.Contains(FinancialsKpis.RevenueSales.ToLower(), StandardKpiConstrans.FinancialKpiList);
            Assert.Contains(FinancialsKpis.EBITDA.ToLower(), StandardKpiConstrans.FinancialKpiList);
        }

        [Fact]
        public void StandardKpiConstrans_MasterDataKpiList_Contains_Expected_Values()
        {
            Assert.Contains(MasterKpis.IssuerPCName.ToLower(), StandardKpiConstrans.MasterDataKpiList);
            Assert.Contains(MasterKpis.Category.ToLower(), StandardKpiConstrans.MasterDataKpiList);
        }
    }
}
