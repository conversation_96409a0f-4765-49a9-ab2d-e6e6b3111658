﻿using Microsoft.EntityFrameworkCore;
using Persistence.Models;

namespace Persistence.DBEntitiesContext
{
    public partial class DBEntities : DbContext
    {
        public DBEntities(DbContextOptions<DBEntities> options) : base(options)
        {
        }
        
        public virtual DbSet<DataIngestionDocuments> DataIngestionDocuments { get; set; }
        public virtual DbSet<Jobs> Jobs { get; set; }
        public virtual DbSet<Status> Status { get; set; }
        public virtual DbSet<DIMappingDocumentsDetails> DIMappingDocumentsDetails { get; set; }
        public virtual DbSet<PortfolioCompanyDetails> PortfolioCompanyDetails { get; set; }
        public virtual DbSet<UserDetails> UserDetails { get; set; }
        public virtual DbSet<FundDetails> FundDetails { get; set; }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.UseLazyLoadingProxies();
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasAnnotation("ProductVersion", "2.2.4-servicing-10062");
        }
    }
}