using Infrastructure.Enum;
using System.ComponentModel;
using Xunit;

namespace UnitTests.Infrastructure.Enum
{
    public class PageSubFieldsDataTypesTests
    {
        [Fact]
        public void PageSubFieldsDataTypes_EnumValues_ShouldHaveCorrectNumericValues()
        {
            // Assert
            Assert.Equal(0, (int)PageSubFieldsDataTypes.Default);
            Assert.Equal(1, (int)PageSubFieldsDataTypes.FreeText);
            Assert.Equal(2, (int)PageSubFieldsDataTypes.Number);
            Assert.Equal(3, (int)PageSubFieldsDataTypes.Currency);
            Assert.Equal(4, (int)PageSubFieldsDataTypes.Percentage);
            Assert.Equal(5, (int)PageSubFieldsDataTypes.Multiple);
            Assert.Equal(6, (int)PageSubFieldsDataTypes.Date);
            Assert.Equal(7, (int)PageSubFieldsDataTypes.List);
        }

        [Fact]
        public void PageSubFieldsDataTypes_DescriptionAttributes_ShouldMatchConstants()
        {
            // Act & Assert
            Assert.Equal(Constants.DataTypeDefault, GetEnumDescription(PageSubFieldsDataTypes.Default));
            Assert.Equal(Constants.DataTypeFreeText, GetEnumDescription(PageSubFieldsDataTypes.FreeText));
            Assert.Equal(Constants.DataTypeNumber, GetEnumDescription(PageSubFieldsDataTypes.Number));
            Assert.Equal(Constants.DataTypeCurrency, GetEnumDescription(PageSubFieldsDataTypes.Currency));
            Assert.Equal(Constants.DataTypePercentage, GetEnumDescription(PageSubFieldsDataTypes.Percentage));
            Assert.Equal(Constants.DataTypeMultiple, GetEnumDescription(PageSubFieldsDataTypes.Multiple));
            Assert.Equal(Constants.DataTypeDate, GetEnumDescription(PageSubFieldsDataTypes.Date));
            Assert.Equal(Constants.DataTypeList, GetEnumDescription(PageSubFieldsDataTypes.List));
        }

        [Fact]
        public void Constants_DataTypeValues_ShouldBeCorrect()
        {
            // Assert
            Assert.Equal("Default", Constants.DataTypeDefault);
            Assert.Equal("Free Text", Constants.DataTypeFreeText);
            Assert.Equal("Number", Constants.DataTypeNumber);
            Assert.Equal("Date", Constants.DataTypeDate);
            Assert.Equal("List", Constants.DataTypeList);
            Assert.Equal("Multiple", Constants.DataTypeMultiple);
            Assert.Equal("Percentage", Constants.DataTypePercentage);
            Assert.Equal("Currency Value", Constants.DataTypeCurrency);
        }

        private static string GetEnumDescription(System.Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = (DescriptionAttribute?)field?.GetCustomAttributes(typeof(DescriptionAttribute), false).FirstOrDefault();
            return attribute?.Description ?? value.ToString();
        }
    }
}