using Amazon.S3;
using Amazon.S3.Model;
using AWSS3;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace UnitTests.AWSS3Tests;

public class AWSS3LibraryTests
{
    private readonly Mock<IAmazonS3> _mockS3Client;
    private readonly Mock<IOptions<S3Setting>> _mockOptions;
    private readonly Mock<ILogger<AWSS3Library>> _mockLogger;
    private readonly AWSS3Library _awsS3Library;
    private readonly S3Setting _s3Settings;

    public AWSS3LibraryTests()
    {
        _mockS3Client = new Mock<IAmazonS3>();
        _mockLogger = new Mock<ILogger<AWSS3Library>>();
        
        _s3Settings = new S3Setting
        {
            BucketName = "test-bucket",
            Region = "us-east-1",
            FolderPrefix = "test-folder",
            KeyPrefix = "test-prefix"
        };
        
        _mockOptions = new Mock<IOptions<S3Setting>>();
        _mockOptions.Setup(x => x.Value).Returns(_s3Settings);
        
        _awsS3Library = new AWSS3Library(_mockOptions.Object, _mockS3Client.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task UploadFileAsync_WithFormFile_Success()
    {
        // Arrange
        var fileName = "test.txt";
        var fileContent = "test content";
        byte[] bytes = System.Text.Encoding.UTF8.GetBytes(fileContent);
        var stream = new MemoryStream(bytes);
        var expectedPresignedUrl = "https://test-bucket.s3.amazonaws.com/test.txt";
        
        var mockFormFile = new Mock<IFormFile>();
        mockFormFile.Setup(f => f.OpenReadStream()).Returns(stream);
        mockFormFile.Setup(f => f.FileName).Returns(fileName);
        mockFormFile.Setup(f => f.Length).Returns(bytes.Length);
        
        _mockS3Client.Setup(x => x.GetPreSignedURL(It.IsAny<GetPreSignedUrlRequest>()))
            .Returns(expectedPresignedUrl);

        // Act
        var result = await _awsS3Library.UploadFileAsync(fileName, mockFormFile.Object);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetFileAsync_Success()
    {
        // Arrange
        var fileName = "test.txt";
        var fileContent = "test content";
        var bytes = System.Text.Encoding.UTF8.GetBytes(fileContent);
        using var responseStream = new MemoryStream(bytes);
        
        var response = new GetObjectResponse { ResponseStream = responseStream };
        _mockS3Client.Setup(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
            .ReturnsAsync(response);

        // Act
        var result = await _awsS3Library.GetFileAsync(fileName);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(Convert.ToBase64String(bytes), result);
        _mockS3Client.Verify(x => x.GetObjectAsync(It.Is<GetObjectRequest>(r => 
            r.BucketName == _s3Settings.BucketName && 
            r.Key == fileName), default), Times.Once);
    }

    [Fact]
    public async Task DeleteFileAsync_Success()
    {
        // Arrange
        var fileName = "test.txt";
        _mockS3Client.Setup(x => x.DeleteObjectAsync(It.IsAny<DeleteObjectRequest>(), default))
            .ReturnsAsync(new DeleteObjectResponse());

        // Act
        await _awsS3Library.DeleteFileAsync(fileName);

        // Assert
        _mockS3Client.Verify(x => x.DeleteObjectAsync(It.Is<DeleteObjectRequest>(r => 
            r.BucketName == _s3Settings.BucketName && 
            r.Key == $"{_s3Settings.KeyPrefix}/{fileName}"), default), Times.Once);
    }

    [Fact]
    public void GetPresignedUrl_Success()
    {
        // Arrange
        var fileName = "test.txt";
        var expectedUrl = "https://test-bucket.s3.amazonaws.com/test.txt";
        _mockS3Client.Setup(x => x.GetPreSignedURL(It.IsAny<GetPreSignedUrlRequest>()))
            .Returns(expectedUrl);

        // Act
        var result = _awsS3Library.GetPresignedUrl(fileName);

        // Assert
        Assert.Equal(expectedUrl, result);
        _mockS3Client.Verify(x => x.GetPreSignedURL(It.Is<GetPreSignedUrlRequest>(r => 
            r.BucketName == _s3Settings.BucketName && 
            r.Key == fileName)), Times.Once);
    }

    [Fact]
    public void GetBucketDetails_ReturnsCorrectSettings()
    {
        // Act
        var result = _awsS3Library.GetBucketDetails();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(_s3Settings.BucketName, result.BucketName);
        Assert.Equal(_s3Settings.Region, result.Region);
        Assert.Equal(_s3Settings.FolderPrefix, result.FolderPrefix);
        Assert.Equal(_s3Settings.KeyPrefix, result.KeyPrefix);
    }

    [Fact]
    public async Task UploadFileAsync_WithFormFile_HandlesException()
    {
        // Arrange
        var fileName = "test.txt";
        var mockFormFile = new Mock<IFormFile>();
        mockFormFile.Setup(f => f.OpenReadStream()).Throws(new Exception("Test exception"));

        // Act
        var result = await _awsS3Library.UploadFileAsync(fileName, mockFormFile.Object);

        // Assert
        Assert.Null(result);
        _mockLogger.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => true),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Fact]
    public async Task GetFileAsync_HandlesException()
    {
        // Arrange
        var fileName = "test.txt";
        _mockS3Client.Setup(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
            .ThrowsAsync(new InvalidOperationException("Test exception"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => _awsS3Library.GetFileAsync(fileName));
        _mockLogger.Verify(x => x.Log(
            LogLevel.Error,
            It.IsAny<EventId>(),
            It.Is<It.IsAnyType>((v, t) => true),
            It.IsAny<Exception>(),
            It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
    }

    [Fact]
    public async Task GetFileContentAsTextAsync_ReturnsFileContentAsText()
    {
        // Arrange
        var s3Path = $"s3://{_s3Settings.BucketName}/test-folder/test.txt";
        var fileContent = "line1\nline2\nline3";
        var bytes = System.Text.Encoding.UTF8.GetBytes(fileContent);
        var responseStream = new MemoryStream(bytes);
        var response = new GetObjectResponse { ResponseStream = responseStream };
        _mockS3Client.Setup(x => x.GetObjectAsync(It.IsAny<GetObjectRequest>(), default))
            .ReturnsAsync(response);

        // Act
        var result = await _awsS3Library.GetFileContentAsTextAsync(s3Path);
        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task UploadFileAsync_WithInvalidFormFile_ReturnsNull()
    {
        // Arrange
        var fileName = "test.txt";
        var mockFormFile = new Mock<IFormFile>();
        mockFormFile.Setup(f => f.Length).Returns(0); // Empty file
        mockFormFile.Setup(f => f.OpenReadStream()).Returns(new MemoryStream());

        // Act
        var result = await _awsS3Library.UploadFileAsync(fileName, mockFormFile.Object);

        // Assert
        Assert.Null(result);
    }

    [Fact] 
    public void GetPresignedUrl_WithNullFileName_ShouldHandleGracefully()
    {
        // Arrange
        string? fileName = null;
        _mockS3Client.Setup(x => x.GetPreSignedURL(It.IsAny<GetPreSignedUrlRequest>()))
            .Returns("https://test-bucket.s3.amazonaws.com/");

        // Act
        var result = _awsS3Library.GetPresignedUrl(fileName!);

        // Assert
        Assert.NotNull(result);
        _mockS3Client.Verify(x => x.GetPreSignedURL(It.Is<GetPreSignedUrlRequest>(r => 
            r.BucketName == _s3Settings.BucketName)), Times.Once);
    }
}
