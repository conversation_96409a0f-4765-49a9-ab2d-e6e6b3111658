﻿using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace Persistence.MongoDb
{
    public class Repository<T> : IRepository<T> where T : class
    {
        public readonly IMongoCollection<T> _collection;
        public Repository(IOptions<MongoDbSettings> settings) 
        {
        
            // Instance of Mongo Client
            var client = new MongoClient(settings.Value.ConnectionString);

            // Database instance
            IMongoDatabase _database = client.GetDatabase(settings.Value.DatabaseName);

            // Collection from the database
            _collection = _database.GetCollection<T>(typeof(T).Name);
        }

        // Retrieves all documents from the collection
        public async Task<IEnumerable<T>> GetAllAsync()
        {
            // Find all documents and convert the result to a list
            return await _collection.Find(_ => true).ToListAsync();
        }

        // Retrieves documents from the collection based on a filter expression
        public async Task<IEnumerable<T>> GetAllAsync(Expression<Func<T, bool>> filter)
        {
            // Apply the filter at the database level before retrieving documents
            return await _collection.Find(filter).ToListAsync();
        }

        // Retrieves a single document by its ID
        public async Task<T?> GetByIdAsync(string id)
        {
            // Convert the string 'id' to an ObjectId before querying
            var objectId = new ObjectId(id);

            // Find the document where the '_id' matches the provided ObjectId
            return await _collection.Find(Builders<T>.Filter.Eq("_id", objectId)).FirstOrDefaultAsync();
        }

        // Inserts a new document into the collection
        public async Task CreateAsync(T entity)
        {
            // Add the provided entity to the collection
            await _collection.InsertOneAsync(entity);
        }

        // Updates an existing document by its ID
        public async Task UpdateAsync(string id, T entity)
        {
            // Convert the string 'id' to an ObjectId before querying
            var objectId = new ObjectId(id);

            // Replace the document where the '_id' matches the provided ID with the new entity
            await _collection.ReplaceOneAsync(Builders<T>.Filter.Eq("_id", objectId), entity);
        }

        // Deletes a document by its ID
        public async Task DeleteAsync(string id)
        {
            // Convert the string 'id' to an ObjectId before querying
            var objectId = new ObjectId(id);

            // Remove the document where the '_id' matches the provided ID
            await _collection.DeleteOneAsync(Builders<T>.Filter.Eq("_id", objectId));
        }
        // Bulk inserts multiple documents into the collection
        public async Task BulkInsertAsync(IEnumerable<T> entities)
        {
            if (entities == null || !entities.Any())
            {
                return;
            }

            // Insert many documents at once for better performance
            await _collection.InsertManyAsync(entities);
        }

        // Bulk updates multiple documents using a list of entities and their IDs
        public async Task BulkUpdateAsync(IDictionary<string, T> entitiesWithIds)
        {
            if (entitiesWithIds == null || !entitiesWithIds.Any())
            {
                return;
            }

            var bulkWriteOperations = new List<WriteModel<T>>();

            foreach (var pair in entitiesWithIds)
            {
                var objectId = new ObjectId(pair.Key);
                var filter = Builders<T>.Filter.Eq("_id", objectId);
                var replaceOne = new ReplaceOneModel<T>(filter, pair.Value);
                bulkWriteOperations.Add(replaceOne);
            }

            // Execute all update operations as a batch
            if (bulkWriteOperations.Any())
            {
                await _collection.BulkWriteAsync(bulkWriteOperations);
            }
        }

        // Finds a single document based on a filter expression
        public async Task<T?> FindOneAsync(Expression<Func<T, bool>> filter)
        {
            return await _collection.Find(filter).FirstOrDefaultAsync();
        }
    }
}
