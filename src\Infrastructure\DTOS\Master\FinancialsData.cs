using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Enum;

/// <summary>
///
/// </summary>
namespace Infrastructure.DTOS.Master
{
    /// <summary>
    /// Represents a Financials entity.
    /// </summary>
    /// <seealso cref="Persistence.Helper.AuditTrail.BaseEntity" />
    public class FinancialsData // Declaring the Financials class which inherits from BaseEntity
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="Financials"/> class.
        /// </summary>
        public FinancialsData() // Constructor for the Financials class
        {
        }

        public string Id { get; set; }
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public Extraction TypeofExtraction { get; set; }
        public List<A1TableGroup> TableGroups { get; set; } //Tabs
        public List<A1File> Files { get; set; }
        public Guid ProcessID { get; set; }
        public Guid JobID { get; set; }
        public string Ticker { get; set; }
        public Guid? TemplateId { get; set; }
        public string? CurrencyUnit { get; set; }
        public string Country { get; set; }
        public string Sector { get; set; }
        public string Industry { get; set; }
        public bool? IsPublished { get; set; }
        public string? CurrencyCode { get; set; }
        public int? CurrencyId { get; set; }
    }
}