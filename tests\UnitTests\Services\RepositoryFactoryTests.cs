using DataIngestionService.Services;
using Microsoft.Extensions.Options;
using Moq;
using Persistence.MongoDb;
using Xunit;
using System;

namespace UnitTests.Services
{
    public class RepositoryFactoryTests
    {
        private readonly Mock<IOptions<MongoDbSettings>> _mockOptions;
        private readonly RepositoryFactory _factory;

        public class TestEntity { public string Id { get; set; } }
        public class AnotherEntity { public int Value { get; set; } }

        public RepositoryFactoryTests()
        {
            _mockOptions = new Mock<IOptions<MongoDbSettings>>();
            _mockOptions.Setup(x => x.Value).Returns(new MongoDbSettings
            {
                ConnectionString = "mongodb://localhost:27017",
                DatabaseName = "TestDb"
            });
            _factory = new RepositoryFactory(_mockOptions.Object);
        }

        [Fact]
        public void GetRepository_ReturnsRepositoryInstance()
        {
            var repo = _factory.GetRepository<TestEntity>();
            Assert.NotNull(repo);
            Assert.IsAssignableFrom<IRepository<TestEntity>>(repo);
        }

        [Fact]
        public void GetRepository_ReturnsSameInstanceForSameType()
        {
            var repo1 = _factory.GetRepository<TestEntity>();
            var repo2 = _factory.GetRepository<TestEntity>();
            Assert.Same(repo1, repo2);
        }

        [Fact]
        public void GetRepository_ReturnsDifferentInstancesForDifferentTypes()
        {
            var repo1 = _factory.GetRepository<TestEntity>();
            var repo2 = _factory.GetRepository<AnotherEntity>();
            Assert.NotSame(repo1, repo2);
        }

        [Fact]
        public void GetRepository_ThreadSafety_ShouldReturnSameInstanceAcrossThreads()
        {
            // Arrange
            IRepository<TestEntity>? repo1 = null;
            IRepository<TestEntity>? repo2 = null;
            var task1Ready = false;
            var task2Ready = false;

            // Act
            var task1 = Task.Run(() =>
            {
                task1Ready = true;
                while (!task2Ready) Thread.Sleep(1); // Wait for both tasks to be ready
                repo1 = _factory.GetRepository<TestEntity>();
            });

            var task2 = Task.Run(() =>
            {
                task2Ready = true;
                while (!task1Ready) Thread.Sleep(1); // Wait for both tasks to be ready
                repo2 = _factory.GetRepository<TestEntity>();
            });

            Task.WaitAll(task1, task2);

            // Assert
            Assert.NotNull(repo1);
            Assert.NotNull(repo2);
            Assert.Same(repo1, repo2);
        }

        [Fact]
        public void GetRepository_WithNullOptions_ShouldThrowException()
        {
            // Arrange
            var mockNullOptions = new Mock<IOptions<MongoDbSettings>>();
            mockNullOptions.Setup(x => x.Value).Returns((MongoDbSettings)null!);
            var factoryWithNullOptions = new RepositoryFactory(mockNullOptions.Object);

            // Act & Assert
            var exception = Record.Exception(() => factoryWithNullOptions.GetRepository<TestEntity>());
            // Should throw NullReferenceException when trying to access null settings
            Assert.NotNull(exception);
            Assert.IsType<NullReferenceException>(exception);
        }
    }
} 