using Newtonsoft.Json;

namespace Infrastructure.Contract.Extract
{
    public class ExtractResponse
    {
        [JsonProperty("isError")]
        public bool IsError { get; set; }

        [JsonProperty("timestamp")]
        public string Timestamp { get; set; } = string.Empty;

        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;

        [JsonProperty("job_id")]
        public Guid? JobId { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;
    }
}