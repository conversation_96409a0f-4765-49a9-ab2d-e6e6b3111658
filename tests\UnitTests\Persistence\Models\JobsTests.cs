using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class JobsTests
    {
        [Fact]
        public void Jobs_DefaultInitialization_ShouldHaveDefaultValues()
        {
            // Act
            var job = new Jobs();

            // Assert
            Assert.Equal(Guid.Empty, job.Id);
            Assert.Equal(Guid.Empty, job.JobId);
            Assert.Equal(Guid.Empty, job.ProcessId);
            Assert.Equal(Guid.Empty, job.StatusId);
            Assert.Equal(Guid.Empty, job.TenantId);
            Assert.Equal(Guid.Empty, job.ParentJobId);
            // Base entity properties
            Assert.True(job.CreatedOn <= DateTime.UtcNow);
            Assert.True(job.CreatedOn >= DateTime.UtcNow.AddMinutes(-1));
            Assert.Equal(0, job.CreatedBy);
            Assert.Null(job.ModifiedOn);
            Assert.Null(job.ModifiedBy);
            Assert.False(job.IsDeleted);
        }

        [Fact]
        public void Jobs_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var id = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var tenantId = Guid.NewGuid();
            var parentJobId = Guid.NewGuid();
            var createdOn = DateTime.UtcNow.AddDays(-1);
            var modifiedOn = DateTime.UtcNow;

            // Act
            var job = new Jobs
            {
                Id = id,
                JobId = jobId,
                ProcessId = processId,
                StatusId = statusId,
                TenantId = tenantId,
                ParentJobId = parentJobId,
                CreatedOn = createdOn,
                CreatedBy = 123,
                ModifiedOn = modifiedOn,
                ModifiedBy = 456,
                IsDeleted = true
            };

            // Assert
            Assert.Equal(id, job.Id);
            Assert.Equal(jobId, job.JobId);
            Assert.Equal(processId, job.ProcessId);
            Assert.Equal(statusId, job.StatusId);
            Assert.Equal(tenantId, job.TenantId);
            Assert.Equal(parentJobId, job.ParentJobId);
            Assert.Equal(createdOn, job.CreatedOn);
            Assert.Equal(123, job.CreatedBy);
            Assert.Equal(modifiedOn, job.ModifiedOn);
            Assert.Equal(456, job.ModifiedBy);
            Assert.True(job.IsDeleted);
        }

        [Fact]
        public void Jobs_WithSameGuidValues_ShouldAllowSameGuids()
        {
            // Arrange
            var sameGuid = Guid.NewGuid();

            // Act
            var job = new Jobs
            {
                Id = sameGuid,
                JobId = sameGuid,
                ProcessId = sameGuid,
                StatusId = sameGuid,
                TenantId = sameGuid,
                ParentJobId = sameGuid
            };

            // Assert
            Assert.Equal(sameGuid, job.Id);
            Assert.Equal(sameGuid, job.JobId);
            Assert.Equal(sameGuid, job.ProcessId);
            Assert.Equal(sameGuid, job.StatusId);
            Assert.Equal(sameGuid, job.TenantId);
            Assert.Equal(sameGuid, job.ParentJobId);
        }

        [Fact]
        public void Jobs_InheritsFromBaseEntity_ShouldHaveBaseEntityProperties()
        {
            // Act
            var job = new Jobs();

            // Assert - Verify it's a BaseEntity
            Assert.IsAssignableFrom<BaseEntity>(job);
        }

        [Fact]
        public void Jobs_WithEmptyGuids_ShouldAcceptEmptyGuids()
        {
            // Act
            var job = new Jobs
            {
                Id = Guid.Empty,
                JobId = Guid.Empty,
                ProcessId = Guid.Empty,
                StatusId = Guid.Empty,
                TenantId = Guid.Empty,
                ParentJobId = Guid.Empty
            };

            // Assert
            Assert.Equal(Guid.Empty, job.Id);
            Assert.Equal(Guid.Empty, job.JobId);
            Assert.Equal(Guid.Empty, job.ProcessId);
            Assert.Equal(Guid.Empty, job.StatusId);
            Assert.Equal(Guid.Empty, job.TenantId);
            Assert.Equal(Guid.Empty, job.ParentJobId);
        }
    }
}