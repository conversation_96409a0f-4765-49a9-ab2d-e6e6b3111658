using Newtonsoft.Json;

namespace Infrastructure.Contract.Extract
{
    public class DocumentUploadResponse
    {
        [JsonProperty("isError")]
        public bool IsError { get; set; }
        
        [JsonProperty("timestamp")]
        public string Timestamp { get; set; }
        
        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;
        
        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;
        
        [JsonProperty("files")]
        public List<FileResponse> Files { get; set; } = new();
    }

    public class FileResponse
    {
        [JsonProperty("file_name")]
        public string FileName { get; set; } = string.Empty;
        
        [JsonProperty("source")]
        public string Source { get; set; } = string.Empty;
        
        [JsonProperty("s3_path")]
        public string S3Path { get; set; } = string.Empty;
        
        [JsonProperty("file_id")]
        public Guid FileId { get; set; }
    }
}