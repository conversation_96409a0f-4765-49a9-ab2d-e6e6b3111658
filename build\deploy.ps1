Import-Module WebAdministration

function check(){
	Write-Host "Passed"
}

function LoadEnv(){
    if ($PSHOME -like "*SysWOW64*") {
        Write-Warning "Restarting this script under 64-bit Windows PowerShell."

        & (Join-<PERSON> ($PSHOME -replace "SysWOW64", "SysNative") powershell.exe) -File `
            (Join-Path $PSScriptRoot $MyInvocation.MyCommand) @args

        Exit $LastExitCode
        Write-Warning " 64-bit Windows PowerShell done."
    }
}

function CreateFolder($arg_Path) {
    Write-Host -ForegroundColor Green -Object "Started: CreateFolder($arg_Path)"

    if (!(Test-Path -Path $arg_Path)) {
        Write-Host -ForegroundColor Green -Object "Creating Folder..."
        New-Item -ItemType directory -Path $arg_Path
    }

    Write-Host -ForegroundColor Green -Object "Ended: CreateFolder($arg_Path)"
}

# Step 0
function TakeBackup() {
    Write-Host -ForegroundColor Green -Object "Begin: TakeBackup"
    # Add backup logic here
    Write-Host -ForegroundColor Yellow -Object "Logic to be added..."
    Write-Host -ForegroundColor Green -Object "Ended: TakeBackup"
}

function DeleteFolderContent($arg_Path) {
    Write-Host -ForegroundColor Green -Object "Begin: DeleteFolderContent($arg_Path)"

    if (Test-Path -Path $arg_Path) {
        Write-Host -ForegroundColor Green -Object "Deleting Folder Contents..."
        Remove-Item ($arg_Path + "\*") -Recurse -Force
    }

    Write-Host -ForegroundColor Green -Object "Ended: DeleteFolderContent($arg_Path)"
}

function DeleteFolder($arg_Path) {
    Write-Host -ForegroundColor Green -Object "Begin: DeleteFolder($arg_Path)"

    if (Test-Path -Path $arg_Path) {
        Write-Host -ForegroundColor Green -Object "Deleting Folder..."
        Remove-Item $arg_Path -Recurse -Force
    }

    Write-Host -ForegroundColor Green -Object "Ended: DeleteFolder($arg_Path)"
}

function CreateApplicationPool($arg_AppPoolName, $arg_AppPoolDotNetVersion) {
    Write-Host -ForegroundColor Green -Object "Started: CreateApplicationPool($arg_AppPoolName, $arg_AppPoolDotNetVersion)"

    if (!(IsApplicationPoolExists($arg_AppPoolName))) {
        Write-Host -ForegroundColor Green -Object "Creating Application Pool..."
        New-WebAppPool -Name $arg_AppPoolName
    }

    Write-Host -ForegroundColor Green -Object "Ended: CreateApplicationPool($arg_AppPoolName, $arg_AppPoolDotNetVersion)"
}

function RemoveApplicationPool($arg_AppPoolName) {
    Write-Host -ForegroundColor Green -Object "Begin: RemoveApplicationPool($arg_AppPoolName)"

    if (IsApplicationPoolExists($arg_AppPoolName)) {
        Write-Host -ForegroundColor Green -Object "Removing Application Pool..."
        Remove-WebAppPool -Name $arg_AppPoolName

        # Sleep for 3 seconds and check again if App pool exists
        Start-Sleep -Seconds 3

        if (IsApplicationPoolExists($arg_AppPoolName)) {
            Write-Host -ForegroundColor Green -Object "Removing Application Pool..."
            Remove-WebAppPool -Name $arg_AppPoolName
        }
    }

    Write-Host -ForegroundColor Green -Object "Ended: RemoveApplicationPool($arg_AppPoolName)"
}

function IsApplicationPoolExists($arg_AppPoolName) {
    Write-Host -ForegroundColor Gray -Object "Begin: IsApplicationPoolExists($arg_AppPoolName)"

    $foundAppPool = $false

    if (Test-Path IIS:\AppPools\$arg_AppPoolName) {
        # AppPool is found
        $foundAppPool = $true
    }

    Write-Host -ForegroundColor Gray -Object "Ended: IsApplicationPoolExists($arg_AppPoolName)"

    return $foundAppPool
}

function StartApplicationPool($arg_AppPoolName) {
    Write-Host -ForegroundColor Green -Object "Started: StartApplicationPool($arg_AppPoolName)"
    
    Start-WebAppPool -Name $arg_AppPoolName

    Write-Host -ForegroundColor Green -Object "Ended: StartApplicationPool($arg_AppPoolName)"
}

function StopApplicationPool($arg_AppPoolName, [int]$secs) {
    Write-Host -ForegroundColor Green -Object "Begin: StopApplicationPool($arg_AppPoolName, $secs)"

    if (!(IsApplicationPoolExists($arg_AppPoolName))) {
        Write-Host -ForegroundColor Green -Object "Ended: StopApplicationPool($arg_AppPoolName, $secs)"
        return
    }

    $retvalue = $false
    $wsec = (get-date).AddSeconds($secs)

    $pstate = Get-WebAppPoolState -Name $arg_AppPoolName

    if ($pstate.Value -eq "Stopped") {
        Write-Host -ForegroundColor Yellow -Object "WebAppPool '$arg_AppPoolName' is stopped already"
        return $true
    }

    Stop-WebAppPool -Name $arg_AppPoolName
    Write-Host -ForegroundColor Green -Object "Waiting up to $secs seconds for the WebAppPool '$arg_AppPoolName' to stop..."
    $poolNotStopped = $true
    while (((get-date) -lt $wsec) -and $poolNotStopped) {
        $pstate = Get-WebAppPoolState -Name $arg_AppPoolName
        if ($pstate.Value -eq "Stopped") {
            Write-Host -ForegroundColor Green -Object "WebAppPool '$arg_AppPoolName' is stopped"
            $poolNotStopped = $false
            $retvalue = $true
        }
    }

    Write-Host -ForegroundColor Green -Object "Ended: StopApplicationPool($arg_AppPoolName, $secs)"

    return $retvalue
}

function CreateWebSite($arg_WebSiteName, $arg_WebSiteBindingPort, $arg_WebSiteAppPoolName, $arg_WebSiteRootPath) {
    Write-Host -ForegroundColor Green -Object "Started: CreateWebSite($arg_WebSiteName, $arg_WebSiteBindingPort, $arg_WebSiteAppPoolName, $arg_WebSiteRootPath)"

    if (!(Get-Website | where-object { $_.name -eq $arg_WebSiteName }) -and (!($arg_WebSiteName -eq ''))) {
        Write-Host -ForegroundColor Green -Object "Creating new website '$arg_WebSiteName' with port '$arg_WebSiteBindingPort'"
        New-Website -Name $arg_WebSiteName -PhysicalPath $arg_WebSiteRootPath -Port $arg_WebSiteBindingPort -ApplicationPool $arg_WebSiteAppPoolName -HostHeader "" -Force
    }

    Write-Host -ForegroundColor Green -Object "Ended: CreateWebSite($arg_WebSiteName, $arg_WebSiteBindingPort, $arg_WebSiteAppPoolName, $arg_WebSiteRootPath)"
}

function DeleteWebSite($arg_webSite) {
    Write-Host -ForegroundColor Green -Object "Ended: DeleteWebSite($arg_webSite)"

    if (!($arg_webSite.ToLower() -eq $defaultWebSite)) {
        # Remove website
        Remove-Website -Name $arg_WebSite -ErrorAction SilentlyContinue
    }

    Write-Host -ForegroundColor Green -Object "Ended: DeleteWebSite($arg_webSite)"
}

function StartWebSite($arg_WebSiteName) {
    Write-Host -ForegroundColor Green -Object "Started: StartWebSite($arg_WebSiteName)"

    if (Get-Website -Name $arg_WebSiteName | where-object { $_.name -eq $arg_WebSiteName }) {
        if ((Get-WebsiteState -Name $arg_WebSiteName).Value.ToLower() -ne "started") {
            Write-Host -ForegroundColor Green -Object "Starting website '$arg_WebSiteName'"
            Start-Website -Name $arg_WebSiteName
        }
    }

    Write-Host -ForegroundColor Green -Object "Ended: StartWebSite($arg_WebSiteName)"
}

function StopWebSite($arg_webSite) {
    Write-Host -ForegroundColor Green -Object "Begin: StopWebSite($arg_webSite)"

    if (!($arg_webSite.ToLower() -eq $defaultWebSite)) {
        # Stop website
        $w = Get-Website -Name $arg_WebSite
        if ($w.Name.Length -gt 0) {
            Stop-Website -Name $arg_WebSite -Passthru
        }
    }

    Write-Host -ForegroundColor Green -Object "Ended: StopWebSite($arg_webSite)"
}

function CreateWebVirtualDirectory($arg_Name, $arg_Path, $arg_WebSite) {
    Write-Host -ForegroundColor Green -Object "Started: CreateWebVirtualDirectory($arg_Name, $arg_Path, $arg_WebSite)"

    if (Test-Path -Path $arg_Path) {
        Write-Host -ForegroundColor Green -Object "Creating Virtual Directory..."
        New-WebVirtualDirectory -Name $arg_Name -PhysicalPath $arg_Path -Site $arg_WebSite -Force -ErrorAction SilentlyContinue
    }

    Write-Host -ForegroundColor Green -Object "Ended: CreateWebVirtualDirectory($arg_Name, $arg_Path, $arg_WebSite)"
}

# We don't want to play around with virtual directory once created
# This may need a fix and testing in future.
function RemoveWebVirtualDirectory($arg_AppName, $arg_website, $arg_applicationPath) {
    Write-Host -ForegroundColor Green -Object "Begin: RemoveWebVirtualDirectory($arg_AppName, $arg_website, $arg_applicationPath)"

    $removeVirtualDirectory = 'IIS:\Sites\' + $arg_website + '\' + $virtualPath + '\' + $arg_AppName
    if ((Test-Path -Path $removeVirtualDirectory)) {
        Remove-Item $removeVirtualDirectory -Force -Recurse
    }

    Write-Host -ForegroundColor Green -Object "Ended: RemoveWebVirtualDirectory($arg_AppName, $arg_website, $arg_applicationPath)"
}

function CopyContentsToDeploymentFolder($arg_SourcePath, $arg_DestinationPath) {
    Write-Host -ForegroundColor Green -Object "Started: CopyContentsToDeploymentFolder($arg_SourcePath, $arg_DestinationPath)"

    Copy-Item ($arg_SourcePath + "\*") -Destination $arg_DestinationPath -Recurse -Force

    Write-Host -ForegroundColor Green -Object "Ended: CopyContentsToDeploymentFolder($arg_SourcePath, $arg_DestinationPath)"
}

function SetIISIUserPersmissions($arg_AppFolderPath) {
    Write-Host -ForegroundColor Green -Object "Started: SetIISIUserPersmissions($arg_AppFolderPath)"

    # $myacl = Get-Acl $arg_AppFolderPath
    # $myaclentry = "IIS_IUSRS","FullControl", "ContainerInherit, ObjectInherit", "None", "Allow"
    # $myaccessrule = New-Object System.Security.AccessControl.FileSystemAccessRule($myaclentry)
    # $myacl.SetAccessRule($myaccessrule)
    # Get-ChildItem -Path "$mypath" -Recurse -Force | Set-Acl -AclObject $myacl -Verbose

    Write-Host -ForegroundColor Green -Object "Ended: SetIISIUserPersmissions($arg_AppFolderPath)"
}

function ConvertToWebApplication($arg_ApplicationPool, $arg_PSPath) {
    Write-Host -ForegroundColor Green -Object "Started: ConvertToWebApplication($arg_ApplicationPool, $arg_PSPath)"

    Write-Host -ForegroundColor Green -Object "Converting to Web application..."
    ConvertTo-WebApplication -ApplicationPool $arg_ApplicationPool -PSPath $arg_PSPath -Force -ErrorAction SilentlyContinue

    Write-Host -ForegroundColor Green -Object "Ended: ConvertToWebApplication($arg_ApplicationPool, $arg_PSPath)"
}

function RemoveWebApplication($arg_AppName, $arg_VirtualPath) {
    Write-Host -ForegroundColor Green -Object "Begin: RemoveWebApplication($arg_AppName, $arg_VirtualPath)"

    Remove-WebApplication -Name $arg_AppName -Site $arg_VirtualPath -ErrorAction SilentlyContinue

    Write-Host -ForegroundColor Green -Object "Ended: RemoveWebApplication($arg_AppName, $arg_VirtualPath)"
}
#BEATOpsHelper Ends

function DeployWebSiteToServer($arg_WebSiteRootPath, $arg_WebSiteAppPoolName, $arg_WebSiteName, $arg_WebSiteBindingsPort) {
    Write-Host -ForegroundColor Green -Object "Started: DeployWebSiteToServer($arg_WebSiteRootPath, $arg_WebSiteAppPoolName, $arg_WebSiteName, $arg_WebSiteBindingsPort)"

    #navigate to the app pools root
    cd IIS:\AppPools\

    # Step 1: Create website root folder
    CreateFolder $arg_WebSiteRootPath

    if (!($arg_WebSiteName -eq $DEFAULT_WEB_SITE)) {
        # Step 2: Create application pool for website
        CreateApplicationPool $arg_WebSiteAppPoolName $APP_POOL_DOT_NET_VERSION

        # Step 3: Start the application pool
        StartApplicationPool $arg_WebSiteAppPoolName

        # Step 4: Create website
        CreateWebSite $arg_WebSiteName $arg_WebSiteBindingsPort $arg_WebSiteAppPoolName $arg_WebSiteRootPath

        # Step 5: Start the website
        StartWebSite $arg_WebSiteName
    }
    else {
        # Step 6: Create Web Virtual Directory
        CreateWebVirtualDirectory $mainApplication $WEBSITE_ROOT_PATH $arg_WebSiteName
    }
    Write-Host -ForegroundColor Green -Object "Ended: DeployWebSiteToServer($arg_WebSiteRootPath, $arg_WebSiteAppPoolName, $arg_WebSiteName, $arg_WebSiteBindingsPort)"
}

function DeployApplicationToServer($arg_AppName) {
    Write-Host -ForegroundColor Green -Object "Started: DeployApplicationToServer($arg_AppName)"
    
    $appPoolName = ("App_Pool_" + $mainApplication + "_" + $stageOrEnvironment + "_" + $clientOrPod + "_" + $arg_AppName).ToLower()
    $applicationPath = ($webSiteInstancePath + "\" + $arg_AppName)
    $applicationSourcePath = ($WEBSITE_ROOT_PATH + $deploymentSourceFolderName + "\publish\")

    #navigate to the app pools root
    cd IIS:\AppPools\

    # Step 1: Create folder for application
    CreateFolder $applicationPath

    # Step 2: Copy contents to deployment folder
    CopyContentsToDeploymentFolder $applicationSourcePath $applicationPath

    # Step 3: Set IIS_IUser Persmission to application folder
    SetIISIUserPersmissions $applicationPath

    # Step 4: Create application pool for web application
    CreateApplicationPool $appPoolName $appPoolDotNetVersion

    # Step 5: Create web application
    ConvertToWebApplication $appPoolName ($PSPath + "\" + $arg_AppName)

    # Step 6: Start the application pool
    StartApplicationPool $appPoolName

    Write-Host -ForegroundColor Green -Object "Ended: DeployApplicationToServer($arg_AppName)"
    Write-Host -ForegroundColor Green -Object ""
}

function ReplaceAppSettings()
{
	#Replace config start
    $rootDirectory = Split-Path (Split-Path $PSScriptRoot -Parent) -Parent
    
    echo $rootDirectory
    
	### appsettings for root
	$apiDirectory = $webSiteInstancePath + "\" + $applicationName + "\appsettings.json"
			
	### appsettings for dev Server
	$apiDirectoryDEV = $webSiteInstancePath + "\" + $applicationName + "\appsettings.dev.json"
			
	### appsettings for Test Server
	$apiDirectoryTEST = $webSiteInstancePath + "\" + $applicationName + "\appsettings.test.json"

    ### appsettings for Test Pod-b Server
	$apiDirectoryTESTPodB = $webSiteInstancePath + "\" + $applicationName + "\appsettings.test-podb.json"
			
	### appsettings for UAT Server
	$apiDirectoryUAT = $webSiteInstancePath + "\" + $applicationName + "\appsettings.uat.json"

    ### appsettings for UAT Server POD B
    $apiDirectoryUATPodB =  $webSiteInstancePath + "\" + $applicationName + "\appsettings.uat-podb.json"
	
	### appsettings for UAT Server
	$apiDirectoryTAABOUATSecurity = $webSiteInstancePath + "\" + $applicationName + "\appsettings.security-uat.json"

    $apiDirectorySecurity = $webSiteInstancePath + "\" + $applicationName + "\appsettings.security.json"

    ### appsettings for PROD DEMO Server
	$apiDirectoryPROD = $webSiteInstancePath + "\" + $applicationName + "\appsettings.prod.json"

    ### appsettings for PROD DEMO2 Server
	$apiDirectoryDEMO2 = $webSiteInstancePath + "\" + $applicationName + "\appsettings.demo2.json"

    ### appsettings for PROD DEMO Server
	$apiDirectoryASMT = $webSiteInstancePath + "\" + $applicationName + "\appsettings.asmt-prod.json"

     ### appsettings for PROD Server for the client Taabo-CH
	$apiDirectoryTAABOCH = $webSiteInstancePath + "\" + $applicationName + "\appsettings.taaboch-prod.json"
	
	 ### appsettings for PROD Server for the client bristol
	$apiDirectoryBRISTOL = $webSiteInstancePath + "\" + $applicationName + "\appsettings.bristol-prod.json"
	
	$apiDirectoryMONMOUTH = $webSiteInstancePath + "\" + $applicationName + "\appsettings.monmouth-prod.json"
	
	$apiDirectoryEXETER = $webSiteInstancePath + "\" + $applicationName + "\appsettings.exeter-prod.json"
	
	$apiDirectoryADMONT = $webSiteInstancePath + "\" + $applicationName + "\appsettings.admont-prod.json"
	
    ### appsettings for Perf1 Server
	$apiDirectoryPERF1 = $webSiteInstancePath + "\" + $applicationName + "\appsettings.perf1.json"			
    ### appsettings for Perf2 Server
	$apiDirectoryPERF2 = $webSiteInstancePath + "\" + $applicationName + "\appsettings.perf2.json"
	
 ### appsettings for PROD trial Server
	$apiDirectoryTRIAL = $webSiteInstancePath + "\" + $applicationName + "\appsettings.trial.json"
    ### appsettings for HIMERA trial Server
	$apiDirectoryHIMERA = $webSiteInstancePath + "\" + $applicationName + "\appsettings.himera-prod.json"		
			
	echo $apiDirectory		
	switch ($env:DEPLOYMENT_GROUP_NAME) {
		"NON-PROD-DEV" {
            #Remove-Item $apiDirectory  
			#Rename-Item -Path $apiDirectoryDEV -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryDEV -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		"NON-PROD-TEST" {
            #Remove-Item $apiDirectory   
			#Rename-Item -Path $apiDirectoryTEST -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryTEST -Destination $apiDirectory
			Remove-Item $apiDirectoryDEV
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "NON-PROD-TEST-POD-B" {
            #Remove-Item $apiDirectory   
			#Rename-Item -Path $apiDirectoryTEST -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryTESTPodB -Destination $apiDirectory
			Remove-Item $apiDirectoryDEV
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		"NON-PROD-UAT" {
            #Remove-Item $apiDirectory    
			#Rename-Item -Path $apiDirectoryUAT -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryUAT -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "NON-PROD-UAT-POD-B" {
            #Remove-Item $apiDirectory    
			#Rename-Item -Path $apiDirectoryUAT -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryUATPodB -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		"uat-taabo-foliosure" {
            #Remove-Item $apiDirectory    
			#Rename-Item -Path $apiDirectoryUAT -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryTAABOUATSecurity -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
			Remove-Item $apiDirectoryTAABOUATSecurity
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "beat-foliosure-security-pod" {
            Copy-Item -Path $apiDirectorySecurity -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
			Remove-Item $apiDirectorySecurity
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "NON-PROD-PERF1" {
            #Remove-Item $apiDirectory   
			#Rename-Item -Path $apiDirectoryPERF1 -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryPERF1 -Destination $apiDirectory
			Remove-Item $apiDirectoryDEV
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "NON-PROD-PERF2" {
            #Remove-Item $apiDirectory   
			#Rename-Item -Path $apiDirectoryPERF2 -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryPERF2 -Destination $apiDirectory
			Remove-Item $apiDirectoryDEV
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
            Remove-Item $apiDirectoryUATPodB
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "beat-foliosure-demo" {
            #Remove-Item $apiDirectory    
			#Rename-Item -Path $apiDirectoryPROD -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryPROD -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
           Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "beat-foliosure-prod-demo2" {
            #Remove-Item $apiDirectory    
			#Rename-Item -Path $apiDirectoryPROD -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryDEMO2  -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
           Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
		}
        "beat-foliosure-prod-asmt" {
            #Remove-Item $apiDirectory    
			#Rename-Item -Path $apiDirectoryPROD -NewName "appsettings.json"
            Copy-Item -Path $apiDirectoryASMT -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        
         "beat-foliosure-prod-taabo-ch" {
           
            Copy-Item -Path $apiDirectoryTAABOCH -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
            Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		 "beat-foliosure-prod-bristol" {
           
            Copy-Item -Path $apiDirectoryBRISTOL -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
			Remove-Item $apiDirectoryBRISTOL
            Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		 "beat-foliosure-prod-monmouth" {
         
            Copy-Item -Path $apiDirectoryMONMOUTH -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
			Remove-Item $apiDirectoryBRISTOL
			Remove-Item $apiDirectoryMONMOUTH
            Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		"beat-foliosure-prod-exeter" {
         
            Copy-Item -Path $apiDirectoryEXETER -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
			Remove-Item $apiDirectoryBRISTOL
			Remove-Item $apiDirectoryMONMOUTH
			Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		"beat-foliosure-prod-trial" {
         
            Copy-Item -Path $apiDirectoryTRIAL -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
			Remove-Item $apiDirectoryBRISTOL
			Remove-Item $apiDirectoryMONMOUTH
			Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
		"beat-foliosure-prod-admont" {
			Copy-Item -Path $apiDirectoryADMONT -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
			Remove-Item $apiDirectoryBRISTOL
			Remove-Item $apiDirectoryMONMOUTH
			Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        "beat-foliosure-prod-himera" {
         
            Copy-Item -Path $apiDirectoryHIMERA -Destination $apiDirectory
            Remove-Item $apiDirectoryDEV	
			Remove-Item $apiDirectoryTEST
            Remove-Item $apiDirectoryTESTPodB
			Remove-Item $apiDirectoryUAT
			Remove-Item $apiDirectoryPROD
            Remove-Item $apiDirectoryPERF1
            Remove-Item $apiDirectoryPERF2
            Remove-Item $apiDirectoryTAABOCH
			Remove-Item $apiDirectoryBRISTOL
			Remove-Item $apiDirectoryMONMOUTH
			Remove-Item $apiDirectoryEXETER
			Remove-Item $apiDirectoryTRIAL
            Remove-Item $apiDirectoryUATPodB
            Remove-Item $apiDirectoryHIMERA
            Remove-Item $apiDirectoryADMONT
            Remove-Item $apiDirectoryASMT
            Remove-Item $apiDirectoryDEMO2
		}
        
		Default {
		}
	}
	#Replace config End
}

function RemoveApplicationFromServer($arg_AppName) {
    Write-Host -ForegroundColor Green -Object "Started: RemoveApplicationFromServer($arg_AppName)"
    
    $appPoolName = ("App_Pool_foliosure_" + $stageOrEnvironment + "_" + $clientOrPod + "_" + $arg_AppName).ToLower()
    $applicationPath = ($webSiteInstancePath + "\" + $arg_AppName)

    #navigate to the app pools root
    cd IIS:\AppPools\

    # Step 0: Take backups, if required
    TakeBackup

    # Step 1: Stop app pool, if running
    StopApplicationPool $appPoolName 30

    # Step 2: Remove web application
    RemoveWebApplication $arg_AppName $VirtualPath

    # Step 3: Remove virtual directory, if any
    #RemoveWebVirtualDirectory $webSiteName $WEBSITE_ROOT_PATH $arg_AppName

    # Step 4: Remove App pool, example - App_Pool_foliosure_sts_features
    RemoveApplicationPool $appPoolName
    #return "exit here"

    # Step 5: Delete the Physical path, if exists
    #DeleteFolder $applicationPath

    Write-Host -ForegroundColor Green -Object "Ended: RemoveApplicationFromServer($arg_AppName)"
    Write-Host -ForegroundColor Green -Object ""
}

function execute-dbScripts ([string]$sqlDBName,[string]$secretKey)
{
  $SqlScriptFolderPath = $webSiteInstancePath + "\" + $applicationName + "\DeploymentSqlScript"
  Write-Host $SqlScriptFolderPath
  foreach ($inputSqlFile in get-childitem -path $SqlScriptFolderPath -filter "*.sql")
  {
  try{
	  $FullFilePath = $SqlScriptFolderPath + "\" + $inputSqlFile;
      aws configure set region $AWS_REGION
      $PoshResponse = aws secretsmanager get-secret-value --secret-id $secretKey | ConvertFrom-Json
      $Creds = $PoshResponse.SecretString | ConvertFrom-Json
      $ConnectionString = "Server=$($Creds.host);Database=$sqlDBName;User Id=$($Creds.username);Password=$($Creds.password);TrustServerCertificate=True;"
      Invoke-Sqlcmd -ConnectionString $ConnectionString -InputFile $FullFilePath
    }
  catch {
      Write-Host "An error occurred: $PSItem.Exception.Message"
        }
  }
}
