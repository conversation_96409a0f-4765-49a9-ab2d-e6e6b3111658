﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<location path="." inheritInChildApplications="false">
		<system.web>
			<httpRuntime enableVersionHeader="false" />
		</system.web>
		<system.webServer>
			<httpProtocol>
				<customHeaders>
					<clear />
					<remove name="X-Frame-Options" />
					<remove name="X-Content-Type-Options" />
					<remove name="X-Powered-By" />
					<remove name="X-AspNetMvc-Version" />
					<remove name="Content-Security-Policy"/>
					<add name="X-Xss-Protection" value="1; mode=block" />
					<add name="X-Frame-Options" value="SAMEORIGIN" />
					<add name="X-Content-Type-Options" value="nosniff" />
					<add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
					<add name="Cache-Control" value="no-cache, no-store, must-revalidate, pre-check=0, post-check=0, max-age=0, s-maxage=0" />
					<add name="Content-Security-Policy" value="default-src 'self'; font-src;img-src 'self' data:; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" />
					<add name="Pragma" value="no-cache" />
					<add name="Expires" value="0" />
					<add name="Strict-Transport-Security" value="max-age=31536000; includeSubDomains" />
				</customHeaders>
			</httpProtocol>
			<directoryBrowse enabled="false" />
			<security>
				<requestFiltering removeServerHeader="true">
					 <!--This will handle requests up to 350MB-->
					<requestLimits maxAllowedContentLength="400000000" />
					<!--<verbs allowUnlisted="false">
						<clear/>
						<add verb="GET" allowed="true"/>
						<add verb="POST" allowed="true"/>
						<add verb="PUT" allowed="true"/>
						<add verb="DELETE" allowed="true"/>
					</verbs>-->
				</requestFiltering>
			</security>
			<aspNetCore processPath="%LAUNCHER_PATH%" arguments="%LAUNCHER_ARGS%" stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" hostingModel="InProcess" />	
			<handlers>
				<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
			</handlers>
		</system.webServer>

	</location>
</configuration>