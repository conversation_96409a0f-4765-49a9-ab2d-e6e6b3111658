﻿using Persistence.UnitOfWork;
using System.Security.Claims;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Ingestion.API.Helpers
{
    public class HelperService(IUnitOfWork unitOfWork) : IHelperService
    {
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        public async Task<int> GetCurrentUserId(ClaimsPrincipal User)
        {
            if (User == null)
                return -1;
            int userId = 0;
            var claimsIdentity = User.Identity as ClaimsIdentity;
            if (claimsIdentity?.Claims?.FirstOrDefault(x => x.Type.Contains("preferred_username"))?.Value != null)
            {
                var user_Email = claimsIdentity?.Claims?.FirstOrDefault(x => x.Type.Contains("preferred_username"))?.Value;
                var userDetail = await _unitOfWork.UserDetailsRepository.FindFirstAsync(x => x.EmailID.ToLower()== user_Email.ToLower() && !x.IsDeleted && x.IsActive==true);
                return userDetail != null ? userDetail.UserID : 0;
            }
            else
            {
                var user = claimsIdentity?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                userId = user != null ? Convert.ToInt32(user) : 0;
            }
            return userId;
        }
    }
}
