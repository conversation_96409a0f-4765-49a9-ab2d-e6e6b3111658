using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Contract
{
    public class ProcessDetailsDto
    {
       
        public Guid ProcessId { get; set; }
        public List<DocumentSummaryDto> Documents { get; set; } = [];
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public int Year { get; set; }
        public string Month { get; set; }
        public string Quarter { get; set; }
        public string PeriodType { get; set; }
        public string EncryptedPortfolioCompanyId { get; set; }
        public string EncryptedFundId { get; set; }
        public string Status { get; set; }
        public string State { get; set; }
        public Guid JobId { get; set; }
        public Guid StatusId { get; set; }
        public Guid ParentJobId { get; set; }
        public bool? IsClassifiers { get; set; } = false;
        public int FundId { get; set; }
        public string FundName { get; set; }
        public string ExtractionType { get; set; }
    }
}