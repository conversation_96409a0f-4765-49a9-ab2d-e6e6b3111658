using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Contract
{
    public class StandardKpiModel
    {
        public IList<StandardFundKpiItemModel> FundKpis { get; set; } = new List<StandardFundKpiItemModel>();
        public IList<StandardKpiItemModel> InvestmentKpis { get; set; } = new List<StandardKpiItemModel>();
        public IList<StandardKpiItemModel> FinancialsKpis { get; set; } = new List<StandardKpiItemModel>();
        public IList<StandardKpiItemModel> MasterDataKpis { get; set; } = new List<StandardKpiItemModel>();
    }
    public class StandardFundKpiItemModel
    {
        public string Text { get; set; }
        public Guid Value { get; set; }
        public Guid KpiId { get; set; }
        public string KpiInfo { get; set; }
        public int MappingId { get; set; }
        public string MappingText { get; set; }
    }
    public class StandardKpiItemModel
    {
        public string Text { get; set; }
        public Guid Value { get; set; }
        public int KpiId { get; set; }
        public string KpiInfo { get; set; }
        public int MappingId { get; set; }
        public string MappingText { get; set; }
        public int CompanyId { get; set; }
        public bool IsHeader { get; set; }
        public bool IsBoldKpi { get; set; }
        public int ModuleId { get; set; }
        public int ParentId { get; set; }
    }
    
}