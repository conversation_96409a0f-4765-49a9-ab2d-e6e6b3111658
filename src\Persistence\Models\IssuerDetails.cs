﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;

namespace Persistence.Models
{
    public class IssuerModel:BaseEntity
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        public Guid ProcessId { get; set; }
        public int? FundId { get; set; }
        [BsonIgnoreIfNull]
        public List<IssuerKPIModel> IssuerKPIModel { get; set; } = [];
    }
    public class IssuerKPIModel
    {
        public int Id { get; set; }
        public int FieldId { get; set; }
        public string Text { get; set; }
        [BsonIgnoreIfNull]
        public List<KpiItemsModel> Items { get; set; } = [];
    }
    public class KpiItemsModel
    {
        public int Id { get; set; }
        public int ItemId { get; set; }
        public string Text { get; set; }
        public string Name { get; set; }
        [BsonIgnoreIfNull]
        public List<KpiItemModel> Items { get; set; } = [];
    }
    public class KpiItemModel
    {
        public int Id { get; set; }
        public int MappingId { get; set; }
        public int CompanyId { get; set; }
        public int KpiId { get; set; }
        public string Text { get; set; }
        public string Name { get; set; } = string.Empty;
        public bool IsHeader { get; set; }
        public bool IsBoldKpi { get; set; }
        public int ModuleId { get; set; }
        public int ParentId { get; set; }
        public string KpiInfo { get; set; }
    }

}