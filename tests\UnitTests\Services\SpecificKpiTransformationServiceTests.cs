using AutoMapper;
using DapperRepository;
using DataIngestionService.Constants;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract;
using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Enum;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.GenericRepository;
using Persistence.Models;
using Persistence.Models.Specific;
using Persistence.MongoDb;
using Persistence.UnitOfWork;
using System.Linq.Expressions;
using System.Text.Json;
using Xunit;
using SpecificKpiDoc = Persistence.Models.Specific.SpecificKpiDocument;

namespace UnitTests.Services
{
    public class SpecificKpiTransformationServiceTests
    {
        private readonly Mock<IRepositoryFactory> _mockRepoFactory;
        private readonly Mock<IRepository<SpecificKpiDoc>> _mockSpecificKpiRepo;
        private readonly Mock<ILogger<SpecificKpiTransformationService>> _mockLogger;
        private readonly Mock<IWebHostEnvironment> _mockHostingEnv;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IDapperGenericRepository> _mockDapper;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IGenericRepository<DIMappingDocumentsDetails>> _mockDiMappingDocumentsDetailsRepo;

        private readonly SpecificKpiTransformationService _service;

        public SpecificKpiTransformationServiceTests()
        {
            _mockRepoFactory = new Mock<IRepositoryFactory>();
            _mockSpecificKpiRepo = new Mock<IRepository<SpecificKpiDoc>>();
            _mockLogger = new Mock<ILogger<SpecificKpiTransformationService>>();
            _mockHostingEnv = new Mock<IWebHostEnvironment>();
            _mockMapper = new Mock<IMapper>();
            _mockDapper = new Mock<IDapperGenericRepository>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockDiMappingDocumentsDetailsRepo = new Mock<IGenericRepository<DIMappingDocumentsDetails>>();

            _mockDapper.Setup(d => d.Query<ModuleDetails>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<ModuleDetails>());
            _mockRepoFactory.Setup(x => x.GetRepository<SpecificKpiDoc>()).Returns(_mockSpecificKpiRepo.Object);
            _mockUnitOfWork.Setup(x => x.DIMappingDocumentsDetailsRepository).Returns(_mockDiMappingDocumentsDetailsRepo.Object);

            _service = new SpecificKpiTransformationService(
                _mockRepoFactory.Object,
                _mockLogger.Object,
                _mockHostingEnv.Object,
                _mockMapper.Object,
                _mockDapper.Object,
                _mockUnitOfWork.Object
            );
        }

        [Fact]
        public async Task TransformDsSpecificToSpecific_InvalidJson_ThrowsArgumentException()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var invalidJson = "this is not valid json";

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => _service.TransformDsSpecificToSpecific(invalidJson, processId));
        }

        [Fact]
        public async Task SaveSpecificKpiDocument_ValidDto_SavesDocumentAndReturnsId()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var userId = 1;
            var specificDto = new SpecificDto();
            var mappedDocument = new SpecificKpiDoc { Id = Guid.NewGuid().ToString() };
            _mockMapper.Setup(m => m.Map<SpecificKpiDoc>(specificDto)).Returns(mappedDocument);

            // Act
            var result = await _service.SaveSpecificKpiDocument(specificDto, processId, userId);

            // Assert
            Assert.Equal(mappedDocument.Id, result);
            Assert.Equal(processId, mappedDocument.ProcessId);
            Assert.Equal(userId, mappedDocument.CreatedBy);
            _mockSpecificKpiRepo.Verify(r => r.CreateAsync(mappedDocument), Times.Once);
        }

        [Fact]
        public async Task UpdateSpecificKpiDocument_ValidRequest_UpdatesDocument()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var userId = 1;
            var specificDto = new SpecificDto();
            var existingDocument = new SpecificKpiDoc { Id = Guid.NewGuid().ToString() };
            var updatedDocument = new SpecificKpiDoc();

            _mockSpecificKpiRepo.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDoc, bool>>>())).ReturnsAsync(existingDocument);
            _mockMapper.Setup(m => m.Map<SpecificKpiDoc>(specificDto)).Returns(updatedDocument);

            // Act
            var result = await _service.UpdateSpecificKpiDocument(specificDto, processId, userId);

            // Assert
            Assert.Equal(existingDocument.Id, result);
            Assert.Equal(processId, updatedDocument.ProcessId);
            Assert.Equal(userId, updatedDocument.ModifiedBy);
            _mockSpecificKpiRepo.Verify(r => r.UpdateAsync(existingDocument.Id, updatedDocument), Times.Once);
        }

        [Fact]
        public async Task UpdateSpecificKpiDocument_DocumentNotFound_ReturnsEmptyString()
        {
            // Arrange
            var processId = Guid.NewGuid();
            _mockSpecificKpiRepo.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDoc, bool>>>())).ReturnsAsync((SpecificKpiDoc)null);

            // Act
            var result = await _service.UpdateSpecificKpiDocument(new SpecificDto(), processId, 1);

            // Assert
            Assert.Equal(string.Empty, result);
        }
        
        [Fact]
        public async Task GetSpecificKpiDocumentByProcessId_DocumentExists_ReturnsDto()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var document = new SpecificKpiDoc { ProcessId = processId, CustomSections = [] };
            var dto = new SpecificDto();

            _mockSpecificKpiRepo.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDoc, bool>>>())).ReturnsAsync(document);
            _mockDapper.Setup(d => d.Query<KpiModel>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<KpiModel>());
            _mockDapper.Setup(d => d.Query<SubPageFields>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(new List<SubPageFields>());
            _mockMapper.Setup(m => m.Map<SpecificDto>(document)).Returns(dto);

            // Act
            var result = await _service.GetSpecificKpiDocumentByProcessId(processId);

            // Assert
            Assert.Same(dto, result);
        }

        [Fact]
        public async Task GetSpecificKpiDocumentByProcessId_DocumentDoesNotExist_ReturnsEmptyDto()
        {
            // Arrange
            var processId = Guid.NewGuid();
            _mockSpecificKpiRepo.Setup(r => r.FindOneAsync(It.IsAny<Expression<Func<SpecificKpiDoc, bool>>>())).ReturnsAsync((SpecificKpiDoc)null);

            // Act
            var result = await _service.GetSpecificKpiDocumentByProcessId(processId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<SpecificDto>(result);
        }

        [Fact]
        public async Task GetFundIdByProcessId_MappingExists_ReturnsFundId()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var fundId = 123;
            var mapping = new DIMappingDocumentsDetails { FundId = fundId };
            _mockDiMappingDocumentsDetailsRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(mapping);

            // Act
            var result = await _service.GetFundIdByProcessId(processId);

            // Assert
            Assert.Equal(fundId.ToString(), result);
        }

        [Fact]
        public async Task GetFundIdByProcessId_MappingDoesNotExist_ReturnsEmptyString()
        {
            // Arrange
            var processId = Guid.NewGuid();
            _mockDiMappingDocumentsDetailsRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync((DIMappingDocumentsDetails)null);

            // Act
            var result = await _service.GetFundIdByProcessId(processId);

            // Assert
            Assert.Equal(string.Empty, result);
        }

        [Fact]
        public async Task ProcessAllSectionsAndAssignKpis_WithData_ProcessesAndAssignsKpis()
        {
            // Arrange
            var document = new SpecificKpiDoc
            {
                CustomSections = [ new CustomSection { SectionType = "", Data = [], Options = [], Periods = [] } ]
            };

            var kpiModels = new List<KpiModel>
            {
                new KpiModel { KpiId = 2, KpiName = "Financial KPI", ModuleId = (int)KpiModuleType.TradingRecords, PortfolioCompanyId = 101 },
                new KpiModel { KpiId = 4, KpiName = "Investment KPI", ModuleId = (int)KpiModuleType.CreditKPI, PortfolioCompanyId = 101 }
            };
            var fundKpis = new List<KpiModel> { new KpiModel { KpiId = 1, KpiName = "Fund KPI", ModuleId = (int)KpiModuleType.FundFinancials, FundId = 1 } };
            var staticFields = new List<SubPageFields> { new SubPageFields { FieldId = 3, AliasName = "Static Field" } };

            _mockDapper.Setup(d => d.Query<KpiModel>(SqlConstants.QueryByGetAllKpis, It.IsAny<object>())).ReturnsAsync(kpiModels);
            _mockDapper.Setup(d => d.Query<KpiModel>(SqlConstants.QueryByGetFundKpis, It.IsAny<object>())).ReturnsAsync(fundKpis);
            _mockDapper.Setup(d => d.Query<SubPageFields>(SqlConstants.QueryByGetPageConfigStaticFields, It.IsAny<object>())).ReturnsAsync(staticFields);

            _mockDapper.Setup(d => d.Query<CurrencyModel>(SqlConstants.QueryByGetFundCurrencies, It.IsAny<object>())).ReturnsAsync(new List<CurrencyModel> { });
            _mockDapper.Setup(d => d.Query<CurrencyModel>(SqlConstants.QueryByGetCompanyCurrencies, It.IsAny<object>())).ReturnsAsync(new List<CurrencyModel> { });
            // Act
            await _service.ProcessAllSectionsAndAssignKpis(document);

            // Assert
            Assert.Single(document.CustomSections);
        }
    }
} 