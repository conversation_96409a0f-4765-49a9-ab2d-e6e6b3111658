using System;
using System.ComponentModel.DataAnnotations;

namespace Ingestion.API.Models
{
    public class GetPdfHighlightsRequest
    {
        [Required]
        public Guid ProcessId { get; set; }

        [Required]
        public string TableGroupLabel { get; set; }

        [Required]
        public string PeriodDate { get; set; }

        public bool IsStateValid()
        {
            return ProcessId != Guid.Empty && !string.IsNullOrWhiteSpace(TableGroupLabel) && !string.IsNullOrWhiteSpace(PeriodDate);
        }
    }
} 