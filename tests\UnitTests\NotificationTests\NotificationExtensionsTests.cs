using Microsoft.Extensions.DependencyInjection;
using Notification.Extensions;
using Notification.Services;
using Xunit;

namespace UnitTests.NotificationTests
{
    public class NotificationExtensionsTests
    {        [Fact]
        public void AddNotificationServices_ShouldRegisterRequiredServices()
        {
            // Arrange
            var services = new ServiceCollection();
            
            // Add required services
            services.AddLogging();

            // Act
            services.AddNotificationServices();

            // Assert
            var serviceProvider = services.BuildServiceProvider();

            // Verify NotificationSender is registered as singleton
            var notificationSender1 = serviceProvider.GetService<NotificationSender>();
            var notificationSender2 = serviceProvider.GetService<NotificationSender>();

            Assert.NotNull(notificationSender1);
            Assert.NotNull(notificationSender2);
            Assert.Same(notificationSender1, notificationSender2); // Verify singleton
        }
    }
}
