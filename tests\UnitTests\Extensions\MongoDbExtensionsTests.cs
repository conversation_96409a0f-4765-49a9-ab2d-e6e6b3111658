using API.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Moq;
using Persistence.MongoDb;
using Xunit;

namespace UnitTests.Extensions
{
    public class MongoDbExtensionsTests : IDisposable
    {
        private readonly string? _originalEnvironment;

        public MongoDbExtensionsTests()
        {
            // Store original environment to restore later
            _originalEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        }

        public void Dispose()
        {
            // Restore original environment variable
            Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", _originalEnvironment);
        }

        [Fact]
        public void ConfigureMongoDb_ShouldConfigureMongoDbSettings()
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = CreateTestConfiguration();

            try
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");

                // Act
                var result = services.ConfigureMongoDb(configuration);

                // Assert
                Assert.Same(services, result); // Should return the same service collection for chaining
                Assert.NotEmpty(services); // Should add services
            }
            finally
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
            }
        }

        private IConfiguration CreateTestConfiguration()
        {
            var inMemorySettings = new Dictionary<string, string?>
            {
                {"MongoDb:ConnectionString", "mongodb://localhost:27017"},
                {"MongoDb:DatabaseName", "TestDatabase"},
                {"AWS:MongoSecretKey", "test-mongo-secret"}
            };

            return new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build();
        }

        [Theory]
        [InlineData("Production")]
        [InlineData("Staging")]  
        [InlineData("")]
        public void ConfigureMongoDb_NonDevelopmentEnvironment_ShouldCallAwsSecretsManager(string? environmentValue)
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = CreateTestConfiguration();

            try
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", environmentValue);

                // Act
                var result = services.ConfigureMongoDb(configuration);

                // Assert
                Assert.Same(services, result);
                
                // Verify the environment check logic
                var isDevelopment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development";
                Assert.False(isDevelopment);
            }
            finally
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
            }
        }

        [Fact]
        public void ConfigureMongoDb_DevelopmentEnvironment_ShouldUseConfigurationSettings()
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = CreateTestConfiguration();

            try
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");

                // Act
                var result = services.ConfigureMongoDb(configuration);

                // Assert
                Assert.Same(services, result);
                Assert.NotEmpty(services); // Should add services
            }
            finally
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
            }
        }

        [Fact]
        public void ConfigureMongoDb_WithEmptySecret_ShouldUseOriginalConnectionString()
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = CreateTestConfiguration();
            
            try
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Production");

                // Act 
                var result = services.ConfigureMongoDb(configuration);

                // Assert
                Assert.Same(services, result);
                Assert.NotEmpty(services); // Should add services
            }
            finally
            {
                Environment.SetEnvironmentVariable("ASPNETCORE_ENVIRONMENT", "Development");
            }
        }

        [Fact]
        public void ConfigureMongoDb_WithNullConfiguration_ShouldThrowException()
        {
            // Arrange
            var services = new ServiceCollection();
            IConfiguration? nullConfiguration = null;

            // Act & Assert - The method throws NullReferenceException due to direct access without null checking
            Assert.Throws<NullReferenceException>(() => services.ConfigureMongoDb(nullConfiguration!));
        }

        [Fact]
        public void ConfigureMongoDb_WithNullServices_ShouldThrowArgumentNullException()
        {
            // Arrange
            IServiceCollection? nullServices = null;
            var mockConfiguration = new Mock<IConfiguration>();
            var mockMongoSection = new Mock<IConfigurationSection>();
            mockConfiguration.Setup(c => c.GetSection("MongoDb")).Returns(mockMongoSection.Object);

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => nullServices!.ConfigureMongoDb(mockConfiguration.Object));
        }
    }
}