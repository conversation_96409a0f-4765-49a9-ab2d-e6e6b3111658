﻿using Infrastructure.Contract.Extract;
using Infrastructure.DTOS.Master;
using Persistence.Models.Classifier;

namespace DataIngestionService.IServices
{
    public interface IExtract
    {
        /// <summary>
        /// Check the status of an extraction job
        /// </summary>
        Task<JobStatusResponse> CheckJobStatusAsync(Guid jobId, string token, CancellationToken cancellationToken = default);
        /// <summary>
        /// Fetch extracted data
        /// </summary>
        Task<ExtractionDataResponse> FetchExtractionDataAsync(Guid jobId, Guid processId, string token, CancellationToken cancellationToken = default);
                
        /// <summary>
        /// Adds or updates classifier data for a process
        /// </summary>
        Task<string> AddOrUpdateClassifierData(TableSuggestionResponse suggestionResponse, Guid ProcessId, int userId);

        /// <summary>
        /// Gets classifier data by process ID
        /// </summary>
        Task<Classifier> GetClassifierDataByProcessIdAsync(Guid processId);
        
        /// <summary>
        /// Deletes classifier data by process ID
        /// </summary>
        Task<bool> DeleteClassifierDataByProcessIdAsync(Guid processId,int userId);

        Task<bool> UpdateFinancials(FinancialsData data, Guid processId);
        Task<ExtractionDataResponse> FetchSpecificExtractionData(Guid jobId, Guid processId, string token, CancellationToken cancellationToken = default);
    }
}
