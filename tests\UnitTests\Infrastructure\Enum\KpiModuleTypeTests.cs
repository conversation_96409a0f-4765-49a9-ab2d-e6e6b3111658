using Infrastructure.Enum;
using System.ComponentModel;
using Xunit;

namespace UnitTests.Infrastructure.Enum
{
    public class KpiModuleTypeTests
    {
        [Fact]
        public void KpiModuleType_AllValues_ShouldHaveCorrectNumericValues()
        {
            // Assert specific enum values
            Assert.Equal(1, (int)KpiModuleType.TradingRecords);
            Assert.Equal(2, (int)KpiModuleType.CreditKPI);
            Assert.Equal(3, (int)KpiModuleType.Operational);
            Assert.Equal(4, (int)KpiModuleType.Investment);
            Assert.Equal(5, (int)KpiModuleType.Company);
            Assert.Equal(6, (int)KpiModuleType.Impact);
            Assert.Equal(7, (int)KpiModuleType.ProfitAndLoss);
            Assert.Equal(8, (int)KpiModuleType.BalanceSheet);
            Assert.Equal(9, (int)KpiModuleType.CashFlow);
            Assert.Equal(11, (int)KpiModuleType.CapTable1);
            Assert.Equal(16, (int)KpiModuleType.MonthlyReport);
            Assert.Equal(30, (int)KpiModuleType.OtherKPI10);
            Assert.Equal(1001, (int)KpiModuleType.FundFinancials);
        }

        [Fact]
        public void KpiModuleType_DescriptionAttributes_ShouldBeCorrect()
        {
            // Arrange & Act
            var tradingRecordsDescription = GetEnumDescription(KpiModuleType.TradingRecords);
            var creditKpiDescription = GetEnumDescription(KpiModuleType.CreditKPI);
            var customTable1Description = GetEnumDescription(KpiModuleType.CustomTable1);

            // Assert
            Assert.Equal("TradingRecords", tradingRecordsDescription);
            Assert.Equal("CreditKPI", creditKpiDescription);
            Assert.Equal("CustomTable1", customTable1Description);
        }

        [Fact]
        public void KpiModuleType_EnumValues_ShouldBeDefinedCorrectly()
        {
            // Act
            var allValues = System.Enum.GetValues<KpiModuleType>();

            // Assert
            Assert.Contains(KpiModuleType.TradingRecords, allValues);
            Assert.Contains(KpiModuleType.FundFinancials, allValues);
            Assert.Contains(KpiModuleType.OtherKPI1, allValues);
            Assert.Contains(KpiModuleType.CapTable5, allValues);
            
            // Verify enum count
            Assert.True(allValues.Length > 25); // Should have many enum values
        }

        private static string GetEnumDescription(System.Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = (DescriptionAttribute?)field?.GetCustomAttributes(typeof(DescriptionAttribute), false).FirstOrDefault();
            return attribute?.Description ?? value.ToString();
        }
    }
}