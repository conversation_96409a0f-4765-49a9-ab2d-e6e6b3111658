﻿using Persistence.GenericRepository;
using Persistence.Models;

namespace Persistence.UnitOfWork {
    public partial interface IUnitOfWork {

        /// <summary>
        /// Save method.
        /// </summary>
        int Save ();
        Task<int> SaveAsync ();

        bool AutoDetectChangesEnabled { get; set; }

        #region Repository Creation properties...
      
        IGenericRepository<DataIngestionDocuments> DataIngestionDocumentRepository { get; }
        IGenericRepository<Jobs> JobsRepository { get; }
        IGenericRepository<Status> StatusRepository { get; }
        IGenericRepository<DIMappingDocumentsDetails> DIMappingDocumentsDetailsRepository { get; }
        IGenericRepository<PortfolioCompanyDetails> PortfoiloCompanyDetailsRepository { get; }
        IGenericRepository<UserDetails> UserDetailsRepository { get; }
        IGenericRepository<FundDetails> FundDetailsRepository { get; }
        #endregion Repository Creation properties...
    }
}