using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class PagesTests
    {
        [Fact]
        public void Pages_DefaultInitialization_ShouldHaveDefaultValues()
        {
            // Act
            var page = new Pages();

            // Assert
            Assert.Null(page.Id);
            Assert.Equal(Guid.Empty, page.DocumentId);
            Assert.Equal(0, page.ModuleId);
            Assert.Equal(string.Empty, page.ModuleName);
            Assert.Equal(string.Empty, page.Items);
            // Base entity properties
            Assert.True(page.CreatedOn <= DateTime.UtcNow);
            Assert.True(page.CreatedOn >= DateTime.UtcNow.AddMinutes(-1));
            Assert.Equal(0, page.CreatedBy);
            Assert.Null(page.ModifiedOn);
            Assert.Null(page.ModifiedBy);
            Assert.False(page.IsDeleted);
        }

        [Fact]
        public void Pages_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var documentId = Guid.NewGuid();
            var createdOn = DateTime.UtcNow.AddDays(-1);
            var modifiedOn = DateTime.UtcNow;

            // Act
            var page = new Pages
            {
                Id = "64a1b2c3d4e5f6789abcdef0",
                DocumentId = documentId,
                ModuleId = 123,
                ModuleName = "Financial Analysis",
                Items = "Revenue, Expenses, Net Income",
                CreatedOn = createdOn,
                CreatedBy = 456,
                ModifiedOn = modifiedOn,
                ModifiedBy = 789,
                IsDeleted = true
            };

            // Assert
            Assert.Equal("64a1b2c3d4e5f6789abcdef0", page.Id);
            Assert.Equal(documentId, page.DocumentId);
            Assert.Equal(123, page.ModuleId);
            Assert.Equal("Financial Analysis", page.ModuleName);
            Assert.Equal("Revenue, Expenses, Net Income", page.Items);
            Assert.Equal(createdOn, page.CreatedOn);
            Assert.Equal(456, page.CreatedBy);
            Assert.Equal(modifiedOn, page.ModifiedOn);
            Assert.Equal(789, page.ModifiedBy);
            Assert.True(page.IsDeleted);
        }

        [Fact]
        public void Pages_WithEmptyStrings_ShouldAcceptEmptyValues()
        {
            // Act
            var page = new Pages
            {
                Id = string.Empty,
                ModuleName = string.Empty,
                Items = string.Empty
            };

            // Assert
            Assert.Equal(string.Empty, page.Id);
            Assert.Equal(string.Empty, page.ModuleName);
            Assert.Equal(string.Empty, page.Items);
        }

        [Fact]
        public void Pages_WithNullId_ShouldAcceptNull()
        {
            // Act
            var page = new Pages { Id = null };

            // Assert
            Assert.Null(page.Id);
        }

        [Fact]
        public void Pages_WithNegativeModuleId_ShouldAcceptNegativeValues()
        {
            // Act
            var page = new Pages { ModuleId = -123 };

            // Assert
            Assert.Equal(-123, page.ModuleId);
        }

        [Fact]
        public void Pages_WithLargeModuleId_ShouldAcceptLargeValues()
        {
            // Act
            var page = new Pages { ModuleId = 999999 };

            // Assert
            Assert.Equal(999999, page.ModuleId);
        }

        [Fact]
        public void Pages_WithLongStrings_ShouldAcceptLongValues()
        {
            // Arrange
            var longModuleName = new string('A', 1000);
            var longItems = new string('B', 2000);

            // Act
            var page = new Pages
            {
                ModuleName = longModuleName,
                Items = longItems
            };

            // Assert
            Assert.Equal(longModuleName, page.ModuleName);
            Assert.Equal(longItems, page.Items);
        }

        [Fact]
        public void Pages_InheritsFromBaseEntity_ShouldHaveBaseEntityProperties()
        {
            // Act
            var page = new Pages();

            // Assert - Verify it's a BaseEntity
            Assert.IsAssignableFrom<BaseEntity>(page);
        }

        [Fact]
        public void Pages_WithEmptyDocumentId_ShouldAcceptEmptyGuid()
        {
            // Act
            var page = new Pages { DocumentId = Guid.Empty };

            // Assert
            Assert.Equal(Guid.Empty, page.DocumentId);
        }
    }
}