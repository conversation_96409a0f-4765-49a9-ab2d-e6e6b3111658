﻿using Infrastructure.DTOS.NonControllerDto;

namespace DataIngestionService.IServices
{
    public interface ISpecificKpiTransformationService
    {
        Task<SpecificDto> TransformDsSpecificToSpecific(string dsSpecificJson, Guid processId);
        Task<SpecificDto> GetSpecificKpiDocumentByProcessId(Guid processId);
        Task<string> UpdateSpecificKpiDocument(SpecificDto specificDto, Guid processId, int userId);
    }
}
