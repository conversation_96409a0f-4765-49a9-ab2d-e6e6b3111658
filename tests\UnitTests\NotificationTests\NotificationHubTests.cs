using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Moq;
using Notification.Hubs;
using Xunit;

namespace UnitTests.NotificationTests
{
    public class NotificationHubTests
    {
        private readonly NotificationHub _hub;
        private readonly Mock<IHubCallerClients> _mockClients;        private readonly Mock<ISingleClientProxy> _mockClientProxy;
        private readonly Mock<HubCallerContext> _mockHubContext;
        private readonly Mock<IGroupManager> _mockGroups;

        public NotificationHubTests()
        {
            _mockClients = new Mock<IHubCallerClients>();
            _mockClientProxy = new Mock<ISingleClientProxy>();
            _mockHubContext = new Mock<HubCallerContext>();
            _mockGroups = new Mock<IGroupManager>();

            _hub = new NotificationHub
            {
                Clients = _mockClients.Object,
                Context = _mockHubContext.Object,
                Groups = _mockGroups.Object
            };

            // Setup default mocks
            _mockClients.Setup(clients => clients.All).Returns(_mockClientProxy.Object);
            _mockClients.Setup(clients => clients.Client(It.IsAny<string>())).Returns(_mockClientProxy.Object);
            _mockHubContext.Setup(c => c.ConnectionId).Returns(Guid.NewGuid().ToString());
        }

        [Fact]
        public void NotificationHub_ShouldInheritFromHub()
        {
            // Assert
            Assert.True(_hub is Hub);
        }        [Fact]        public async Task OnConnectedAsync_ShouldSetupConnection()
        {
            // Arrange
            var connectionId = "test-connection-id";
            _mockHubContext.Setup(c => c.ConnectionId).Returns(connectionId);
            _mockClients.Setup(c => c.Caller).Returns(_mockClientProxy.Object);

            // Act
            await _hub.OnConnectedAsync();

            // Assert
            Assert.Equal(connectionId, _hub.Context.ConnectionId);
            _mockHubContext.Verify(c => c.ConnectionId, Times.Once);
        }

        [Fact]
        public async Task OnDisconnectedAsync_ShouldHandleException()
        {
            // Arrange
            var connectionId = "test-connection-id";
            var testException = new Exception("Test disconnect");
            _mockHubContext.Setup(c => c.ConnectionId).Returns(connectionId);

            // Act
            await _hub.OnDisconnectedAsync(testException);

            // Act & Assert
            // We just verify that the base implementation is called without error
            Assert.Equal(connectionId, _hub.Context.ConnectionId);
        }

        [Fact]
        public async Task SendMessage_ShouldReachAllClients()
        {
            // Arrange
            var message = "Test message";
            _mockClients.Setup(c => c.All).Returns(_mockClientProxy.Object);

            // Act
            await _hub.Clients.All.SendAsync("ReceiveNotification", message);

            // Assert
            _mockClientProxy.Verify(
                x => x.SendCoreAsync(
                    "ReceiveNotification",
                    It.Is<object[]>(o => o[0] as string == message),
                    default),
                Times.Once);
        }

        [Fact]
        public void Hub_ShouldHaveCorrectContext()
        {
            // Arrange
            var connectionId = "testConnection";
            var user = "testUser";
            _mockHubContext.Setup(c => c.ConnectionId).Returns(connectionId);
            _mockHubContext.Setup(c => c.UserIdentifier).Returns(user);

            // Act & Assert
            Assert.Equal(connectionId, _hub.Context.ConnectionId);
            Assert.Equal(user, _hub.Context.UserIdentifier);
            Assert.NotNull(_hub.Clients);
            Assert.NotNull(_hub.Groups);
        }
    }
}
