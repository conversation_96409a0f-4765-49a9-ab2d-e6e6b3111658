﻿using Microsoft.AspNetCore.Http;

namespace Infrastructure.Contract
{
    public class UploadDto : DataIngestionData
    {
        public Guid Id { get; set; }
        public Guid ProcessId { get; set; } = Guid.NewGuid();
        public Guid TenantId { get; set; }
        #nullable enable
        public string? UpdateProcessId { get; set; } = string.Empty;
    }
    public class DataIngestionData
    {
        public int SourceId { get; set; }
        #nullable enable
        public int? CompanyId { get; set; }
    
        public int FeatureId { get; set; }
      
        public List<FilePageDetails> Files { get; set; } = new List<FilePageDetails>();

        public int? FundId { get; set; }
        public string? CompanyIssuers { get; set; }
        public string? FundKpiDetails { get; set; }
        public required string ExtractionType { get; set; }
    }
    public class FilePageDetails
    {
        public required IFormFile File { get; set; }
        #nullable enable
        public string? Errors { get; set; } = string.Empty;
    }
   #nullable enable
    public class UpdateUploadDto : DataIngestionData
    {
        public string? ProcessId { get; set; }
    }    

}
