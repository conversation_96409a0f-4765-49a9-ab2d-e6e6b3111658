﻿using Microsoft.IdentityModel.Tokens;
using Serilog;

namespace API.Extensions
{
    public static class TokenSignatureExtension
    {
        public static async Task<JsonWebKey> GetJwk(IConfiguration config)
        {
            try {
                using var client = new HttpClient();
                var response = await client.GetAsync(config.GetValue<string>("IdentityServerConfig:jwk_uri"));
                if (response.IsSuccessStatusCode)
                {
                    string output = await response.Content.ReadAsStringAsync();
                    var jwks = new JsonWebKeySet(output);
                    var jwk = jwks.Keys.First();
                    return jwk;
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Error(ex, "AwsSecretsManagerHelper:GetSecret:Exception: {ErrorMessage}", ex.Message);
            }
            return new JsonWebKey();
        }
    }
}
