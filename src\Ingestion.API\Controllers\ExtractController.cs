﻿using DataIngestionService.IServices;
using Infrastructure.DTOS.Mappers;
using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Persistence.MongoDb;
namespace API.Controllers
{
    [ApiController]
    [Route("api")]
    [Authorize(Policy = JwtBearerDefaults.AuthenticationScheme)]
    public class ExtractController(IExtract extractService, ILogger<ExtractController> logger, IFinancialsRepository financialsRepository,IHelperService helperService, ISpecificKpiTransformationService specificKpi, IMongoDb mongoDBSevice) : ControllerBase
    {
        private readonly IExtract _extractService = extractService;
        private readonly ILogger<ExtractController> _logger = logger;
        private readonly IFinancialsRepository _financialsRepository = financialsRepository;
        private readonly IHelperService _helperService = helperService;
        private readonly ISpecificKpiTransformationService _specificKpi = specificKpi;
        private readonly IMongoDb _mongoDBService = mongoDBSevice;
        [HttpGet("Details/{processId}")]
        public async Task<IActionResult> Details(Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            _logger.LogInformation("Retrieving metadata for ProcessId: {ProcessId}", processId);
            var result = await _financialsRepository.GetMetaDataByJobId(processId);
            _logger.LogInformation("Successfully retrieved metadata for ProcessId: {ProcessId}", processId);
            return Ok(result);
        }

        [HttpPost("classifier-data/{processId}")]
        public async Task<IActionResult> AddOrUpdateClassifier([FromBody] ClassifierRequestDto classifierRequestDto, Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            _logger.LogInformation("Adding or updating classifier data for ProcessId: {ProcessId}", processId);
            var suggestionResponse = ClassifierMapper.MapToTableSuggestionResponse(classifierRequestDto);
            int userId = await _helperService.GetCurrentUserId(User);
            var result = await _extractService.AddOrUpdateClassifierData(suggestionResponse, processId,userId);
            _logger.LogInformation("Successfully added or updated classifier data for ProcessId: {ProcessId}", processId);
            return Ok(new { Id=result??string.Empty});
        }
        
        [HttpGet("classifier-info/{processId}")]
        public async Task<IActionResult> GetClassifierByProcessId(Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            if (processId == Guid.Empty)
            {
                _logger.LogWarning("Attempted to get classifier with empty ProcessId");
                return BadRequest("Process ID cannot be empty.");
            }            
            _logger.LogInformation("Retrieving classifier data for ProcessId: {ProcessId}", processId);
            var classifierData = await _extractService.GetClassifierDataByProcessIdAsync(processId);
            
            if (classifierData == null)
            {
                _logger.LogWarning("No classifier data found for ProcessId: {ProcessId}", processId);
                return NotFound($"No classifier data found for process ID: {processId}");
            }
            
            var classifierRequestDto = ClassifierMapper.MapToClassifierRequestDto(classifierData.TableSuggestionResponse);
            _logger.LogInformation("Successfully retrieved classifier data for ProcessId: {ProcessId}", processId);
            return Ok(classifierRequestDto);
        }
        [HttpPost("update-financials/{processId}")]
        public async Task<IActionResult> UpdateFinancials([FromBody] FinancialsData requestData, Guid processId)
        {
            try
            {
                if (processId == Guid.Empty)
                {
                    _logger.LogWarning("Attempted to update financials with empty ProcessId");
                    return BadRequest("Process ID cannot be empty.");
                }
                _logger.LogInformation("Updating financials for ProcessId: {ProcessId}", processId);
                bool response = await _extractService.UpdateFinancials(requestData, processId);
                _logger.LogInformation("Financials updated successfully for ProcessId: {ProcessId},Response: {Response}", processId, response);
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating financials for ProcessId: {ProcessId}. Error: {ErrorMessage}", processId, ex.Message);
                return BadRequest(ex.Message);
            }
        }
        [HttpGet("specific-kpi-document/{processId:guid}")]
        public async Task<IActionResult> GetSpecificKpiDocument(Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var document = await _specificKpi.GetSpecificKpiDocumentByProcessId(processId);
            if (document == null)
            {
                return NotFound($"No KPI document found for processId: {processId}");
            }
            return Ok(document);
        }
        [HttpPost("update-specific-kpi-document/{processId}")]
        public async Task<IActionResult> UpdateSpecificKpiDocumentApi([FromBody] SpecificDto specificDto, Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            int userId = await _helperService.GetCurrentUserId(User);
            var result = await _specificKpi.UpdateSpecificKpiDocument(specificDto, processId, userId);
            return Ok(new { Id = result });
        }
        [HttpGet("issuer-details/{processId}")]
        public async Task<IActionResult> GetIssuerDetails(Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            return Ok(await _mongoDBService.GetIssuerDetails(processId));
        }
    }
}
