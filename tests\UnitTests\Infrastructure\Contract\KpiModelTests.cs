using Infrastructure.Contract;
using Xunit;

namespace UnitTests.Infrastructure.Contract
{
    public class KpiModelTests
    {
        [Fact]
        public void KpiModel_DefaultValues_ShouldBeSetCorrectly()
        {
            // Act
            var kpiModel = new KpiModel();

            // Assert
            Assert.Equal(0, kpiModel.PortfolioCompanyId);
            Assert.Equal(0, kpiModel.KpiId);
            Assert.Null(kpiModel.KpiName);
            Assert.Null(kpiModel.ParentKPIId);
            Assert.Null(kpiModel.DisplayOrder);
            Assert.False(kpiModel.IsMapped);
            Assert.False(kpiModel.IsHeader);
            Assert.False(kpiModel.IsBoldKPI);
            Assert.Equal(0, kpiModel.MappingKPIId);
            Assert.Null(kpiModel.KpiInfo);
            Assert.False(kpiModel.IsExtraction);
            Assert.Null(kpiModel.Synonym);
            Assert.Null(kpiModel.Definition);
            Assert.Equal(0, kpiModel.MethodologyId);
            Assert.Equal(0, kpiModel.ModuleId);
            Assert.Equal(0, kpiModel.FundId);
        }

        [Fact]
        public void KpiModel_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var kpiModel = new KpiModel
            {
                PortfolioCompanyId = 123,
                KpiId = 456,
                KpiName = "Revenue Growth",
                ParentKPIId = 789,
                DisplayOrder = 1,
                IsMapped = true,
                IsHeader = true,
                IsBoldKPI = true,
                MappingKPIId = 999,
                KpiInfo = "Revenue growth information",
                IsExtraction = true,
                Synonym = "Revenue Increase",
                Definition = "Year over year revenue growth percentage",
                MethodologyId = 111,
                ModuleId = 222,
                FundId = 333
            };

            // Assert
            Assert.Equal(123, kpiModel.PortfolioCompanyId);
            Assert.Equal(456, kpiModel.KpiId);
            Assert.Equal("Revenue Growth", kpiModel.KpiName);
            Assert.Equal(789, kpiModel.ParentKPIId);
            Assert.Equal(1, kpiModel.DisplayOrder);
            Assert.True(kpiModel.IsMapped);
            Assert.True(kpiModel.IsHeader);
            Assert.True(kpiModel.IsBoldKPI);
            Assert.Equal(999, kpiModel.MappingKPIId);
            Assert.Equal("Revenue growth information", kpiModel.KpiInfo);
            Assert.True(kpiModel.IsExtraction);
            Assert.Equal("Revenue Increase", kpiModel.Synonym);
            Assert.Equal("Year over year revenue growth percentage", kpiModel.Definition);
            Assert.Equal(111, kpiModel.MethodologyId);
            Assert.Equal(222, kpiModel.ModuleId);
            Assert.Equal(333, kpiModel.FundId);
        }

        [Fact]
        public void KpiModel_WithNullableProperties_ShouldHandleNullValues()
        {
            // Act
            var kpiModel = new KpiModel
            {
                ParentKPIId = null,
                DisplayOrder = null,
                KpiName = null,
                KpiInfo = null,
                Synonym = null,
                Definition = null
            };

            // Assert
            Assert.Null(kpiModel.ParentKPIId);
            Assert.Null(kpiModel.DisplayOrder);
            Assert.Null(kpiModel.KpiName);
            Assert.Null(kpiModel.KpiInfo);
            Assert.Null(kpiModel.Synonym);
            Assert.Null(kpiModel.Definition);
        }
    }
}