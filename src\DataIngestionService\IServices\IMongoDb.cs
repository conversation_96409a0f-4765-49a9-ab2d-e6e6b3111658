﻿using Infrastructure.Contract;
using Persistence.Models;

namespace DataIngestionService.IServices
{
    public interface IMongoDb
    {
        Task<string> AddIssuerDetails(UploadDto request, int userId);
        Task<string> AddSubPageDetails(UploadDto request, int userId);
        Task<bool> GetClassifiersByProcessId(List<Guid> processIds);
        Task<List<IssuerKPIModel>> GetIssuerDetails(Guid processId);
        Task<List<SubPageFieldsModel>> GetFundKpiDetails(Guid processId);
    }
}
