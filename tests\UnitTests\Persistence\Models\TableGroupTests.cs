using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class TableGroupTests
    {
        [Fact]
        public void TableGroup_DefaultInitialization_ShouldHaveNullValues()
        {
            // Act
            var tableGroup = new TableGroup();

            // Assert
            Assert.Null(tableGroup.Label);
            Assert.Null(tableGroup.Tables);
        }

        [Fact]
        public void TableGroup_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var tables = new List<Table> 
            { 
                new Table { Name = "Table 1" }, 
                new Table { Name = "Table 2" } 
            };

            // Act
            var tableGroup = new TableGroup
            {
                Label = "Financial Tables",
                Tables = tables
            };

            // Assert
            Assert.Equal("Financial Tables", tableGroup.Label);
            Assert.Equal(tables, tableGroup.Tables);
            Assert.Equal(2, tableGroup.Tables.Count);
        }

        [Fact]
        public void TableGroup_WithEmptyLabel_ShouldAcceptEmptyString()
        {
            // Act
            var tableGroup = new TableGroup { Label = string.Empty };

            // Assert
            Assert.Equal(string.Empty, tableGroup.Label);
        }

        [Fact]
        public void TableGroup_WithEmptyTablesList_ShouldAcceptEmptyCollection()
        {
            // Act
            var tableGroup = new TableGroup { Tables = new List<Table>() };

            // Assert
            Assert.NotNull(tableGroup.Tables);
            Assert.Empty(tableGroup.Tables);
        }

        [Fact]
        public void TableGroup_WithManyTables_ShouldAcceptLargeCollection()
        {
            // Arrange
            var manyTables = Enumerable.Range(1, 100).Select(i => new Table 
            { 
                Name = $"Table {i}",
                Id = $"table-{i}"
            }).ToList();

            // Act
            var tableGroup = new TableGroup { Tables = manyTables };

            // Assert
            Assert.Equal(100, tableGroup.Tables.Count);
            Assert.Equal("Table 1", tableGroup.Tables.First().Name);
            Assert.Equal("Table 100", tableGroup.Tables.Last().Name);
        }

        [Fact]
        public void TableGroup_WithLongLabel_ShouldAcceptLongString()
        {
            // Arrange
            var longLabel = new string('A', 1000);

            // Act
            var tableGroup = new TableGroup { Label = longLabel };

            // Assert
            Assert.Equal(longLabel, tableGroup.Label);
        }

        [Fact]
        public void TableGroup_WithSingleTable_ShouldAcceptSingleItem()
        {
            // Arrange
            var singleTable = new Table { Name = "Single Table", Id = "single-1" };

            // Act
            var tableGroup = new TableGroup 
            { 
                Label = "Single Group",
                Tables = new List<Table> { singleTable }
            };

            // Assert
            Assert.Equal("Single Group", tableGroup.Label);
            Assert.Single(tableGroup.Tables);
            Assert.Equal("Single Table", tableGroup.Tables[0].Name);
        }

        [Fact]
        public void TableGroup_WithNullTables_ShouldAcceptNull()
        {
            // Act
            var tableGroup = new TableGroup 
            { 
                Label = "Test Group",
                Tables = null 
            };

            // Assert
            Assert.Equal("Test Group", tableGroup.Label);
            Assert.Null(tableGroup.Tables);
        }
    }
}