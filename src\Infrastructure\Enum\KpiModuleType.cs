﻿using System.ComponentModel;

namespace Infrastructure.Enum
{
    public enum KpiModuleType
    {
        [Description("TradingRecords")]
        TradingRecords = 1,
        [Description("CreditKPI")]
        CreditKPI = 2,
        [Description("Operational")]
        Operational = 3,
        [Description("Investment")]
        Investment = 4,
        [Description("Company")]
        Company = 5,
        [Description("Impact")]
        Impact = 6,
        [Description("ProfitAndLoss")]
        ProfitAndLoss = 7,
        [Description("BalanceSheet")]
        BalanceSheet = 8,
        [Description("CashFlow")]
        CashFlow = 9,
        CapTable1 = 11,
        CapTable2 = 12,
        CapTable3 = 13,
        CapTable4 = 14,
        CapTable5 = 15,
        MonthlyReport = 16,
        [Description("CustomTable1")]
        CustomTable1 = 17,
        [Description("CustomTable2")]
        CustomTable2 = 18,
        [Description("CustomTable3")]
        CustomTable3 = 19,
        [Description("CustomTable4")]
        CustomTable4 = 20,
        [Description("OtherKPI1")]
        OtherKPI1 = 21,
        [Description("OtherKPI2")]
        OtherKPI2 = 22,
        [Description("OtherKPI3")]
        OtherKPI3 = 23,
        [Description("OtherKPI4")]
        OtherKPI4 = 24,
        [Description("OtherKPI5")]
        OtherKPI5 = 25,
        [Description("OtherKPI6")]
        OtherKPI6 = 26,
        [Description("OtherKPI7")]
        OtherKPI7 = 27,
        [Description("OtherKPI8")]
        OtherKPI8 = 28,
        [Description("OtherKPI9")]
        OtherKPI9 = 29,
        [Description("OtherKPI10")]
        OtherKPI10 = 30,
        [Description("FundFinancials")]
        FundFinancials = 1001

    }
    public enum PageConfigurationSubFeature
    {
        [Description("Static Information")]
        StaticInformation = 1
    }
    public enum PageSubFieldsDataTypes
    {
        [Description(Constants.DataTypeDefault)]
        Default = 0,
        [Description(Constants.DataTypeFreeText)]
        FreeText = 1,
        [Description(Constants.DataTypeNumber)]
        Number = 2,
        [Description(Constants.DataTypeCurrency)]
        Currency = 3,
        [Description(Constants.DataTypePercentage)]
        Percentage = 4,
        [Description(Constants.DataTypeMultiple)]
        Multiple = 5,
        [Description(Constants.DataTypeDate)]
        Date = 6,
        [Description(Constants.DataTypeList)]
        List = 7
    }
    public static class Constants
    {
        public const string DataTypeDefault = "Default";
        public const string DataTypeFreeText = "Free Text";
        public const string DataTypeNumber = "Number";
        public const string DataTypeDate = "Date";
        public const string DataTypeList = "List";
        public const string DataTypeMultiple = "Multiple";
        public const string DataTypePercentage = "Percentage";
        public const string DataTypeCurrency = "Currency Value";
    }
}
