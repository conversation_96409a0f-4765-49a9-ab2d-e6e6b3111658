using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Persistence.Models.Specific
{
    public class SpecificKpiDocument : BaseEntity
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        
        public Guid ProcessId { get; set; }
        public Guid JobId { get; set; }
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public string CurrencyCode { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public List<SpecificFile> Files { get; set; } = [];
        public List<CustomSection> CustomSections { get; set; } = new();
    }
}
