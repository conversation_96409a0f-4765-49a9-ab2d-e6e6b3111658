using AWSS3;
using Xunit;

namespace UnitTests.AWSS3Tests
{
    public class S3SettingTests
    {
        [Fact]
        public void S3Setting_Properties_ShouldSetAndGetCorrectly()
        {
            // Arrange & Act
            var s3Setting = new S3Setting
            {
                BucketName = "test-bucket",
                Region = "us-east-1", 
                FolderPrefix = "documents/",
                KeyPrefix = "ingestion-"
            };

            // Assert
            Assert.Equal("test-bucket", s3Setting.BucketName);
            Assert.Equal("us-east-1", s3Setting.Region);
            Assert.Equal("documents/", s3Setting.FolderPrefix);
            Assert.Equal("ingestion-", s3Setting.KeyPrefix);
        }

        [Fact]
        public void S3Setting_WithEmptyValues_ShouldAcceptEmptyStrings()
        {
            // Arrange & Act
            var s3Setting = new S3Setting
            {
                BucketName = "",
                Region = "",
                FolderPrefix = "",
                KeyPrefix = ""
            };

            // Assert
            Assert.Equal("", s3Setting.BucketName);
            Assert.Equal("", s3Setting.Region);
            Assert.Equal("", s3Setting.FolderPrefix);
            Assert.Equal("", s3Setting.KeyPrefix);
        }
    }
}