using Infrastructure.Contract;
using Xunit;

namespace UnitTests.Infrastructure.Contract
{
    public class CreateJobsTests
    {
        [Fact]
        public void CreateJobs_PropertiesInitialization_ShouldSetCorrectly()
        {
            // Arrange
            var id = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var tenantId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var parentJobId = Guid.NewGuid();

            // Act
            var createJobs = new CreateJobs
            {
                Id = id,
                JobId = jobId,
                ProcessId = processId,
                TenantId = tenantId,
                StatusId = statusId,
                ParentJobId = parentJobId
            };

            // Assert
            Assert.Equal(id, createJobs.Id);
            Assert.Equal(jobId, createJobs.JobId);
            Assert.Equal(processId, createJobs.ProcessId);
            Assert.Equal(tenantId, createJobs.TenantId);
            Assert.Equal(statusId, createJobs.StatusId);
            Assert.Equal(parentJobId, createJobs.ParentJobId);
        }

        [Fact]
        public void CreateJobs_DefaultValues_ShouldBeEmptyGuids()
        {
            // Act
            var createJobs = new CreateJobs();

            // Assert
            Assert.Equal(Guid.Empty, createJobs.Id);
            Assert.Equal(Guid.Empty, createJobs.JobId);
            Assert.Equal(Guid.Empty, createJobs.ProcessId);
            Assert.Equal(Guid.Empty, createJobs.TenantId);
            Assert.Equal(Guid.Empty, createJobs.StatusId);
            Assert.Equal(Guid.Empty, createJobs.ParentJobId);
        }
    }
}