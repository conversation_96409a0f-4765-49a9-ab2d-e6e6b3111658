using Amazon.S3.Model;
using AWSS3.Interfaces;
using DataIngestionService.Constants;
using DataIngestionService.IServices;
using Infrastructure.Contract;
using Infrastructure.Contract.BlobStorage;
using Microsoft.Extensions.Logging;
using Persistence.Models;
using Persistence.UnitOfWork;

namespace DataIngestionService.Services
{
    /// <summary>
    /// Service for handling file operations such as retrieving, downloading and deleting files from S3 storage
    /// </summary>
    public class FileService(IAWSS3Library awsS3Library, ILogger<FileService> logger, IUnitOfWork unitOfWork, IExtract extractService) : IFileService
    {
        private readonly IAWSS3Library _awsS3Library = awsS3Library;
        private readonly ILogger<FileService> _logger = logger;
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private readonly IExtract _extractService=extractService;

        /// <summary>
        /// Retrieves a document from blob storage using its key
        /// </summary>
        /// <param name="query">The request containing the file key</param>
        /// <returns>A response with the file content and metadata</returns>
        /// <exception cref="ArgumentException">Thrown when the file key is empty</exception>
        /// <exception cref="InvalidOperationException">Thrown when an error occurs during file retrieval</exception>
        public async Task<BlobStorageResponse> GetDocumentFromBlobStorage(BlobStorageRequest query)
        {
            if (string.IsNullOrEmpty(query.Key))
                throw new ArgumentException("File key cannot be empty");

            try
            {
                string fileName = ExtractFileName(query.Key);
                string fileContent = await _awsS3Library.GetFileAsync(query.Key);
                var document = await GetDocumentById(fileName);
                return CreateBlobStorageResponse(fileContent, document.FileName, document.S3Path);
            }
            catch (Exception ex)
            {
                var contextMessage = $"Error downloading file with key: {query.Key}";
                _logger.LogError(ex, contextMessage);
                throw new InvalidOperationException(contextMessage, ex);
            }
        }

        /// <summary>
        /// Downloads a file object directly from S3 storage
        /// </summary>
        /// <param name="keyPath">The S3 key path of the file to download</param>
        /// <returns>The S3 GetObjectResponse containing the file stream and metadata</returns>
        public async Task<GetObjectResponse?> DownloadFileObject(string keyPath)
        {
            return await _awsS3Library.DownloadFileObject(keyPath);
        }

        /// <summary>
        /// Extracts the file name from an S3 key path
        /// </summary>
        /// <param name="key">The S3 key path</param>
        /// <returns>The extracted file name</returns>
        private static string ExtractFileName(string key)
        {
            string[] pathParts = key.Split('/');
            string fileName = pathParts.Length > 0 ? pathParts[^1] : key;
            return fileName.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase) ? Path.GetFileNameWithoutExtension(fileName) : fileName;
        }

        /// <summary>
        /// Retrieves a document from the database by its ID
        /// </summary>
        /// <param name="id">The document ID</param>
        /// <returns>The document entity</returns>
        /// <exception cref="InvalidOperationException">Thrown when the document is not found</exception>
        private async Task<DataIngestionDocuments> GetDocumentById(string id)
        {
            var document = await _unitOfWork.DataIngestionDocumentRepository
                .GetFirstOrDefaultAsync(x => x.Id == Guid.Parse(id));

            if (document == null)
                throw new InvalidOperationException($"Document not found with ID: {id}");

            return document;
        }

        /// <summary>
        /// Creates a blob storage response object with the file content and metadata
        /// </summary>
        /// <param name="fileContent">The file content as a string</param>
        /// <param name="fileName">The file name</param>
        /// <param name="fileKey">The file key in S3</param>
        /// <returns>A BlobStorageResponse object</returns>
        private static BlobStorageResponse CreateBlobStorageResponse(string fileContent, string fileName, string fileKey)
        {
            return new BlobStorageResponse
            {
                File = fileContent,
                FileName = fileName,
                FileKey = fileKey,
                FileId= Path.GetFileNameWithoutExtension(Path.GetFileName(fileKey))
            };
        }

        /// <summary>
        /// Deletes a file from S3 storage and updates related database records
        /// </summary>
        /// <param name="deleteDocument">The document to delete</param>
        /// <param name="userId">The ID of the user performing the deletion</param>
        /// <returns>True if deletion was successful, false otherwise</returns>
        public async Task<bool> DeleteFile(DeleteDocument deleteDocument,int userId)
        {
            var modelDocuments = await _unitOfWork.DataIngestionDocumentRepository
                .GetFirstOrDefaultAsync(x => x.Id == deleteDocument.Id && !x.IsDeleted);

            if (modelDocuments == null)
                return false;
            Guid processId = modelDocuments.ProcessId;
            await DeleteDocument(modelDocuments, userId);
            var mappingDetails = await GetMappingDetailsByProcessId(processId);
            if (mappingDetails.ExtractionType == ApiConstants.AsIsExtraction)
            {
                var job = await _unitOfWork.JobsRepository
                    .GetFirstOrDefaultAsync(x => x.ProcessId == processId && !x.IsDeleted);
                await DeleteJob(job, userId);
            }
            await _extractService.DeleteClassifierDataByProcessIdAsync(processId, userId);
            await _awsS3Library.DeleteFileAsync(deleteDocument.Key);
            return true;
        }

        /// <summary>
        /// Marks a document as deleted in the database
        /// </summary>
        /// <param name="deleteDocument">The document to mark as deleted</param>
        /// <param name="userId">The ID of the user performing the deletion</param>
        public async Task DeleteDocument(DataIngestionDocuments deleteDocument,int userId)
        {
            if (deleteDocument != null)
            {
                deleteDocument.IsDeleted = true;
                deleteDocument.ModifiedOn = DateTime.UtcNow;
                deleteDocument.ModifiedBy = userId;
                _unitOfWork.DataIngestionDocumentRepository.Update(deleteDocument);
                await _unitOfWork.SaveAsync();
            }
        }

        /// <summary>
        /// Marks a job as deleted in the database
        /// </summary>
        /// <param name="job">The job to mark as deleted</param>
        /// <param name="userId">The ID of the user performing the deletion</param>
        public async Task DeleteJob(Jobs job, int userId)
        {
            if (job != null)
            {
                job.IsDeleted = true;
                job.ModifiedOn = DateTime.UtcNow;
                job.ModifiedBy = userId;
                _unitOfWork.JobsRepository.Update(job);
                await _unitOfWork.SaveAsync();
            }
        }

        /// <summary>
        /// Retrieves documents associated with a specific process ID
        /// </summary>
        /// <param name="processId">The process ID</param>
        /// <returns>A response with the file content and metadata</returns>
        public async Task<BlobStorageResponse> GetDocumentsByProcessId(Guid processId)
        {
            try
            {
                string environment = $"{_awsS3Library.GetBucketDetails().KeyPrefix}/";
                var documents = await GetActiveDocumentsByProcessId(processId);
                documents.S3Path = $"{environment}{documents.S3Path}";
                return await DownloadDocuments(documents);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving documents for process ID: {ProcessId}", processId);
                throw;
            }
        }

        /// <summary>
        /// Retrieves active (non-deleted) documents associated with a specific process ID
        /// </summary>
        /// <param name="processId">The process ID</param>
        /// <returns>The document entity</returns>
        private async Task<DataIngestionDocuments> GetActiveDocumentsByProcessId(Guid processId)
        {
            return await _unitOfWork.DataIngestionDocumentRepository
                .GetFirstOrDefaultAsync(x => x.ProcessId == processId && !x.IsDeleted);
        }

        /// <summary>
        /// Retrieves mapping details associated with a specific process ID
        /// </summary>
        /// <param name="processId">The process ID</param>
        /// <returns>The mapping details entity</returns>
        private async Task<DIMappingDocumentsDetails> GetMappingDetailsByProcessId(Guid processId)
        {
            return await _unitOfWork.DIMappingDocumentsDetailsRepository
                .GetFirstOrDefaultAsync(x => x.ProcessId == processId && !x.IsDeleted);
        }

        /// <summary>
        /// Downloads documents from S3 storage
        /// </summary>
        /// <param name="document">The document entity containing the S3 path</param>
        /// <returns>A response with the file content and metadata</returns>
        private async Task<BlobStorageResponse> DownloadDocuments(DataIngestionDocuments document)
        {
            var responses = new BlobStorageResponse();

            try
            {
                string fileContent = await _awsS3Library.GetFileAsync(document.S3Path);
                responses=CreateBlobStorageResponse(fileContent, document.FileName, document.S3Path);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file: {Key}", document.S3Path);
            }
            return responses;
        }
    }
}