using System;
using System.Collections.Generic;
using Notification.Models;
using Xunit;

namespace UnitTests.NotificationTests
{
    public class NotificationMessageTests
    {
        [Fact]
        public void NotificationMessage_DefaultValues_ShouldBeCorrect()
        {
            // Act
            var message = new NotificationMessage();

            // Assert
            Assert.Equal(string.Empty, message.Title);
            Assert.Equal(string.Empty, message.Message);
            Assert.Null(message.EntityId);
            Assert.Null(message.EntityType);
            Assert.NotEqual(default, message.Timestamp);
            Assert.NotNull(message.Data);
            Assert.Empty(message.Data);
        }

        [Fact]
        public void NotificationMessage_WithValues_ShouldSetPropertiesCorrectly()
        {
            // Arrange
            var title = "Test Title";
            var message = "Test Message";
            var entityId = "test123";
            var entityType = "document";
            var type = NotificationType.Info;
            var jobStatuses = new List<JobStatus>
            {
                new JobStatus { JobId = Guid.NewGuid(), StatusId = Guid.NewGuid() }
            };

            // Act
            var notification = new NotificationMessage
            {
                Title = title,
                Message = message,
                EntityId = entityId,
                EntityType = entityType,
                Type = type,
                Data = jobStatuses
            };

            // Assert
            Assert.Equal(title, notification.Title);
            Assert.Equal(message, notification.Message);
            Assert.Equal(entityId, notification.EntityId);
            Assert.Equal(entityType, notification.EntityType);
            Assert.Equal(type, notification.Type);
            Assert.Equal(jobStatuses, notification.Data);
        }

        [Fact]
        public void JobStatus_ShouldSetAndGetProperties()
        {
            // Arrange
            var jobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();

            // Act
            var jobStatus = new JobStatus
            {
                JobId = jobId,
                StatusId = statusId
            };

            // Assert
            Assert.Equal(jobId, jobStatus.JobId);
            Assert.Equal(statusId, jobStatus.StatusId);
        }
    }
}
