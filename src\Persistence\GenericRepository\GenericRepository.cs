﻿using Microsoft.EntityFrameworkCore;
using Persistence.DBEntitiesContext;
using System.Linq.Expressions;
namespace Persistence.GenericRepository
{
    /// <summary>
    /// Generic Repository class for Entity Operations
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    public class GenericRepository<TEntity> : IGenericRepository<TEntity> where TEntity : class
    {
        #region Private member variables...
        internal DBEntities Context;
        internal DbSet<TEntity> DbSet;
        #endregion

        #region Public Constructor...
        /// <summary>
        /// Public Constructor,initializes privately declared local variables.
        /// </summary>
        /// <param name="context"></param>
        public GenericRepository(DBEntities context)
        {
            this.Context = context;
            this.DbSet = context.Set<TEntity>();
            this.Context.Database.SetCommandTimeout(10000);
        }
        #endregion

        #region Public member methods...

        /// <summary>
        /// generic Get method for Entities
        /// </summary>
        /// <returns></returns>
        public virtual IEnumerable<TEntity> Get()
        {
            IQueryable<TEntity> query = DbSet.AsNoTracking();
            return query.ToList();
        }
        public IQueryable<TEntity> GetQueryable()
        {
            return DbSet.AsNoTracking();
        }
        /// <summary>
        /// generic get method , fetches data for the entities on the basis of condition.
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public TEntity Get(Func<TEntity, Boolean> where)
        {
            return DbSet.FirstOrDefault(where);
        }

        /// <summary>
        /// Generic get method on the basis of id for Entities.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public virtual TEntity GetByID(object id)
        {
            return DbSet.Find(id);
        }

        /// <summary>
        /// generic method to get many record on the basis of a condition.
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public virtual IEnumerable<TEntity> GetMany(Func<TEntity, bool> where)
        {
            return DbSet.AsNoTracking().Where(where).ToList();
        }

        /// <summary>
        /// generic method to get many record on the basis of a condition but query able.
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public virtual IQueryable<TEntity> GetManyQueryable(Func<TEntity, bool> where)
        {
            return DbSet.AsNoTracking().Where(where).AsQueryable();
        }
        public virtual IQueryable<TEntity> GetActiveRecords(Func<TEntity, bool> where = null)
        {
            if (where == null)
            {
                where = entity => true; // Default to a function that always returns true
            }
            var query = DbSet.AsNoTracking().Where(where).AsQueryable();

            query = AddPropertyFilter(query, "IsDeleted", false);
            query = AddPropertyFilter(query, "IsActive", true);

            return query;
        }

        private static IQueryable<TEntity> AddPropertyFilter(IQueryable<TEntity> query, string propertyName, bool expectedValue)
        {
            var property = typeof(TEntity).GetProperty(propertyName);
            if (property != null && property.PropertyType == typeof(bool))
            {
                var parameter = Expression.Parameter(typeof(TEntity), "e");
                var propertyAccess = Expression.Property(parameter, property);
                var constant = Expression.Constant(expectedValue);
                var equal = Expression.Equal(propertyAccess, constant);
                var lambda = Expression.Lambda<Func<TEntity, bool>>(equal, parameter);
                query = query.Where(lambda);
            }

            return query;
        }
        /// <summary>
        /// generic method to fetch all the records from db
        /// </summary>
        /// <returns></returns>
        public virtual IEnumerable<TEntity> GetAll()
        {
            return DbSet.AsNoTracking().ToList();
        }

        /// <summary>
        /// Inclue multiple
        /// </summary>
        /// <param name="predicate"></param>
        /// <param name="include"></param>
        /// <returns></returns>
        public IQueryable<TEntity> GetWithInclude(System.Linq.Expressions.Expression<Func<TEntity, bool>> predicate, params string[] include)
        {
            IQueryable<TEntity> query = this.DbSet.AsNoTracking();
            query = include.Aggregate(query, (current, inc) => current.Include(inc));
            return query.Where(predicate);
        }
        /// <summary>
        /// generic Insert method for the entities
        /// </summary>
        /// <param name="entity"></param>
        public virtual void Insert(TEntity entity)
        {
            DbSet.Add(entity);
        }
        /// <summary>
        /// generic Insert method for the bulk entities
        /// </summary>
        /// <param name="entity"></param>
        public virtual void InsertBulk(List<TEntity> entities)
        {
            DbSet.AddRange(entities);
        }
        /// <summary>
        /// generic Update method for the bulk entities
        /// </summary>
        /// <param name="entity"></param>
        public virtual void UpdateBulk(List<TEntity> entityToUpdate)
        {
            DbSet.UpdateRange(entityToUpdate);
        }

        /// <summary>
        /// Generic Delete method for the entities
        /// </summary>
        /// <param name="id"></param>
        public virtual void Delete(object id)
        {
            TEntity entityToDelete = DbSet.Find(id);
            Delete(entityToDelete);
        }

        /// <summary>
        /// Generic Delete method for the entities
        /// </summary>
        /// <param name="entityToDelete"></param>
        public virtual void Delete(TEntity entityToDelete)
        {
            if (Context.Entry(entityToDelete).State == EntityState.Detached)
            {
                DbSet.Attach(entityToDelete);
            }
            DbSet.Remove(entityToDelete);
        }


        /// <summary>
        /// generic delete method , deletes data for the entities on the basis of condition.
        /// </summary>
        /// <param name="where"></param>
        /// <returns></returns>
        public void Delete(Func<TEntity, Boolean> where)
        {
            IQueryable<TEntity> objects = DbSet.Where<TEntity>(where).AsQueryable();
            foreach (TEntity obj in objects)
                DbSet.Remove(obj);
        }

        /// <summary>
        /// Generic update method for the entities
        /// </summary>
        /// <param name="entityToUpdate"></param>
        public virtual void Update(TEntity entityToUpdate)
        {
            DbSet.Attach(entityToUpdate);
            Context.Entry(entityToUpdate).State = EntityState.Modified;
        }

        /// <summary>
        /// Generic method to check if entity exists
        /// </summary>
        /// <param name="primaryKey"></param>
        /// <returns></returns>
        public bool Exists(object primaryKey)
        {
            return DbSet.Find(primaryKey) != null;
        }

        /// <summary>
        /// Gets a single record by the specified criteria (usually the unique identifier)
        /// </summary>
        /// <param name="predicate">Criteria to match on</param>
        /// <returns>A single record that matches the specified criteria</returns>
        public TEntity GetSingle(Func<TEntity, bool> predicate)
        {
            return DbSet.AsNoTracking().Single<TEntity>(predicate);
        }

        /// <summary>
        /// The first record matching the specified criteria
        /// </summary>
        /// <param name="predicate">Criteria to match on</param>
        /// <returns>A single record containing the first record matching the specified criteria</returns>
        public TEntity GetFirst(Func<TEntity, bool> predicate)
        {
            return DbSet.AsNoTracking().First<TEntity>(predicate);
        }
        public TEntity GetFirstOrDefault(Func<TEntity, bool> predicate)
        {
            return DbSet.AsNoTracking().FirstOrDefault<TEntity>(predicate);
        }
        public async Task<TEntity> GetFirstOrDefaultAsync(Expression<Func<TEntity, bool>> predicate)
        {
            return await DbSet.AsNoTracking().FirstOrDefaultAsync(predicate);
        }
        public TEntity GetLastOrDefault(Func<TEntity, bool> predicate)
        {
            return DbSet.AsNoTracking().LastOrDefault<TEntity>(predicate);
        }
        public bool ExistsAny(Func<TEntity, bool> predicate)
        {
            return DbSet.AsNoTracking().Any<TEntity>(predicate);
        }
        public virtual async Task<IEnumerable<TEntity>> GetManyAsync(Func<TEntity, bool> where)
        {
            return await Task.FromResult(DbSet.AsNoTracking().Where(where).AsEnumerable());
        }
        public async Task<TEntity> AddAsyn(TEntity entity)
        {
            await DbSet.AddAsync(entity);
            return entity;
        }
        public async Task AddBulkAsyn(List<TEntity> entities)
        {
            await DbSet.AddRangeAsync(entities);
        }
        public async Task<int> CountAsync()
        {
            return await DbSet.AsNoTracking().CountAsync();
        }
        public async Task<int> CountAsync(Expression<Func<TEntity, bool>> match)
        {
            return await DbSet.AsNoTracking().Where(match).CountAsync();
        }
        public async Task<List<TEntity>> FindAllAsync(Expression<Func<TEntity, bool>> match)
        {
            return await DbSet.AsNoTracking().Where(match).ToListAsync();
        }
        public async Task<TEntity> FindFirstAsync(Expression<Func<TEntity, bool>> match)
        {
            return await DbSet.AsNoTracking().Where(match).FirstOrDefaultAsync();
        }
        public async Task<TEntity> FindSingleAsync(Expression<Func<TEntity, bool>> match)
        {
            return await DbSet.AsNoTracking().Where(match).SingleOrDefaultAsync();
        }
        public async Task<TEntity> FindLastAsync(Expression<Func<TEntity, bool>> match)
        {
            return await DbSet.AsNoTracking().Where(match).LastOrDefaultAsync();
        }
        public async Task<bool> ExistsAsyncAny(Expression<Func<TEntity, bool>> predicate)
        {
            return await DbSet.AsNoTracking().Where(predicate).AnyAsync();
        }
        public async Task<ICollection<TEntity>> FindByAsyn(Expression<Func<TEntity, bool>> predicate)
        {
            return await DbSet.AsNoTracking().Where(predicate).ToListAsync();
        }
        public async Task<List<TEntity>> GetAllAsyn()
        {
            return await DbSet.AsNoTracking().ToListAsync();
        }
        public async Task<int> SaveAsync()
        {
            return await Context.SaveChangesAsync();
        }
        #endregion
    }
}
