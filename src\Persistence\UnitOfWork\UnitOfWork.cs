using Microsoft.EntityFrameworkCore;
using Persistence.DBEntitiesContext;
using Persistence.GenericRepository;
using Persistence.Models;

namespace Persistence.UnitOfWork
{
    public partial class UnitOfWork(DBEntities dBEntities) : IDisposable, IUnitOfWork
    {
        #region Private member variables...
        private readonly DBEntities _context = dBEntities;
        private IGenericRepository<DataIngestionDocuments>? _DataIngestionDocumentRepository;
        private IGenericRepository<Jobs>? _JobsRepository;
        private IGenericRepository<Status>? _StatusRepository;
        private IGenericRepository<DIMappingDocumentsDetails>? _DIMappingDocumentsDetailsRepository;
        private IGenericRepository<PortfolioCompanyDetails>? _PortfoiloCompanyDetailsRepository;
        private IGenericRepository<UserDetails>? _UserDetailsRepository;
        private IGenericRepository<FundDetails>? _FundDetailsRepository;
        #endregion Private member variables...

        public bool AutoDetectChangesEnabled
        {
            get
            {
                return _context.ChangeTracker.AutoDetectChangesEnabled;
            }
            set
            {
                _context.ChangeTracker.AutoDetectChangesEnabled = value;
                if (value)
                {
                    _context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.TrackAll;
                }
                else
                {
                    _context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                }
            }
        }

        #region Public Repository Creation properties...
        public IGenericRepository<DataIngestionDocuments> DataIngestionDocumentRepository
        {
            get
            {
                return _DataIngestionDocumentRepository ??= new GenericRepository<DataIngestionDocuments>(_context);
            }
        }

        public IGenericRepository<Jobs> JobsRepository
        {
            get
            {
                return _JobsRepository ??= new GenericRepository<Jobs>(_context);
            }
        }

        public IGenericRepository<Status> StatusRepository
        {
            get
            {
                return _StatusRepository ??= new GenericRepository<Status>(_context);
            }
        }
        public IGenericRepository<DIMappingDocumentsDetails> DIMappingDocumentsDetailsRepository
        {
            get
            {
                return _DIMappingDocumentsDetailsRepository ??= new GenericRepository<DIMappingDocumentsDetails>(_context);
            }
        }
        public IGenericRepository<PortfolioCompanyDetails> PortfoiloCompanyDetailsRepository
        {
            get
            {
                return _PortfoiloCompanyDetailsRepository ??= new GenericRepository<PortfolioCompanyDetails>(_context);
            }
        }
        public IGenericRepository<UserDetails> UserDetailsRepository
        {
            get
            {
                return _UserDetailsRepository ??= new GenericRepository<UserDetails>(_context);
            }
        }
        public IGenericRepository<FundDetails> FundDetailsRepository
        {
            get
            {
                return _FundDetailsRepository ??= new GenericRepository<FundDetails>(_context);
            }
        }
        #endregion Public Repository Creation properties...

        #region Public member methods
        public int Save()
        {
            return _context.SaveChanges();
        }

        public async Task<int> SaveAsync()
        {
            return await _context.SaveChangesAsync();
        }
        #endregion

        #region Implementing IDiosposable...
        #region private dispose variable declaration...
        private bool disposed = false;
        #endregion

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed && disposing)
            {
                _context.Dispose();
            }
            disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}