using API.Controllers;
using DataIngestionService.IServices;
using Infrastructure.Contract;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.Models;
using System.Security.Claims;
using Xunit;

namespace UnitTests.Controllers
{
    public class JobsControllerTests
    {
        private readonly Mock<IJobsUpdateService> _mockJobsUpdateService;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly Mock<ILogger<JobsController>> _mockLogger;
        private readonly JobsController _controller;

        public JobsControllerTests()
        {
            _mockJobsUpdateService = new Mock<IJobsUpdateService>();
            _mockHelperService = new Mock<IHelperService>();
            _mockLogger = new Mock<ILogger<JobsController>>();

            _controller = new JobsController(
                _mockJobsUpdateService.Object,
                _mockLogger.Object,
                _mockHelperService.Object
            );

            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.NameIdentifier, "1")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
        }

        [Fact]
        public async Task CreateJob_ValidRequest_ReturnsOk()
        {

            var request = new CreateJobs();
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).ReturnsAsync(1);
            _mockJobsUpdateService.Setup(j => j.CreateJob(request, 1)).ReturnsAsync(new StatusResponse());


            var result = await _controller.CreateJob(request);


            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task GetStatus_ValidRequest_ReturnsOk()
        {

            _mockJobsUpdateService.Setup(j => j.GetStatus()).ReturnsAsync(new List<Status>());


            var result = await _controller.GetStatus();


            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task StatusUpdate_ValidRequest_ReturnsOk()
        {

            var statusUpdateModel = new StatusUpdateModel();
            _mockJobsUpdateService.Setup(j => j.UpdateJobStatus(statusUpdateModel)).ReturnsAsync(new StatusResponse());


            var result = await _controller.StatusUpdate(statusUpdateModel);

            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
            Assert.IsType<StatusResponse>(okResult.Value);
        }
        [Fact]
        public async Task CreateJob_InvalidModelState_ReturnsBadRequest()
        {

            var request = new CreateJobs();
            _controller.ModelState.AddModelError("JobId", "JobId is required");


            var result = await _controller.CreateJob(request);


            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.IsType<SerializableError>(badRequestResult.Value);
        }
    }
}
