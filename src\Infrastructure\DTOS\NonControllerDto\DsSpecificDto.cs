using System.Text.Json.Serialization;

namespace Infrastructure.DTOS.NonControllerDto
{
    public class DsSpecificDto
    {
        [JsonPropertyName("isError")]
        public bool IsError { get; set; }

        [JsonPropertyName("timestamp")]
        public string Timestamp { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("version")]
        public string Version { get; set; }

        [JsonPropertyName("job_id")]
        public string JobId { get; set; }

        [JsonPropertyName("company_id")]
        public string CompanyId { get; set; }

        [JsonPropertyName("company_name")]
        public string CompanyName { get; set; }

        [JsonPropertyName("files")]
        public List<DsSpecificFileDto> Files { get; set; }

        [JsonPropertyName("kpi_groups")]
        public List<DsSpecificKpiGroupDto> KpiGroups { get; set; }
    }

    public class DsSpecificFileDto
    {
        [JsonPropertyName("file_name")]
        public string FileName { get; set; }

        [JsonPropertyName("s3_path")]
        public string S3Path { get; set; }
    }

    public class DsSpecificKpiGroupDto
    {
        [JsonPropertyName("label")]
        public string Label { get; set; }

        [JsonPropertyName("label_id")]
        public string LabelId { get; set; }

        [JsonPropertyName("label_type")]
        public string LabelType { get; set; }

        [JsonPropertyName("company_id")]
        public string CompanyId { get; set; }

        [JsonPropertyName("company_name")]
        public string CompanyName { get; set; }

        [JsonPropertyName("kpi_name")]
        public string KpiName { get; set; }

        [JsonPropertyName("kpi_type")]
        public string KpiType { get; set; }

        [JsonPropertyName("mapping_name")]
        public string MappingName { get; set; }

        [JsonPropertyName("mapping_id")]
        public string MappingId { get; set; }

        [JsonPropertyName("id")]
        public int Id { get; set; }
        [JsonPropertyName("kpiId")]
        public int KpiId { get; set; }

        [JsonPropertyName("values")]
        public List<DsSpecificValueDto> Values { get; set; }

        [JsonPropertyName("analysis")]
        public DsSpecificAnalysisDto Analysis { get; set; }
    }

    public class DsSpecificValueDto
    {
        [JsonPropertyName("time_period")]
        public string Column { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }

        [JsonPropertyName("unit_scale")]
        public string UnitScale { get; set; }

        [JsonPropertyName("currency_symbol")]
        public string CurrencySymbol { get; set; }

        [JsonPropertyName("confidence_score")]
        public double ConfidenceScore { get; set; }

        [JsonPropertyName("page_num")]
        public int? PageNumber { get; set; }

        [JsonIgnore]
        public int PageNumberOrDefault => PageNumber ?? 0;

        [JsonPropertyName("file_type")]
        public string FileType { get; set; }

        [JsonPropertyName("file_path")]
        public string FilePath { get; set; }

        [JsonPropertyName("file_id")]
        public string FileId { get; set; }

        [JsonPropertyName("pdf")]
        public DsSpecificPdfDto Pdf { get; set; }

        [JsonPropertyName("excel")]
        public DsSpecificExcelDto Excel { get; set; }

        [JsonPropertyName("analysis")]
        public DsSpecificAnalysisDto Analysis { get; set; }
    }

    public class DsSpecificPdfDto
    {
        [JsonPropertyName("page_num")]
        public int PageNumber { get; set; }

        [JsonPropertyName("bbox")]
        public DsSpecificBboxDto Bbox { get; set; }
    }

    public class DsSpecificBboxDto
    {
        [JsonPropertyName("x1")]
        public float X1 { get; set; }

        [JsonPropertyName("y1")]
        public float Y1 { get; set; }

        [JsonPropertyName("x2")]
        public float X2 { get; set; }

        [JsonPropertyName("y2")]
        public float Y2 { get; set; }
    }

    public class DsSpecificExcelDto
    {
        [JsonPropertyName("sheet")]
        public string Sheet { get; set; }

        [JsonPropertyName("reference")]
        public string Reference { get; set; }
    }

    public class DsSpecificAnalysisDto
    {
        [JsonPropertyName("trend")]
        public string Trend { get; set; }

        [JsonPropertyName("percentage_change")]
        public double? PercentageChange { get; set; }

        [JsonPropertyName("reasoning")]
        public string Reasoning { get; set; }

        [JsonPropertyName("available_periods")]
        public List<string> AvailablePeriods { get; set; } = [];
    }
}
