using Newtonsoft.Json.Linq;
using Persistence.MongoDb;

namespace API.Extensions;

public static class MongoDbExtensions
{
    public static IServiceCollection ConfigureMongoDb(this IServiceCollection services, IConfiguration configuration)
    {
        var mongoSettings = new MongoDbSettings();
        configuration.GetSection("MongoDb").Bind(mongoSettings);        
        if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") != Environments.Development)
        {
            var mongoSecret = AwsSecretsManagerHelper.GetSecret($"{configuration["AWS:MongoSecretKey"]}", AwsSecretsManagerHelper.DEFAULT_REGION);
            if (!string.IsNullOrEmpty(mongoSecret))
            {
                dynamic jObject = JObject.Parse(mongoSecret);
                mongoSettings.ConnectionString = $"mongodb://{jObject.username}:{jObject.password}@{jObject.host}:{jObject.port}/{mongoSettings.DatabaseName}";
                mongoSettings.DatabaseName = mongoSettings.DatabaseName;
            }
        }        
        services.Configure<MongoDbSettings>(options =>
        {
            options.ConnectionString = mongoSettings.ConnectionString;
            options.DatabaseName = mongoSettings.DatabaseName;
        });

        return services;
    }
}