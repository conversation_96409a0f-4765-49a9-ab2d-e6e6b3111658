﻿using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;

namespace Persistence.Models
{
    public class Pages:BaseEntity
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }
        public Guid DocumentId { get; set; }
        public int ModuleId { get; set; }
        public string ModuleName { get; set; } = string.Empty;
        public string Items { get; set; } = string.Empty;
    }
}
