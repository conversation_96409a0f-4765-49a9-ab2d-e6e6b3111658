using API.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.SwaggerUI;
using System.Reflection;
using Xunit;

namespace UnitTests.Extensions
{
    public class SwaggerConfigTests
    {
        [Fact]
        public void SwaggerConfiguration_ShouldConfigureSwaggerUIOptions()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.SwaggerConfiguration();

            // Assert
            Assert.NotEmpty(services);
            
            // Verify services were added (Options and SwaggerGen related services)
            var hasOptionsServices = services.Any(d => d.ServiceType.Name.Contains("Options"));
            Assert.True(hasOptionsServices);
        }

        [Fact]
        public void SwaggerConfiguration_ShouldConfigureSwaggerGen()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.SwaggerConfiguration();

            // Assert
            // Verify Swagger services are added
            var hasSwaggerServices = services.Any(d => d.ServiceType.Name.Contains("Swagger") || 
                                                       d.ServiceType.Name.Contains("OpenApi"));
            Assert.True(hasSwaggerServices);
        }

        [Fact]
        public void SwaggerConfiguration_WithNullServices_ShouldThrowArgumentNullException()
        {
            // Arrange
            IServiceCollection? nullServices = null;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => nullServices!.SwaggerConfiguration());
        }

        [Fact]
        public void SwaggerExcludeAttribute_ShouldBeAttribute()
        {
            // Act & Assert
            var type = typeof(SwaggerConfig.SwaggerExcludeAttribute);
            Assert.True(type.IsSubclassOf(typeof(Attribute)));
            Assert.Equal(AttributeTargets.Property, type.GetCustomAttribute<AttributeUsageAttribute>()?.ValidOn);
        }

        [Fact]
        public void SwaggerExcludeFilter_ShouldImplementISchemaFilter()
        {
            // Act & Assert
            var type = typeof(SwaggerConfig.SwaggerExcludeFilter);
            Assert.True(typeof(ISchemaFilter).IsAssignableFrom(type));
        }

        [Fact]
        public void SwaggerExcludeFilter_Apply_WithNullSchema_ShouldNotThrow()
        {
            // Arrange
            var filter = new SwaggerConfig.SwaggerExcludeFilter();
            OpenApiSchema? nullSchema = null;
            var context = new SchemaFilterContext(typeof(object), null!, null!);

            // Act & Assert
            // Should not throw exception
            filter.Apply(nullSchema!, context);
        }

        [Fact]
        public void SwaggerExcludeFilter_Apply_WithEmptyProperties_ShouldNotThrow()
        {
            // Arrange
            var filter = new SwaggerConfig.SwaggerExcludeFilter();
            var schema = new OpenApiSchema();
            var context = new SchemaFilterContext(typeof(object), null!, null!);

            // Act & Assert
            // Should not throw exception
            filter.Apply(schema, context);
        }

        [Fact]
        public void RemoveVersionFromParameter_ShouldImplementIOperationFilter()
        {
            // Act & Assert
            var type = typeof(SwaggerConfig).GetNestedType("RemoveVersionFromParameter", BindingFlags.NonPublic);
            Assert.NotNull(type);
            Assert.True(typeof(IOperationFilter).IsAssignableFrom(type));
        }

        [Fact]
        public void ReplaceVersionWithExactValueInPath_ShouldImplementIDocumentFilter()
        {
            // Act & Assert
            var type = typeof(SwaggerConfig).GetNestedType("ReplaceVersionWithExactValueInPath", BindingFlags.NonPublic);
            Assert.NotNull(type);
            Assert.True(typeof(IDocumentFilter).IsAssignableFrom(type));
        }

        [Fact]
        public void SwaggerConfig_ShouldHaveStaticSwaggerConfigurationMethod()
        {
            // Act & Assert
            var method = typeof(SwaggerConfig).GetMethod("SwaggerConfiguration", BindingFlags.Public | BindingFlags.Static);
            Assert.NotNull(method);
            Assert.True(method.IsStatic);
            Assert.True(method.IsPublic);
            
            var parameters = method.GetParameters();
            Assert.Single(parameters);
            Assert.Equal(typeof(IServiceCollection), parameters[0].ParameterType);
        }

        [Fact]
        public void SwaggerConfig_RandomString_ShouldGenerateCorrectLength()
        {
            // This tests the private RandomString method indirectly through reflection
            // to verify its behavior
            
            // Arrange
            var type = typeof(SwaggerConfig);
            var method = type.GetMethod("RandomString", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.NotNull(method);

            // Act
            var result = method.Invoke(null, new object[] { 9 });

            // Assert
            Assert.NotNull(result);
            Assert.IsType<string>(result);
            var randomString = (string)result;
            Assert.Equal(9, randomString.Length);
            
            // Verify it contains only valid characters
            const string validChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            Assert.All(randomString, c => Assert.Contains(c, validChars));
        }

        [Fact]
        public void SwaggerConfig_RandomString_ShouldGenerateDifferentValues()
        {
            // Arrange
            var type = typeof(SwaggerConfig);
            var method = type.GetMethod("RandomString", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.NotNull(method);

            // Act
            var result1 = method.Invoke(null, new object[] { 10 });
            var result2 = method.Invoke(null, new object[] { 10 });

            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            Assert.IsType<string>(result1);
            Assert.IsType<string>(result2);
            
            var string1 = (string)result1;
            var string2 = (string)result2;
            
            Assert.Equal(10, string1.Length);
            Assert.Equal(10, string2.Length);
            Assert.NotEqual(string1, string2); // Should generate different values
        }

        [Theory]
        [InlineData(1)]
        [InlineData(5)]
        [InlineData(20)]
        [InlineData(50)]
        public void SwaggerConfig_RandomString_ShouldHandleDifferentLengths(int length)
        {
            // Arrange
            var type = typeof(SwaggerConfig);
            var method = type.GetMethod("RandomString", BindingFlags.NonPublic | BindingFlags.Static);
            Assert.NotNull(method);

            // Act
            var result = method.Invoke(null, new object[] { length });

            // Assert
            Assert.NotNull(result);
            Assert.IsType<string>(result);
            var randomString = (string)result;
            Assert.Equal(length, randomString.Length);
        }

        [Fact]
        public void SwaggerConfig_NestedClasses_ShouldBePrivate()
        {
            // Act & Assert
            var removeVersionType = typeof(SwaggerConfig).GetNestedType("RemoveVersionFromParameter", BindingFlags.NonPublic);
            var replaceVersionType = typeof(SwaggerConfig).GetNestedType("ReplaceVersionWithExactValueInPath", BindingFlags.NonPublic);
            
            Assert.NotNull(removeVersionType);
            Assert.NotNull(replaceVersionType);
            Assert.True(removeVersionType.IsNestedPrivate);
            Assert.True(replaceVersionType.IsNestedPrivate);
        }

        [Fact]
        public void SwaggerExcludeFilter_ShouldBePublicNestedClass()
        {
            // Act & Assert
            var type = typeof(SwaggerConfig.SwaggerExcludeFilter);
            Assert.True(type.IsNested);
            Assert.True(type.IsNestedPublic);
        }

        [Fact]
        public void SwaggerExcludeAttribute_ShouldBePublicNestedClass()
        {
            // Act & Assert
            var type = typeof(SwaggerConfig.SwaggerExcludeAttribute);
            Assert.True(type.IsNested);
            Assert.True(type.IsNestedPublic);
        }
    }

    // Test class to verify SwaggerExcludeFilter functionality
    public class TestClassWithExcludeAttribute
    {
        public string NormalProperty { get; set; } = string.Empty;

        [SwaggerConfig.SwaggerExcludeAttribute]
        public string ExcludedProperty { get; set; } = string.Empty;
    }
}