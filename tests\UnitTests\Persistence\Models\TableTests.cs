using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class TableTests
    {
        [Fact]
        public void Table_DefaultInitialization_ShouldHaveNullValues()
        {
            // Act
            var table = new Table();

            // Assert
            Assert.Null(table.Rows);
            Assert.Null(table.Columns);
            Assert.Null(table.Name);
            Assert.Null(table.Id);
        }

        [Fact]
        public void Table_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var rows = new List<global::Persistence.TableRow> { new global::Persistence.TableRow(), new global::Persistence.TableRow() };
            var columns = new List<TableColumn> { new TableColumn(), new TableColumn() };

            // Act
            var table = new Table
            {
                Rows = rows,
                Columns = columns,
                Name = "Financial Data",
                Id = "table-001"
            };

            // Assert
            Assert.Equal(rows, table.Rows);
            Assert.Equal(2, table.Rows.Count);
            Assert.Equal(columns, table.Columns);
            Assert.Equal(2, table.Columns.Count);
            Assert.Equal("Financial Data", table.Name);
            Assert.Equal("table-001", table.Id);
        }

        [Fact]
        public void Table_WithEmptyLists_ShouldAcceptEmptyCollections()
        {
            // Act
            var table = new Table
            {
                Rows = new List<global::Persistence.TableRow>(),
                Columns = new List<TableColumn>()
            };

            // Assert
            Assert.NotNull(table.Rows);
            Assert.Empty(table.Rows);
            Assert.NotNull(table.Columns);
            Assert.Empty(table.Columns);
        }

        [Fact]
        public void Table_WithEmptyStrings_ShouldAcceptEmptyValues()
        {
            // Act
            var table = new Table
            {
                Name = string.Empty,
                Id = string.Empty
            };

            // Assert
            Assert.Equal(string.Empty, table.Name);
            Assert.Equal(string.Empty, table.Id);
        }

        [Fact]
        public void Table_WithLargeLists_ShouldAcceptManyItems()
        {
            // Arrange
            var manyRows = Enumerable.Range(1, 100).Select(i => new global::Persistence.TableRow()).ToList();
            var manyColumns = Enumerable.Range(1, 50).Select(i => new TableColumn()).ToList();

            // Act
            var table = new Table
            {
                Rows = manyRows,
                Columns = manyColumns
            };

            // Assert
            Assert.Equal(100, table.Rows.Count);
            Assert.Equal(50, table.Columns.Count);
        }

        [Fact]
        public void Table_WithLongName_ShouldAcceptLongString()
        {
            // Arrange
            var longName = new string('A', 1000);

            // Act
            var table = new Table { Name = longName };

            // Assert
            Assert.Equal(longName, table.Name);
        }

        [Fact]
        public void Table_WithLongId_ShouldAcceptLongString()
        {
            // Arrange
            var longId = new string('1', 500);

            // Act
            var table = new Table { Id = longId };

            // Assert
            Assert.Equal(longId, table.Id);
        }
    }
}