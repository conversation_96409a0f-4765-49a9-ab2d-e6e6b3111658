using Infrastructure.Contract;
using Persistence.Models;

namespace DataIngestionService.IServices
{
    public interface IJobsUpdateService
    {
        Task<bool> UpdateJobsStatus(string jwtToken, CancellationToken cancellationToken = default);
        Task<StatusResponse> CreateJob(CreateJobs jobs, int userId);
        Task<List<Status>> GetStatus();
        Task<StatusResponse> UpdateJobStatus(StatusUpdateModel statusUpdate);
    }
}