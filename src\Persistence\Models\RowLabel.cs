﻿using MongoDB.Bson.Serialization.Attributes;

namespace Persistence.Models
{
    public class RowLabel
    {
        public string Text { get; set; } 
        public bool IsBold { get; set; } = false;
        public bool IsEmptyRow { get; set; } = false;
        public string Style { get; set; }
        public string Id { get; set; }
        public string Mapping { get; set; }

        [BsonIgnoreIfNull]
        public int? MappingId { get; set; }
        public float? MappingScore { get; set; }
    }
}
