﻿using MongoDB.Bson.Serialization.Attributes;

namespace Persistence.Models
{
    public class Cell
    {
        public string ColumnKey { get; set; }
        public string Value { get; set; }
        public string Source { get; set; }
        public PdfHighlight PdfHighlight { get; set; }
        public List<string> Comments { get; set; }
        public string Format { get; set; }
        public string Type { get; set; }
        public string Date { get; set; }
        [BsonIgnoreIfNull]
        public string? ColumnIndex { get; set; } // Col Index
    }
}
