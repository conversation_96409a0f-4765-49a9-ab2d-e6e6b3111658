using Ingestion.API.Controllers;
using Ingestion.API.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.MongoDb;
using Persistence.Models;
using System.Security.Claims;
using Xunit;

namespace UnitTests.Controllers
{
    public class FinancialsControllerTests
    {
        private readonly Mock<ILogger<FinancialsController>> _mockLogger;
        private readonly Mock<IFinancialsRepository> _mockFinancialsRepository;
        private readonly FinancialsController _controller;

        public FinancialsControllerTests()
        {
            _mockLogger = new Mock<ILogger<FinancialsController>>();
            _mockFinancialsRepository = new Mock<IFinancialsRepository>();

            _controller = new FinancialsController(_mockLogger.Object, _mockFinancialsRepository.Object);

            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.NameIdentifier, "1")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
        }

        [Fact]
        public async Task GetPdfHighlights_WithValidRequest_ReturnsOkResult()
        {
            // Arrange
            var request = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.NewGuid(),
                TableGroupLabel = "Revenue",
                PeriodDate = "2023-12"
            };

            var expectedHighlights = new List<PdfHighlight> 
            { 
                new PdfHighlight { Text = "Sample highlight" } 
            };
            _mockFinancialsRepository
                .Setup(x => x.GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(
                    request.ProcessId, request.TableGroupLabel, request.PeriodDate))
                .ReturnsAsync(expectedHighlights);

            // Act
            var result = await _controller.GetPdfHighlights(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedHighlights, okResult.Value);
            _mockFinancialsRepository.Verify(x => x.GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(
                request.ProcessId, request.TableGroupLabel, request.PeriodDate), Times.Once);
        }

        [Fact]
        public async Task GetPdfHighlights_WithInvalidRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.Empty, // Invalid - empty GUID
                TableGroupLabel = "Revenue",
                PeriodDate = "2023-12"
            };

            // Act
            var result = await _controller.GetPdfHighlights(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("All parameters are required.", badRequestResult.Value);
            _mockFinancialsRepository.Verify(x => x.GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(
                It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task GetPdfHighlights_WithNullTableGroupLabel_ReturnsBadRequest()
        {
            // Arrange
            var request = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.NewGuid(),
                TableGroupLabel = null!, // Invalid - null value
                PeriodDate = "2023-12"
            };

            // Act
            var result = await _controller.GetPdfHighlights(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("All parameters are required.", badRequestResult.Value);
            _mockFinancialsRepository.Verify(x => x.GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(
                It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task GetPdfHighlights_WithEmptyPeriodDate_ReturnsBadRequest()
        {
            // Arrange
            var request = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.NewGuid(),
                TableGroupLabel = "Revenue",
                PeriodDate = string.Empty // Invalid - empty string
            };

            // Act
            var result = await _controller.GetPdfHighlights(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("All parameters are required.", badRequestResult.Value);
            _mockFinancialsRepository.Verify(x => x.GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(
                It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }
    }
}