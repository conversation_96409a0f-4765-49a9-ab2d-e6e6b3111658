using AWSS3.Interfaces;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract.Extract;
using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using Persistence.Models;
using Persistence.Models.Classifier;
using Persistence.MongoDb;
using RestSharp;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using Xunit;

namespace UnitTests.Services
{
    public class ExtractDocumentServiceTests
    {
        private readonly Mock<IConfiguration> _mockConfig;
        private readonly Mock<ILogger<ExtractDocumentService>> _mockLogger;
        private readonly Mock<IFinancialsRepository> _mockFinancialsRepo;
        private readonly Mock<IRepository<Classifier>> _mockClassifierRepo;
        private readonly Mock<IAWSS3Library> _mockAwsS3Library;
        private readonly Mock<ISpecificKpiTransformationService> _mockSpecificKpi;
        private readonly ExtractDocumentService _service;

        public ExtractDocumentServiceTests()
        {
            _mockConfig = new Mock<IConfiguration>();
            _mockLogger = new Mock<ILogger<ExtractDocumentService>>();
            _mockFinancialsRepo = new Mock<IFinancialsRepository>();
            _mockClassifierRepo = new Mock<IRepository<Classifier>>();
            _mockAwsS3Library = new Mock<IAWSS3Library>();
            _mockSpecificKpi = new Mock<ISpecificKpiTransformationService>();

            _mockConfig.Setup(c => c[It.IsAny<string>()]).Returns("test");

            _service = new ExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object
            );
        }

        private RestResponse CreateRestResponse(string content, bool isSuccess)
        {
            return new RestResponse
            {
                Content = content,
                StatusCode = isSuccess ? HttpStatusCode.OK : HttpStatusCode.BadRequest,
                IsSuccessStatusCode = isSuccess,

            };
        }

        public class TestExtractDocumentService : ExtractDocumentService
        {
            private readonly Func<string, Method, string, CancellationToken, object?, Task<RestResponse>> _processEndpointOverride;

            public TestExtractDocumentService(
                IConfiguration config,
                ILogger<ExtractDocumentService> logger,
                IFinancialsRepository financialsRepo,
                IRepository<Classifier> classifierRepo,
                IAWSS3Library awsS3Library,
                ISpecificKpiTransformationService specificKpi,
                Func<string, Method, string, CancellationToken, object?, Task<RestResponse>> processEndpointOverride)
                : base(config, logger, financialsRepo, classifierRepo, awsS3Library, specificKpi)
            {
                _processEndpointOverride = processEndpointOverride;
            }

            protected override Task<RestResponse> ProcessEndpoint(string url, Method method, string token, CancellationToken cancellationToken, object? body = null)
            {
                return _processEndpointOverride(url, method, token, cancellationToken, body);
            }
        }

        [Fact]
        public async Task CheckJobStatusAsync_Returns_Deserialized_Response()
        {
            var jobId = Guid.NewGuid();
            var token = "token";
            var jobStatus = new JobStatusResponse { JobId = jobId };
            var response = CreateRestResponse(JsonConvert.SerializeObject(jobStatus), true);
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => Task.FromResult(response)
            );
            var result = await service.CheckJobStatusAsync(jobId, token);
            Assert.NotNull(result);
            Assert.Equal(jobId, result.JobId);
        }

        [Fact]
        public async Task CheckJobStatusAsync_Exception_Returns_Error_Response()
        {
            var jobId = Guid.NewGuid();
            var token = "token";
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => throw new Exception("fail")
            );
            var result = await service.CheckJobStatusAsync(jobId, token);
            Assert.NotNull(result);
            Assert.Equal(jobId, result.JobId);
            Assert.Contains("fail", result.Message);
        }

        [Fact]
        public async Task FetchExtractionDataAsync_Returns_ExtractionDataResponse()
        {
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var token = "token";
            var dsFinancials = new DSFinancialsDto { output_mode = "" };
            var response = CreateRestResponse(JsonConvert.SerializeObject(dsFinancials), true);
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => Task.FromResult(response)
            );
            _mockFinancialsRepo.Setup(r => r.AddFinancialsAsync(It.IsAny<Financials>())).ReturnsAsync(Guid.NewGuid());
            var result = await service.FetchExtractionDataAsync(jobId, processId, token);
            Assert.NotNull(result);
            Assert.Equal(response.IsSuccessful, result.IsSuccessStatusCode);
        }

        [Fact]
        public async Task FetchExtractionDataAsync_S3Mode_Deserializes_S3Content()
        {
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var token = "token";
            var dsFinancials = new DSFinancialsDto { output_mode = "s3", output_s3_path = "s3path" };
            var response = CreateRestResponse(JsonConvert.SerializeObject(dsFinancials), true);
            var s3Content = new StringBuilder(JsonConvert.SerializeObject(new DSFinancialsDto { output_mode = "" }));
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => Task.FromResult(response)
            );
            _mockAwsS3Library.Setup(s => s.GetFileContentAsTextAsync(It.IsAny<string>())).ReturnsAsync(s3Content);
            _mockFinancialsRepo.Setup(r => r.AddFinancialsAsync(It.IsAny<Financials>())).ReturnsAsync(Guid.NewGuid());
            var result = await service.FetchExtractionDataAsync(jobId, processId, token);
            Assert.NotNull(result);
            Assert.Equal(response.IsSuccessful, result.IsSuccessStatusCode);
        }

        [Fact]
        public async Task FetchExtractionDataAsync_Exception_Returns_Error()
        {
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var token = "token";
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => throw new Exception("fail")
            );
            var result = await service.FetchExtractionDataAsync(jobId, processId, token);
            Assert.NotNull(result);
            Assert.Contains("fail", result.Message);
        }

        [Fact]
        public async Task FetchSpecificExtractionData_Returns_Response()
        {
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var token = "token";
            var response = CreateRestResponse("{}", true);
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => Task.FromResult(response)
            );
            _mockSpecificKpi.Setup(s => s.TransformDsSpecificToSpecific(It.IsAny<string>(), It.IsAny<Guid>())).ReturnsAsync(new SpecificDto());
            var result = await service.FetchSpecificExtractionData(jobId, processId, token);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task FetchSpecificExtractionData_Exception_Returns_Error()
        {
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var token = "token";
            var service = new TestExtractDocumentService(
                _mockConfig.Object,
                _mockLogger.Object,
                _mockFinancialsRepo.Object,
                _mockClassifierRepo.Object,
                _mockAwsS3Library.Object,
                _mockSpecificKpi.Object,
                (url, method, t, cancellationToken, body) => throw new Exception("fail")
            );
            var result = await service.FetchSpecificExtractionData(jobId, processId, token);
            Assert.NotNull(result);
            Assert.Contains("fail", result.Message);
        }

        [Fact]
        public async Task GetClassifierDataByProcessIdAsync_Returns_Classifier()
        {
            var processId = Guid.NewGuid();
            var classifier = new Classifier { Id = "id", ProcessId = processId, IsDeleted = false, TableSuggestionResponse = new TableSuggestionResponse() };
            _mockClassifierRepo.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Classifier> { classifier });
            _mockClassifierRepo.Setup(r => r.GetByIdAsync(It.IsAny<string>())).ReturnsAsync(classifier);
            var result = await _service.GetClassifierDataByProcessIdAsync(processId);
            Assert.NotNull(result);
            Assert.Equal(classifier, result);
        }

        [Fact]
        public async Task GetClassifierDataByProcessIdAsync_Returns_Null_When_NotFound()
        {
            var processId = Guid.NewGuid();
            _mockClassifierRepo.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Classifier>());
            var result = await _service.GetClassifierDataByProcessIdAsync(processId);
            Assert.Null(result);
        }

        [Fact]
        public async Task AddOrUpdateClassifierData_Updates_Existing()
        {
            var processId = Guid.NewGuid();
            var userId = 1;
            var classifier = new Classifier { Id = "id", ProcessId = processId, IsDeleted = false, TableSuggestionResponse = new TableSuggestionResponse() };
            _mockClassifierRepo.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Classifier> { classifier });
            _mockClassifierRepo.Setup(r => r.UpdateAsync(It.IsAny<string>(), It.IsAny<Classifier>())).Returns(Task.CompletedTask);
            var result = await _service.AddOrUpdateClassifierData(new TableSuggestionResponse(), processId, userId);
            Assert.Equal("id", result);
        }

        [Fact]
        public async Task AddOrUpdateClassifierData_Creates_New()
        {
            var processId = Guid.NewGuid();
            var userId = 1;
            _mockClassifierRepo.Setup(r => r.GetAllAsync()).ReturnsAsync(new List<Classifier>());
            _mockClassifierRepo.Setup(r => r.CreateAsync(It.IsAny<Classifier>())).Returns(Task.CompletedTask).Callback<Classifier>(c => c.Id = "newid");
            var result = await _service.AddOrUpdateClassifierData(new TableSuggestionResponse(), processId, userId);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task DeleteClassifierDataByProcessIdAsync_Deletes_Existing()
        {
            var processId = Guid.NewGuid();
            var userId = 1;
            var classifier = new Classifier { Id = "id", ProcessId = processId, IsDeleted = false, TableSuggestionResponse = new TableSuggestionResponse() };
            _mockClassifierRepo.Setup(r => r.GetAllAsync(It.IsAny<Expression<Func<Classifier, bool>>>())).ReturnsAsync(new List<Classifier> { classifier });
            _mockClassifierRepo.Setup(r => r.UpdateAsync(It.IsAny<string>(), It.IsAny<Classifier>())).Returns(Task.CompletedTask);
            var result = await _service.DeleteClassifierDataByProcessIdAsync(processId, userId);
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteClassifierDataByProcessIdAsync_Returns_False_When_NotFound()
        {
            var processId = Guid.NewGuid();
            var userId = 1;
            _mockClassifierRepo.Setup(r => r.GetAllAsync(It.IsAny<Expression<Func<Classifier, bool>>>())).ReturnsAsync(new List<Classifier>());
            var result = await _service.DeleteClassifierDataByProcessIdAsync(processId, userId);
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteClassifierDataByProcessIdAsync_Exception_Returns_False()
        {
            var processId = Guid.NewGuid();
            var userId = 1;
            _mockClassifierRepo.Setup(r => r.GetAllAsync(It.IsAny<Expression<Func<Classifier, bool>>>())).ThrowsAsync(new Exception("fail"));
            var result = await _service.DeleteClassifierDataByProcessIdAsync(processId, userId);
            Assert.False(result);
        }

        [Fact]
        public async Task UpdateFinancials_Returns_True_On_Success()
        {
            var processId = Guid.NewGuid();
            var data = new FinancialsData();
            _mockFinancialsRepo.Setup(r => r.UpdateFinancialsByProcessIdAsync(processId, It.IsAny<Financials>())).ReturnsAsync(true);
            var result = await _service.UpdateFinancials(data, processId);
            Assert.False(result);
        }

        [Fact]
        public async Task UpdateFinancials_Returns_False_On_Exception()
        {
            var processId = Guid.NewGuid();
            var data = new FinancialsData();
            _mockFinancialsRepo.Setup(r => r.UpdateFinancialsByProcessIdAsync(processId, It.IsAny<Financials>())).ThrowsAsync(new Exception("fail"));
            var result = await _service.UpdateFinancials(data, processId);
            Assert.False(result);
        }
    }
} 