﻿namespace Persistence.Models
{
    public partial class UserDetails : BaseEntity
    {
        [Key]
        public int UserID { get; set; }
        public int? RegionID { get; set; }
        public int? CountryID { get; set; }
        public int? TimeZoneID { get; set; }
        public string? Title { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string EmailID { get; set; }
        public string? Password { get; set; }
        public string? PhoneNumber { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsLocked { get; set; }
        public string EncryptedUserID { get; set; }
        public string? Organization { get; set; }

    }
}
