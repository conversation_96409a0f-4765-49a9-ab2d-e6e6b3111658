using DataIngestionService.Helpers;
using System;
using Xunit;

namespace UnitTests.Helpers
{
    public class GuidExtensionsTests
    {
        private static readonly Guid DefaultTenantId = new("00000000-0000-0000-0000-000000000001");

        [Fact]
        public void ToTenantId_WithNullGuid_ReturnsDefaultTenantId()
        {
            // Arrange
            Guid? nullGuid = null;

            // Act
            var result = nullGuid.ToTenantId();

            // Assert
            Assert.Equal(DefaultTenantId, result);
        }

        [Fact]
        public void ToTenantId_WithValidGuid_ReturnsSameGuid()
        {
            // Arrange
            Guid? validGuid = Guid.NewGuid();

            // Act
            var result = validGuid.ToTenantId();

            // Assert
            Assert.Equal(validGuid.Value, result);
        }

        [Fact]
        public void ToTenantId_WithEmptyGuid_ReturnsEmptyGuid()
        {
            // Arrange
            Guid? emptyGuid = Guid.Empty;

            // Act
            var result = emptyGuid.ToTenantId();

            // Assert
            Assert.Equal(Guid.Empty, result);
        }

        [Fact]
        public void ToTenantId_DefaultTenantId_ShouldBeSpecificValue()
        {
            // Arrange
            Guid? nullGuid = null;
            var expectedDefaultTenantId = new Guid("00000000-0000-0000-0000-000000000001");

            // Act
            var result = nullGuid.ToTenantId();

            // Assert
            Assert.Equal(expectedDefaultTenantId, result);
            Assert.NotEqual(Guid.Empty, result);
        }
    }
}
