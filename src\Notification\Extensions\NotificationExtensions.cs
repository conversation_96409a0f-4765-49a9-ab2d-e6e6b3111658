using Microsoft.Extensions.DependencyInjection;
using Notification.Hubs;
using Notification.Services;

namespace Notification.Extensions
{
    /// <summary>
    /// Extension methods for registering notification services
    /// </summary>
    public static class NotificationExtensions
    {
        /// <summary>
        /// Add the bare minimum notification services needed for SignalR communication
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddNotificationServices(this IServiceCollection services)
        {
            // Register the SignalR service
            services.AddSignalR();
            
            // Register our notification sender as a singleton
            services.AddSingleton<NotificationSender>();
            
            return services;
        }
    }
}
