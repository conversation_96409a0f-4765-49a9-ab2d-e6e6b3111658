using System;

namespace Infrastructure.Contract
{
    public class DocumentSummaryDto
    {
        public Guid Id { get; set; }
        public string S3Path { get; set; }
        public string Name { get; set; }
        public string Url { get; set; }
        public int DocumentTypeId { get; set; }
        public string DocumentType { get; set; }
        public List<string> Errors { get; set; } = new();
        public string FileStatus { get; set; } = string.Empty;
        public string Extension { get; set; }
        #nullable enable
        public string? PeriodType { get; set; }
        public int? Year { get; set; }
        public string? Month { get; set; }
        public string? Quarter { get; set; }
    }
}