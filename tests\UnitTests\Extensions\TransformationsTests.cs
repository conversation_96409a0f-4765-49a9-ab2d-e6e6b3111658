using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Enum;
using Infrastructure.Extentions;
using Xunit;

namespace UnitTests.Extensions
{
    public class TransformationsTests
    {
        [Fact]
        public void ToFinancialsData_ShouldTransformCompleteObject()
        {
            // Arrange
            var financialsDto = new DSFinancialsDto
            {
                job_id = "test-job",
                company_id = "company-123",
                company_name = "Test Company",
                files = new List<DSFileDetails>
                {
                    new DSFileDetails
                    {
                        s3_path = "s3://test-bucket/file.pdf",
                        file_name = "test.pdf",
                        page_count = 10
                    }
                },
                table_groups = new List<DSTableGroup>
                {
                    new DSTableGroup
                    {
                        label = "Financial Statements",
                        tables = new List<DSTable>
                        {
                            new DSTable
                            {
                                id = "table-1",
                                name = "Balance Sheet",
                                sections = new List<DSSection>
                                {
                                    new DSSection
                                    {
                                        rows = new List<DSRow>
                                        {
                                            new DSRow
                                            {
                                                label = new DSLabel
                                                {
                                                    text = "Total Assets",
                                                    style = "bold",
                                                    defined_name = "assets",
                                                    mapping = "Assets",
                                                    mapping_id = 1,
                                                    mapping_score = 5
                                                },
                                                cells = new List<DSCell>
                                                {
                                                    new DSCell
                                                    {
                                                        column_index = "col1",
                                                        column_key = "FY2023|Dec|12-months",
                                                        value = new DSValue
                                                        {
                                                            value = "1000000",
                                                            format = "#,##0",
                                                            type = "number",
                                                            date = null
                                                        },
                                                        comment = new DSComment { texts = new List<string> { "Verified" } },
                                                        file_id = "file1",
                                                        page = 1,
                                                        bbox = new DSBbox
                                                        {
                                                            x1 = 100,
                                                            y1 = 200,
                                                            x2 = 150,
                                                            y2 = 220,
                                                            ph = 1000,
                                                            pw = 800
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                currency_unit = "USD",
                ticker = "TEST",
                country = "USA",
                sector = "Technology",
                industry = "Software"
            };

            // Act
            var result = financialsDto.ToFinancialsData();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("test-job", result.Id);
            Assert.Equal("company-123", result.CompanyId);
            Assert.Equal("Test Company", result.CompanyName);
            Assert.Equal(Extraction.AsReported, result.TypeofExtraction);
            
            // Verify Files
            Assert.Single(result.Files);
            var file = result.Files[0];
            Assert.Equal("s3://test-bucket/file.pdf", file.Url);
            Assert.Equal("test.pdf", file.FileName);
            Assert.Equal(10, file.PageCount);

            // Verify TableGroups
            Assert.Single(result.TableGroups);
            var tableGroup = result.TableGroups[0];
            Assert.Equal("Financial Statements", tableGroup.Label);
            
            // Verify Tables
            Assert.Single(tableGroup.Tables);
            var table = tableGroup.Tables[0];
            Assert.Equal("table-1", table.Id);
            Assert.Equal("Balance Sheet", table.Name);

            // Verify Columns
            Assert.Single(table.Columns);
            var column = table.Columns[0];
            Assert.NotNull(column.ColumnKey);
            Assert.Equal("FY2023", column.PeriodInfo);
            Assert.Equal(StatementType.Original, column.FilingType);

            // Verify Rows
            Assert.Single(table.Rows);
            var row = table.Rows[0];
            Assert.Equal("Total Assets", row.Label.Text);
            Assert.Equal("bold", row.Label.Style);
            Assert.Equal("assets", row.Label.Id);
            Assert.Equal("Assets", row.Label.Mapping);

            // Verify Cells
            Assert.Single(row.Cells);
            var cell = row.Cells[0];
            Assert.Equal("1000000", cell.Value);
            Assert.Equal("#,##0", cell.Format);
            Assert.Equal("number", cell.Type);
            Assert.Single(cell.Comments);
            Assert.Equal("Verified", cell.Comments[0]);
            Assert.Equal("file1", cell.Source);

            // Verify PDF Highlight
            Assert.NotNull(cell.PdfHighlight);
            Assert.Equal(1, cell.PdfHighlight.PageNumber);
            Assert.Equal(4, cell.PdfHighlight.Bounds.Count);
            Assert.Equal(100f, cell.PdfHighlight.Bounds[0]);
            Assert.Equal(200f, cell.PdfHighlight.Bounds[1]);
            Assert.Equal(150f, cell.PdfHighlight.Bounds[2]);
            Assert.Equal(220f, cell.PdfHighlight.Bounds[3]);
            Assert.Equal(1000f, cell.PdfHighlight.PageHeight);
            Assert.Equal(800f, cell.PdfHighlight.PageWidth);
        }
        [Theory]
        [InlineData("12-months", ReportingPeriod.Annual)]
        [InlineData("9-months", ReportingPeriod.NineMonth)]
        [InlineData("6-months", ReportingPeriod.HalfYearly)]
        [InlineData("3-months", ReportingPeriod.Quarter)]
        [InlineData("unknown", ReportingPeriod.Unknown)]
        public void GetReportingPeriod_ShouldReturnCorrectEnum(string input, ReportingPeriod expected)
        {
            // Act
            var result = input.GetReportingPeriod();

            // Assert
            Assert.Equal(expected, result);
        }        [Theory]
        [InlineData("part1|part2|part3|part4", 0, "part1")]
        [InlineData("part1|part2|part3|part4", 1, "part2")]
        [InlineData("part1|part2|part3|part4", 2, "part3")]
        [InlineData("part1|part2|part3|part4", 3, "part4")]
        [InlineData("part1|part2", 3, null)]
        public void DecodeColumnKey_ShouldReturnCorrectPart(string input, int index, string? expected)
        {
            // Act
            var result = input.DecodeColumnKey(index);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void GetUniqueId_ShouldReturnConsistentHash()
        {
            // Arrange
            var input = "test-string";

            // Act
            var result1 = input.GetUniqueId();
            var result2 = input.GetUniqueId();

            // Assert
            Assert.NotNull(result1);
            Assert.StartsWith("uid", result1);
            Assert.Equal(result1, result2);  // Same input should produce same hash
            
            // Different input should produce different hash
            var differentResult = "different-string".GetUniqueId();
            Assert.NotEqual(result1, differentResult);
        }

        [Fact]
        public void GetUniqueId_WithNullInput_ShouldHandleGracefully()
        {
            // Arrange
            string? nullInput = null;

            // Act
            var result = nullInput.GetUniqueId();

            // Assert
            Assert.NotNull(result);
            Assert.StartsWith("uid", result);
        }

        [Fact]
        public void GetUniqueId_CollisionResistance_ShouldProduceDifferentHashesForSimilarInputs()
        {
            // Arrange
            var inputs = new List<string>
            {
                "test1",
                "test2", 
                "test11",
                "Test1",
                "TEST1",
                "test-1",
                "test_1"
            };

            // Act
            var results = inputs.Select(input => input.GetUniqueId()).ToList();

            // Assert
            // All results should be unique
            Assert.Equal(inputs.Count, results.Distinct().Count());
            
            // All should start with "uid"
            Assert.All(results, result => Assert.StartsWith("uid", result));
            
            // All should have consistent length (uid + 32 hex chars)
            Assert.All(results, result => Assert.Equal(35, result.Length)); // "uid" + 32 chars
        }

        [Theory]
        [InlineData("")]
        [InlineData("|")]
        [InlineData("||")]
        public void DecodeColumnKey_WithInvalidInput_ShouldReturnEmpty(string input)
        {
            // Act
            var result = input.DecodeColumnKey(0);

            // Assert
            // For empty strings, the method might return empty string instead of null
            // depending on implementation
            if (result != null)
            {
                Assert.Equal("", result);
            }
            else
            {
                Assert.Null(result);
            }
        }

        [Fact]
        public void ToA1Bounds_WithZeroValues_ShouldReturnValidList()
        {
            // Arrange
            var bbox = new DSBbox
            {
                x1 = 0,
                y1 = 0,
                x2 = 0,
                y2 = 0,
                ph = 0,
                pw = 0
            };

            // Act
            var result = bbox.ToA1Bounds();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(4, result.Count);
            Assert.All(result, value => Assert.Equal(0f, value));
        }
    }
}
