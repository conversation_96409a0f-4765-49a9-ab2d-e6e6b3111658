using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Quartz;
using Xunit;
using API.Configuration;
using DataIngestionService.IServices;

public class JobsUpdateJobTests
{
    [Fact]
    public async Task Execute_LogsStartAndCompletion()
    {
        // Arrange
        var loggerMock = new Mock<ILogger<JobsUpdateJob>>();
        var serviceProviderMock = new Mock<IServiceProvider>();
        var jobsUpdateServiceMock = new Mock<IJobsUpdateService>();

        jobsUpdateServiceMock
            .Setup(s => s.UpdateJobsStatus(It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(true));

        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IJobsUpdateService)))
            .Returns(jobsUpdateServiceMock.Object);

        // Mock IServiceScopeFactory and IServiceScope
        var serviceScopeFactoryMock = new Mock<IServiceScopeFactory>();
        var serviceScopeMock = new Mock<IServiceScope>();
        serviceScopeMock.Setup(s => s.ServiceProvider).Returns(serviceProviderMock.Object);
        serviceScopeFactoryMock.Setup(f => f.CreateScope()).Returns(serviceScopeMock.Object);

        serviceProviderMock
            .Setup(sp => sp.GetService(typeof(IServiceScopeFactory)))
            .Returns(serviceScopeFactoryMock.Object);

        var job = new JobsUpdateJob(loggerMock.Object, serviceProviderMock.Object);

        var jobExecutionContextMock = new Mock<IJobExecutionContext>();

        // Act
        await job.Execute(jobExecutionContextMock.Object);

        // Assert
        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("JobsUpdateJob started")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            ),
            Times.Once
        );

        loggerMock.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v != null && v.ToString()!.Contains("JobsUpdateJob completed successfully")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()
            ),
            Times.Once
        );
    }
}