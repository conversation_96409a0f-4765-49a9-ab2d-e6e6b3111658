﻿namespace DataIngestionService.Helpers
{
    public static class Common
    {
        public static string GetMonthName(this int? monthNo)
        {
            return monthNo switch
            {
                1 => "Jan",
                2 => "Feb",
                3 => "Mar",
                4 => "Apr",
                5 => "May",
                6 => "Jun",
                7 => "Jul",
                8 => "Aug",
                9 => "Sep",
                10 => "Oct",
                11 => "Nov",
                12 => "Dec",
                _ => "",
            };
        }

        public static Guid ConvertToGuid(this string value)
        {
            if (string.IsNullOrWhiteSpace(value))
                return Guid.NewGuid();

            if (Guid.TryParse(value, out Guid result))
                return result;
            return Guid.NewGuid();
        }
    }
}
