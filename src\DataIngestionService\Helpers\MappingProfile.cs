using AutoMapper;
using Contract;
using Infrastructure.Contract;
using Infrastructure.DTOS.NonControllerDto;
using Persistence.Models;
using Persistence.Models.Specific;

namespace DataIngestionService.Helpers
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // Map from UploadDto to DataIngestionDocuments
            CreateMap<UploadDto, DataIngestionDocuments>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ProcessId, opt => opt.MapFrom(src => src.ProcessId))
                .ForMember(dest => dest.TenantId, opt => opt.MapFrom(src => src.TenantId))
                .ForMember(dest => dest.FileName, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.Ignore()) // Will be set manually
                .ForMember(dest => dest.S3Path, opt => opt.Ignore()) // Will be set manually
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => DateTime.UtcNow));

            // Map from UploadDto to DIMappingDocumentsDetails
            CreateMap<UploadDto, DIMappingDocumentsDetails>()
                .ForMember(dest => dest.FeatureId, opt => opt.MapFrom(src => src.FeatureId))
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
                .ForMember(dest => dest.ProcessId, opt => opt.MapFrom(src => src.ProcessId))
                .ForMember(dest => dest.SourceTypeId, opt => opt.MapFrom(src => src.SourceId))
                .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(src => DateTime.UtcNow));

            // Map from upload info to UploadResponseDto
            CreateMap<(Guid DocumentId, Guid ProcessId, string Url), UploadResponseDto>()
                .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.DocumentId))
                .ForMember(dest => dest.ProcessId, opt => opt.MapFrom(src => src.ProcessId))
                .ForMember(dest => dest.Url, opt => opt.MapFrom(src => src.Url));

            CreateMap<PageModule, Pages>()
               .ForMember(dest => dest.DocumentId, opt => opt.Ignore()) // Set separately
               .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
               .ForMember(dest => dest.CreatedOn, opt => opt.MapFrom(_ => DateTime.UtcNow))
               .ForMember(dest => dest.ModuleId, opt => opt.MapFrom(src => src.ModuleId))
               .ForMember(dest => dest.Items, opt => opt.MapFrom(src => src.Items))
               .ForMember(dest => dest.ModuleName, opt => opt.MapFrom(src => src.ModuleName))
               .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(_ => false));

            // Map from SpecificDto to SpecificKpiDocument
            CreateMap<SpecificDto, Persistence.Models.Specific.SpecificKpiDocument>()
                .ForMember(dest => dest.JobId, opt => opt.MapFrom(src => string.IsNullOrEmpty(src.JobId) ? Guid.Empty : new Guid(src.JobId)))
                .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.CompanyName))
                .ForMember(dest => dest.CurrencyCode, opt => opt.MapFrom(src => src.CurrencyCode))
                .ForMember(dest => dest.Unit, opt => opt.MapFrom(src => src.Unit))
                .ForMember(dest => dest.Files, opt => opt.MapFrom(src => src.Files))
                .ForMember(dest => dest.CustomSections, opt => opt.MapFrom(src => src.CustomSections));

            CreateMap<SpecificFileDto, SpecificFile>();
            CreateMap<KpiOptionDto, KpiOption>();
            CreateMap<PeriodConfigurationDto, PeriodConfiguration>();
            CreateMap<SelectedKpiDto, SelectedKpi>();
            CreateMap<DataRowDto, DataRow>();
            CreateMap<KpiValueDto, KpiValue>();
            CreateMap<PdfHighlightDto, Persistence.Models.Specific.PdfHighlight>();
            CreateMap<ExcelHighlightDto, Persistence.Models.Specific.ExcelHighlight>();

            // Map from SpecificKpiDocument to SpecificDto
            CreateMap<Persistence.Models.Specific.SpecificKpiDocument, SpecificDto>()
           .ForMember(dest => dest.JobId, opt => opt.MapFrom(src => src.JobId == Guid.Empty ? null : src.JobId.ToString()))
           .ForMember(dest => dest.CompanyId, opt => opt.MapFrom(src => src.CompanyId))
           .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => src.CompanyName))
           .ForMember(dest => dest.CurrencyCode, opt => opt.MapFrom(src => src.CurrencyCode))
           .ForMember(dest => dest.Unit, opt => opt.MapFrom(src => src.Unit))
           .ForMember(dest => dest.Files, opt => opt.MapFrom(src => src.Files))
           .ForMember(dest => dest.CustomSections, opt => opt.MapFrom(src => src.CustomSections));
            CreateMap<SpecificFile, SpecificFileDto>();
            CreateMap<KpiOption, KpiOptionDto>();
            CreateMap<PeriodConfiguration, PeriodConfigurationDto>();
            CreateMap<SelectedKpi, SelectedKpiDto>();
            CreateMap<DataRow, DataRowDto>();
            CreateMap<KpiValue, KpiValueDto>();
            CreateMap<Persistence.Models.Specific.PdfHighlight, PdfHighlightDto>();
            CreateMap<Persistence.Models.Specific.ExcelHighlight, ExcelHighlightDto>();
            CreateMap<CustomSection, CustomSectionDto>();
            CreateMap<CustomSectionDto, CustomSection>();

        }
    }
}