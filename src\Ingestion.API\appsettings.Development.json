{"ConnectionStrings": {"DefaultDBConnection": "server=BAN-L5KPZS44;database=foliosure_poda;MultipleActiveResultSets=True;Integrated Security=True;TrustServerCertificate=True"}, "TokenConfigurations": {"Audience": "ExempleAudience", "Issuer": "Exemple<PERSON><PERSON>uer", "Minutes": 90, "FinalExpiration": 30, "IdleSession": 30, "SecretKey": "124523621452125942"}, "AWS": {"SecretKey": "nprd/foliosure/pod/dev"}, "IdentityServerConfig": {"Audience": "beat-foliosure-pod-pec-localhost-client-id_services", "Issuer": "https://test.beatapps.net/identity/test/beat/sts", "jwk_uri": "https://test.beatapps.net/identity/test/beat/sts/.well-known/openid-configuration/jwks"}, "Environment": "DEV", "ClientCode": "himera", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.AspNetCore": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "Elasticsearch", "Args": {"nodeUris": "**********************************************************************************/elasticSearch", "indexFormat": "bds-beat-foliosure-ingestion-api-dev-{0:yyyy.MM}", "autoRegisterTemplate": true, "autoRegisterTemplateVersion": "ESv7", "overwriteTemplate": true, "numberOfReplicas": 1, "numberOfShards": 2}}, {"Name": "File", "Args": {"path": ".\\Logs\\PEC-FS-.log", "restrictedToMinimumLevel": "Verbose", "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {ElasticApmTraceId} {ElasticApmTransactionId} {Message:lj} <s:{SourceContext}>{NewLine}{Exception}", "buffered": false, "fileSizeLimitBytes": 10000000, "shared": true, "rollingInterval": "Day", "rollOnFileSizeLimit": true}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "Ingestion.API"}}, "ElasticApm": {"SecretToken": "", "ServerUrls": "https://tools.beatapps.net/APM", "ServiceName": "bds-beat-foliosure-dev-api"}, "ServiceConfiguration": {"AWSS3": {"BucketName": "beat-foliosure-vault", "KeyPrefix": "feature/"}}, "AllowedHosts": "", "AllowedOrigins": "https://*.dev.beatapps.net,https://*.beatfoliosure.com,http://localhost:4200,http://localhost,http://localhost:7285,https://localhost:7285,https://*.test.beatapps.net,https://localhost:4200", "CspTrustedDomains": ["https://*.beatapps.net", "https://*.beatfoliosure.com", "http://localhost:4200", "http://localhost"], "JwtOption": {"TokenCancellationMode": true, "ExpiryTimeInMinute": 60}, "IsWorkflow": false, "IsPendoEnabled": false, "envSettings": {"environment": "<PERSON>", "nprdOrPrd": "nprd", "enableSMTPSecretManager": false, "DEFAULT_REGION": "eu-west-1"}, "EmailConfiguration": {"Email": "<EMAIL>", "Password": "", "UserName": "", "Host": "email-smtp.eu-west-1.amazonaws.com", "Port": "587", "EnvirnmentEmailKey": "beatflow@notification", "EnableSSL": true, "UseDefaultCredentials": false}, "ApiSettings": {"BaseApiUrl": "https://localhost:7288/api/ai"}, "MongoDb": {"ConnectionString": "mongodb://localhost:27017", "DatabaseName": "foliosure_ingestion_dev"}, "SQSSettings": {"RequestQueueUrl": "https://sqs.eu-west-1.amazonaws.com/633874203883/beat-foliosure-ingestion-dev", "ResponseQueueUrl": "https://sqs.eu-west-1.amazonaws.com/633874203883/beat-foliosure-ingestion-dev", "Region": "eu-west-1", "UseSQS": false}, "S3Setting": {"bucketname": "beat-foliosure-vault", "region": "eu-west-1", "keyPrefix": "feature", "folderPrefix": "Documents"}}