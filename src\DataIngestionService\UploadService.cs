using AutoMapper;
using AWSS3.Interfaces;
using Contract;
using DataIngestionService.Constants;
using DataIngestionService.Helpers;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Persistence.Models;
using Persistence.UnitOfWork;
namespace DataIngestionService
{
    public class UploadService(IAWSS3Library awsS3Library, ILogger<UploadService> logger, IUnitOfWork unitOfWork, IMapper mapper,
         ICacheService cacheService, IMongoDb dataService) : IUpload
    {
        private readonly IAWSS3Library _awsS3Library = awsS3Library;
        private readonly ILogger<UploadService> _logger = logger;
        private readonly IUnitOfWork _unitOfWork = unitOfWork;
        private readonly IMapper _mapper = mapper;
        private readonly ICacheService _cacheService = cacheService;
        private readonly IMongoDb _dataService = dataService;
        private const string STATUS_CACHE_KEY = "AllStatus";
        private const string DEFAULT_STATUS_CACHE_KEY = "DefaultStatus";
        public async Task<List<UploadResponseDto>> UploadFile(UploadDto request, int userId)
        {
            var uploadResponses = new List<UploadResponseDto>();
            if (!string.IsNullOrEmpty(request.UpdateProcessId))
                request.ProcessId = request.UpdateProcessId.ConvertToGuid();
            await CreateDocumentMappingDetails(request, userId);
            request.Id = Guid.NewGuid();
            GetTenantId(request);
            foreach (var fileData in request.Files)
            {
                request.Id = Guid.NewGuid();
                string key = $"{_awsS3Library.GetBucketDetails().FolderPrefix}/{request.TenantId}/{request.ProcessId}/{request.Id}{Path.GetExtension(fileData.File.FileName).ToLowerInvariant()}";
                await _awsS3Library.UploadFileAsync(key, fileData.File);

                var document = _mapper.Map<DataIngestionDocuments>(request);
                document.FileName = fileData.File.FileName;
                document.Extension = Path.GetExtension(fileData.File.FileName).ToLowerInvariant();
                document.S3Path = key;
                document.Type = fileData.File.ContentType;
                document.CreatedBy = userId;
                if (!string.IsNullOrEmpty(fileData.Errors))
                    document.Errors = string.Join(",", fileData.Errors?.DeserializeErrors().Where(x => !string.IsNullOrEmpty(x)));

                await _unitOfWork.DataIngestionDocumentRepository.AddAsyn(document);
                await _unitOfWork.SaveAsync();
                _logger.LogInformation("Document created with ID: {DocumentId}", request.Id);

                string s3Url = $"s3://{_awsS3Library.GetBucketDetails().BucketName}/{_awsS3Library.GetBucketDetails().KeyPrefix}/{key}";
                uploadResponses.Add(new UploadResponseDto
                {
                    DocumentId = request.Id,
                    ProcessId = request.ProcessId,
                    Url = s3Url
                });
            }
            return uploadResponses;
        }
        private static string GetTenantId(UploadDto request)
        {
            if (request.Id != Guid.Empty)
            {
                request.TenantId = Guid.Parse("00000000-0000-0000-0000-000000000001");
            }
            return request.TenantId != Guid.Empty ? request.TenantId.ToString() : "00000000-0000-0000-0000-000000000001";
        }
        public async Task<Guid> CreateDocumentMappingDetails(UploadDto request, int userId)
        {
            await _dataService.AddIssuerDetails(request, userId);
            await _dataService.AddSubPageDetails(request, userId);
            bool exists = await _unitOfWork.DIMappingDocumentsDetailsRepository.ExistsAsyncAny(x => x.ProcessId == request.ProcessId);
            if (!exists)
            {
                var document = _mapper.Map<DIMappingDocumentsDetails>(request);
                document.ExtractionType = request.ExtractionType;
                document.FundId = request.FundId;
                document.CreatedBy = userId;
                await _unitOfWork.DIMappingDocumentsDetailsRepository.AddAsyn(document);
                await _unitOfWork.SaveAsync();                _logger.LogDebug("Created new document mapping with ID: {MappingId}", document.DiMappingId);
                return document.ProcessId;
            }
            return Guid.Empty;
        }
        private async Task<IEnumerable<DataIngestionDocuments>> GetDocuments(List<Guid> processIds)
        {
            return await _unitOfWork.DataIngestionDocumentRepository.FindAllAsync(x => !x.IsDeleted && processIds.Contains(x.ProcessId));
        }

        private async Task<IEnumerable<Jobs>> GetJobs(List<Guid> processIds)
        {
            return await _unitOfWork.JobsRepository.FindAllAsync(x => !x.IsDeleted && processIds.Contains(x.ProcessId));
        }

        private async Task<IEnumerable<Status>> GetStatuses()
        {
            return await _cacheService.GetOrSetAsync(STATUS_CACHE_KEY,
               async () => await _unitOfWork.StatusRepository.FindAllAsync(x => !x.IsDeleted),
               TimeSpan.FromDays(1));
        }

        private async Task<IEnumerable<PortfolioCompanyDetails>> GetCompanies(List<int> companyIds)
        {
            return await _unitOfWork.PortfoiloCompanyDetailsRepository.FindAllAsync(x => !x.IsDeleted && companyIds.Contains(x.PortfolioCompanyId));
        }
        private async Task<IEnumerable<FundDetails>> GetFundDetails(List<int> fundIds)
        {
            if (fundIds == null || !fundIds.Any())
                return Enumerable.Empty<FundDetails>();
            return await _unitOfWork.FundDetailsRepository.FindAllAsync(x => !x.IsDeleted && fundIds.Contains(x.FundId));
        }

        /// <summary>
        /// Gets the initials of a user based on their userId
        /// </summary>
        /// <param name="userId">The ID of the user</param>
        /// <returns>The user's initials (first letter of first name + first letter of last name)</returns>
        public async Task<List<UserDetails>> GetUserList()
        {
            return await _unitOfWork.UserDetailsRepository.FindAllAsync(x => !x.IsDeleted);

        }
        /// <summary>
        /// Get User Initials
        /// </summary>
        /// <returns></returns>
        private static string GetUserInitials(UserDetails user)
        {
            string firstInitial = !string.IsNullOrEmpty(user.FirstName) ? user.FirstName.Substring(0, 1) : "";
            string lastInitial = !string.IsNullOrEmpty(user.LastName) ? user.LastName.Substring(0, 1) : "";
            return $"{firstInitial}{lastInitial}".ToUpper();
        }
        public async Task<StatusCount> GetDocumentProcessingStatusCount()
        {
            var statuses = await GetStatuses();
            var defaultStatus = await GetFileStatus();
            var results = (from job in await _unitOfWork.JobsRepository.FindAllAsync(x => !x.IsDeleted)
                           join status in statuses on job.StatusId equals status.Id into statusData
                           from status in statusData.DefaultIfEmpty()
                           select status?.Name ?? defaultStatus.Name)
                          .ToList();
            return new StatusCount
            {
                InProgressCount = results.Count(x => x == ApiConstants.StatusInProgress),
                CompletedCount = results.Count(x => x == ApiConstants.STATUS_COMPLETED),
                FailedCount = results.Count(x => x == ApiConstants.STATUS_FAILED)
            };
        }
        public async Task<IEnumerable<DocumentDetailDto>> GetUploadedDocumentDetails(int limit = 10, int offset = 0)
        {          
            var defaultStatus = await GetFileStatus();
            var query = (from mapping in _unitOfWork.DIMappingDocumentsDetailsRepository.GetQueryable()
                         where (mapping.CompanyId > 0) || (mapping.FundId.HasValue && mapping.FundId.Value > 0)
                         join company in _unitOfWork.PortfoiloCompanyDetailsRepository.GetQueryable()
                            on mapping.CompanyId equals company.PortfolioCompanyId into companyData
                         from company in companyData.DefaultIfEmpty()
                         join fund in _unitOfWork.FundDetailsRepository.GetQueryable()
                            on mapping.FundId equals fund.FundId into fundData
                         from fund in fundData.DefaultIfEmpty()
                         join job in _unitOfWork.JobsRepository.GetQueryable()
                            on mapping.ProcessId equals job.ProcessId into jobData
                         from job in jobData.DefaultIfEmpty()
                         join status in _unitOfWork.StatusRepository.GetQueryable()
                            on job.StatusId equals status.Id into statusData
                         from status in statusData.DefaultIfEmpty()
                         join user in _unitOfWork.UserDetailsRepository.GetQueryable()
                            on mapping.CreatedBy equals user.UserID into userData
                         from user in userData.DefaultIfEmpty()
                         select new DocumentDetailDto
                         {
                             ProcessId = mapping.ProcessId,
                             JobId = job != null ? job.JobId : Guid.Empty,
                             StatusId = job != null ? job.StatusId : defaultStatus.Id,
                             Status = status != null ? status.Name : defaultStatus.Name,
                             State = status != null ? status.State : defaultStatus.State,
                             CompanyName = company != null ? company.CompanyName : string.Empty,
                             CompanyId = company != null ? company.PortfolioCompanyId : 0,
                             FundName = fund != null ? fund.FundName : string.Empty,
                             FundId = fund != null ? fund.FundId : 0,
                             Id = job != null ? job.Id : Guid.Empty,
                             Country = string.Empty,
                             Sector = string.Empty,
                             IsPublic = false,
                             AcuityId = string.Empty,
                             Exchange = string.Empty,
                             Ticker = string.Empty,
                             StartDate = mapping.CreatedOn,
                             Period = string.Empty,
                             CreatedBy = user != null ? $"{user.FirstName} {user.LastName}" : string.Empty,
                             UserInitials = user != null ? GetUserInitials(user) : string.Empty,
                             ExtractionType = mapping.ExtractionType ?? string.Empty,
                         });

            return query
                .OrderByDescending(x => x.StartDate)
                .Skip(offset)
                .Take(limit)
                .ToList();
        }
        public async Task<Status> GetFileStatus(string state = ApiConstants.FileDraftBeforExtract)
        {
            return await _cacheService.GetOrSet(DEFAULT_STATUS_CACHE_KEY,
               async () => await _unitOfWork.StatusRepository.GetFirstOrDefaultAsync(x => x.State == state && !x.IsDeleted),
               TimeSpan.FromDays(1));
        }

        private IEnumerable<DocumentSummaryDto> BuildDocumentSummaries(
            IEnumerable<DataIngestionDocuments> documents)
        {
            if (!documents.Any())
                return Array.Empty<DocumentSummaryDto>();
            var bucketName = $"{_awsS3Library.GetBucketDetails().BucketName}/{_awsS3Library.GetBucketDetails().KeyPrefix}";
            return documents.Select(doc =>
            {
                return new DocumentSummaryDto
                {
                    Id = doc.Id,
                    S3Path = doc.S3Path,
                    Name = doc.FileName,
                    Url = $"s3://{bucketName}/{doc.S3Path}",
                    DocumentTypeId = 0,
                    DocumentType = string.Empty,
                    Errors = string.IsNullOrEmpty(doc.Errors) ? new List<string>() : doc.Errors.Split(',').ToList(),
                    FileStatus = string.IsNullOrEmpty(doc.Errors) ? "valid" : "invalid",
                    Extension = doc.Extension
                };
            });
        }

        public async Task<ProcessDetailsDto> GetProcessDetailsById(Guid processId)
        {
            var defaultStatus = await GetFileStatus();

            // Get documents for the specific process ID only
            var documentsMappings = await _unitOfWork.DIMappingDocumentsDetailsRepository.FindAllAsync(x => !x.IsDeleted && x.ProcessId == processId);

            if (!documentsMappings.Any())
                return new ProcessDetailsDto { ProcessId = processId };

            var docMappingProcessIds = documentsMappings.Select(map => map.ProcessId).ToList();

            // Execute database queries sequentially to prevent DbContext concurrency issues
            var jobs = await GetJobs([processId]);
            var statuses = await GetStatuses();
            var status = statuses.FirstOrDefault(x => !x.IsDeleted && jobs.Select(x => x.Id).ToList().Contains(x.Id));
            var documents = await GetDocuments([processId]);
            // Only query companies if we have mappings with company IDs
            var companyIds = documentsMappings.Select(map => map.CompanyId).ToList();
            var companies = companyIds.Any()
                ? await GetCompanies(companyIds)
                : Enumerable.Empty<PortfolioCompanyDetails>();

            // Build document summaries more efficiently
            var documentSummaries = BuildDocumentSummaries(documents);

            // Get company info if available
            var firstMapping = documentsMappings.FirstOrDefault();
            var company = firstMapping != null && firstMapping.CompanyId > 0
                ? companies.FirstOrDefault(c => c.PortfolioCompanyId == firstMapping.CompanyId)
                : null;
            var fundIds = documentsMappings.Where(x => x.FundId.HasValue && x.FundId.Value > 0)
                       .Select(x => x.FundId.Value)
                       .Distinct()
                       .ToList();
            var fundDetails = fundIds.Any() ? await GetFundDetails(fundIds) : Enumerable.Empty<FundDetails>();
            var fund = firstMapping != null && firstMapping.FundId.HasValue && firstMapping.FundId.Value > 0
              ? fundDetails.FirstOrDefault(c => c.FundId == firstMapping.FundId)
              : null;
            bool isClassifiers = await _dataService.GetClassifiersByProcessId(docMappingProcessIds);
            return new ProcessDetailsDto
            {
                ProcessId = processId,
                Documents = documentSummaries.ToList(),
                CompanyId = firstMapping?.CompanyId.ToString() ?? string.Empty,
                CompanyName = company?.CompanyName ?? string.Empty,
                Year =  0,
                Month = string.Empty,
                Quarter = string.Empty,
                PeriodType = string.Empty,
                EncryptedPortfolioCompanyId = company?.EncryptedPortfolioCompanyId ?? string.Empty,
                JobId = jobs.FirstOrDefault()?.JobId ?? Guid.Empty,
                ParentJobId = jobs?.FirstOrDefault()?.ParentJobId ?? Guid.Empty,
                Status = status?.Name ?? defaultStatus.Name,
                State = status?.State ?? defaultStatus.State,
                StatusId = status?.Id ?? defaultStatus.Id,
                IsClassifiers = isClassifiers,
                FundId = fund?.FundId ?? 0,
                EncryptedFundId = fund?.EncryptedFundId ??string.Empty,
                FundName = fund?.FundName ?? string.Empty,
                ExtractionType = firstMapping?.ExtractionType ?? string.Empty,
            };
        }

        public async Task<bool> UpdateDocumentConfigurations(List<FileConfigurationDetails> request, int userId)
        {
            var fileIds = request.Select(r => r.FileId.ConvertToGuid()).ToList();
            var documents = await _unitOfWork.DataIngestionDocumentRepository.FindAllAsync(x => !x.IsDeleted && fileIds.Contains(x.Id));
            if (documents == null || !documents.Any())
                return false;
            else
            {
                var documentstoUpdate = new List<DataIngestionDocuments>();
                foreach (var document in documents)
                {
                    var updatedDocument = request.FirstOrDefault(x => x.FileId.ConvertToGuid() == document.Id);
                    if (updatedDocument != null)
                    {
                        document.DocumentTypeId = updatedDocument.DocumentTypeId;
                        document.PeriodType = updatedDocument.PeriodType;
                        document.Year = updatedDocument.Year;
                        document.Month = updatedDocument.Month;
                        document.Quarter = updatedDocument.Quarter;
                        document.ModifiedBy = userId;
                        document.ModifiedOn = DateTime.UtcNow;
                        documentstoUpdate.Add(document);
                    }
                }
                _unitOfWork.DataIngestionDocumentRepository.UpdateBulk(documentstoUpdate);
                await _unitOfWork.SaveAsync();
                return true;

            }
        }
    }
}
