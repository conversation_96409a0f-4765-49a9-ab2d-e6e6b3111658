using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class PdfHighlightTests
    {
        [Fact]
        public void PdfHighlight_DefaultInitialization_ShouldHaveNullValues()
        {
            // Act
            var highlight = new PdfHighlight();

            // Assert
            Assert.Null(highlight.Text);
            Assert.Null(highlight.Bounds);
            Assert.Null(highlight.PageNumber);
        }

        [Fact]
        public void PdfHighlight_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var bounds = new List<float> { 10.5f, 20.5f, 30.5f, 40.5f };

            // Act
            var highlight = new PdfHighlight
            {
                Text = "Sample highlighted text",
                Bounds = bounds,
                PageNumber = 5
            };

            // Assert
            Assert.Equal("Sample highlighted text", highlight.Text);
            Assert.Equal(bounds, highlight.Bounds);
            Assert.Equal(4, highlight.Bounds.Count);
            Assert.Equal(10.5f, highlight.Bounds[0]);
            Assert.Equal(20.5f, highlight.Bounds[1]);
            Assert.Equal(30.5f, highlight.Bounds[2]);
            Assert.Equal(40.5f, highlight.Bounds[3]);
            Assert.Equal(5, highlight.PageNumber);
        }

        [Fact]
        public void PdfHighlight_WithEmptyText_ShouldAcceptEmptyString()
        {
            // Act
            var highlight = new PdfHighlight { Text = string.Empty };

            // Assert
            Assert.Equal(string.Empty, highlight.Text);
        }

        [Fact]
        public void PdfHighlight_WithEmptyBounds_ShouldAcceptEmptyList()
        {
            // Act
            var highlight = new PdfHighlight { Bounds = new List<float>() };

            // Assert
            Assert.NotNull(highlight.Bounds);
            Assert.Empty(highlight.Bounds);
        }

        [Fact]
        public void PdfHighlight_WithZeroPageNumber_ShouldAcceptZero()
        {
            // Act
            var highlight = new PdfHighlight { PageNumber = 0 };

            // Assert
            Assert.Equal(0, highlight.PageNumber);
        }

        [Fact]
        public void PdfHighlight_WithNegativePageNumber_ShouldAcceptNegative()
        {
            // Act
            var highlight = new PdfHighlight { PageNumber = -1 };

            // Assert
            Assert.Equal(-1, highlight.PageNumber);
        }

        [Fact]
        public void PdfHighlight_WithLargePageNumber_ShouldAcceptLargeNumbers()
        {
            // Act
            var highlight = new PdfHighlight { PageNumber = 9999 };

            // Assert
            Assert.Equal(9999, highlight.PageNumber);
        }

        [Fact]
        public void PdfHighlight_WithLongText_ShouldAcceptLongString()
        {
            // Arrange
            var longText = new string('A', 1000);

            // Act
            var highlight = new PdfHighlight { Text = longText };

            // Assert
            Assert.Equal(longText, highlight.Text);
        }

        [Fact]
        public void PdfHighlight_WithManyBounds_ShouldAcceptLargeList()
        {
            // Arrange
            var manyBounds = Enumerable.Range(1, 100).Select(i => (float)i).ToList();

            // Act
            var highlight = new PdfHighlight { Bounds = manyBounds };

            // Assert
            Assert.Equal(100, highlight.Bounds.Count);
            Assert.Equal(1f, highlight.Bounds.First());
            Assert.Equal(100f, highlight.Bounds.Last());
        }

        [Fact]
        public void PdfHighlight_BoundsWithNegativeValues_ShouldAcceptNegativeFloats()
        {
            // Arrange
            var boundsWithNegatives = new List<float> { -10.5f, -20.5f, 30.5f, 40.5f };

            // Act
            var highlight = new PdfHighlight { Bounds = boundsWithNegatives };

            // Assert
            Assert.Equal(-10.5f, highlight.Bounds[0]);
            Assert.Equal(-20.5f, highlight.Bounds[1]);
            Assert.Equal(30.5f, highlight.Bounds[2]);
            Assert.Equal(40.5f, highlight.Bounds[3]);
        }
    }
}