﻿using DataIngestionService.Constants;
using Infrastructure.Contract.Extract;

namespace DataIngestionService.Helpers
{
    public static class ResponseFactory
    {
        public static JobStatusResponse CreateJobStatusResponse(Guid jobId, string message)
        {
            return new JobStatusResponse
            {
                IsError = true,
                Status = ApiConstants.ERROR_STATUS,
                Message = message,
                Timestamp = DateTime.UtcNow.ToString(ApiConstants.TIMESTAMP_FORMAT),
                JobId = jobId
            };
        }

        public static DocumentUploadResponse CreateDocumentUploadResponse(string message)
        {
            return new DocumentUploadResponse
            {
                IsError = true,
                Status = ApiConstants.ERROR_STATUS,
                Message = message,
                Timestamp = DateTime.UtcNow.ToString(ApiConstants.TIMESTAMP_FORMAT),
                Files = new List<FileResponse>()
            };
        }

        public static ExtractResponse CreateExtractResponse(string message)
        {
            return new ExtractResponse
            {
                IsError = true,
                Status = ApiConstants.ERROR_STATUS,
                Message = message,
                Timestamp = DateTime.UtcNow.ToString(ApiConstants.TIMESTAMP_FORMAT)
            };
        }

        public static ExtractionDataResponse CreateExtractionDataResponse(string message)
        {
            return new ExtractionDataResponse
            {
                Status = ApiConstants.ERROR_STATUS_LOWERCASE,
                Message = message,
                IsSuccessStatusCode = false
            };
        }
    }
}
