﻿namespace Infrastructure.Contract
{
    public class DocumentDetailDto : CreateJobs
    {
        public string CompanyName { get; set; }
        public int CompanyId { get; set; }
        public DateTime CreatedOn { get; set; }
        public string Status { get; set; }
        public string AcuityId { get; set; }
        public string Country { get; set; }
        public string Sector { get; set; }
        public bool IsPublic { get; set; }
        public string Exchange { get; set; }
        public string Ticker { get; set; }
        public string Period { get; set; }
        public DateTime StartDate { get; set; }
        public string CreatedBy { get; set; }
        public Guid DocumentId { get; set; }
        public string State { get; set; }
        public string UserInitials { get; set; }
        public string FundName { get; set; }
        public int FundId { get; set; }
        public string ExtractionType { get; set; }

    }
}
