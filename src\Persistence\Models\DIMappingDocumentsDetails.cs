﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Persistence.Models
{
    public class DIMappingDocumentsDetails:BaseEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid DiMappingId { get; set; }

        [Required]
        public int CompanyId { get; set; }

        [Required]
        public int SourceTypeId { get; set; }

        [Required]
        public int FeatureId { get; set; }
       
        [Required]
        public Guid ProcessId { get; set; }

        public int? FundId { get; set; }
        public string? ExtractionType { get; set; }
    }
}
