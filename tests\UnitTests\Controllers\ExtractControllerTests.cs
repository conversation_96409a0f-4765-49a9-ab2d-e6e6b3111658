using API.Controllers;
using DataIngestionService.IServices;
using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.Models;
using Persistence.Models.Classifier;
using Persistence.MongoDb;
using System.Security.Claims;
using Xunit;

namespace UnitTests.Controllers
{
    public class ExtractControllerTests
    {
        private readonly Mock<ILogger<ExtractController>> _mockLogger;
        private readonly Mock<IExtract> _mockExtractService;
        private readonly Mock<IFinancialsRepository> _mockFinancialsRepository;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly Mock<IMongoDb> _mockSpecificKpiService;
        private readonly Mock<ISpecificKpiTransformationService> _mockSpecificKpi;
        private readonly ExtractController _controller;

        public ExtractControllerTests()
        {
            _mockLogger = new Mock<ILogger<ExtractController>>();
            _mockExtractService = new Mock<IExtract>();
            _mockFinancialsRepository = new Mock<IFinancialsRepository>();
            _mockHelperService = new Mock<IHelperService>();
            _mockSpecificKpiService = new Mock<IMongoDb>();
            _mockSpecificKpi = new Mock<ISpecificKpiTransformationService>();

            _controller = new ExtractController(
                _mockExtractService.Object,
                _mockLogger.Object,
                _mockFinancialsRepository.Object,
                _mockHelperService.Object,
                _mockSpecificKpi.Object,
                _mockSpecificKpiService.Object
            );

            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.NameIdentifier, "1")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
        }

        [Fact]
        public async Task Details_ValidRequest_ReturnsOk()
        {

            var processId = Guid.NewGuid();
            _mockFinancialsRepository.Setup(f => f.GetMetaDataByJobId(processId)).ReturnsAsync(new Financials());


            var result = await _controller.Details(processId);


            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task AddOrUpdateClassifier_ValidRequest_ReturnsOk()
        {

            var processId = Guid.NewGuid();
            var classifierRequestDto = new ClassifierRequestDto();
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).ReturnsAsync(1);
            _mockExtractService.Setup(e => e.AddOrUpdateClassifierData(It.IsAny<TableSuggestionResponse>(), processId, 1)).ReturnsAsync("123");
            var result = await _controller.AddOrUpdateClassifier(classifierRequestDto, processId);
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }

        [Fact]
        public async Task GetClassifierByProcessId_ValidRequest_ReturnsOk()
        {

            var processId = Guid.NewGuid();
            _mockExtractService.Setup(e => e.GetClassifierDataByProcessIdAsync(processId)).ReturnsAsync(new Classifier { TableSuggestionResponse = new TableSuggestionResponse() });


            var result = await _controller.GetClassifierByProcessId(processId);


            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.NotNull(okResult.Value);
        }
        [Fact]
        public async Task Details_InvalidModelState_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.Details(processId);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsType<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            Assert.Equal(new[] { "error" }, errors["key"] as string[]);
        }

        [Fact]
        public async Task GetClassifierByProcessId_InvalidModelState_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.GetClassifierByProcessId(processId);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsType<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            Assert.Equal(new[] { "error" }, errors["key"] as string[]);
        }

        [Fact]
        public async Task GetSpecificKpiDocument_InvalidModelState_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.GetSpecificKpiDocument(processId);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsType<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            Assert.Equal(new[] { "error" }, errors["key"] as string[]);
        }

        [Fact]
        public async Task UpdateSpecificKpiDocumentApi_InvalidModelState_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            var specificDto = new SpecificDto();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.UpdateSpecificKpiDocumentApi(specificDto, processId);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsType<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            Assert.Equal(new[] { "error" }, errors["key"] as string[]);
        }
        [Fact]
        public async Task AddOrUpdateClassifier_InvalidModelState_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            var classifierRequestDto = new ClassifierRequestDto();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.AddOrUpdateClassifier(classifierRequestDto, processId);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsType<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            Assert.Equal(new[] { "error" }, errors["key"] as string[]);
        }
        [Fact]
        public async Task UpdateFinancials_ValidRequest_ReturnsOk()
        {
            var processId = Guid.NewGuid();
            var data = new FinancialsData();
            _mockExtractService.Setup(e => e.UpdateFinancials(data, processId)).ReturnsAsync(true);

            var result = await _controller.UpdateFinancials(data, processId);

            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value);
        }

        [Fact]
        public async Task UpdateFinancials_ProcessIdEmpty_ReturnsBadRequest()
        {
            var data = new FinancialsData();
            var result = await _controller.UpdateFinancials(data, Guid.Empty);

            var badRequest = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Process ID cannot be empty.", badRequest.Value);
        }

        [Fact]
        public async Task UpdateFinancials_Exception_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            var data = new FinancialsData();
            _mockExtractService.Setup(e => e.UpdateFinancials(data, processId)).ThrowsAsync(new Exception("fail"));

            var result = await _controller.UpdateFinancials(data, processId);

            var badRequest = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("fail", badRequest.Value);
        }

        [Fact]
        public async Task GetClassifierByProcessId_ClassifierDataNull_ReturnsNotFound()
        {
            var processId = Guid.NewGuid();
            _mockExtractService.Setup(e => e.GetClassifierDataByProcessIdAsync(processId)).ReturnsAsync((Classifier)null);

            var result = await _controller.GetClassifierByProcessId(processId);

            var notFound = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Contains(processId.ToString(), notFound.Value.ToString());
        }

        [Fact]
        public async Task GetClassifierByProcessId_ProcessIdEmpty_ReturnsBadRequest()
        {
            var result = await _controller.GetClassifierByProcessId(Guid.Empty);

            var badRequest = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Process ID cannot be empty.", badRequest.Value);
        }

        [Fact]
        public async Task GetSpecificKpiDocument_DocumentNull_ReturnsNotFound()
        {
            var processId = Guid.NewGuid();
            _mockSpecificKpi.Setup(s => s.GetSpecificKpiDocumentByProcessId(processId)).ReturnsAsync((SpecificDto)null);

            var result = await _controller.GetSpecificKpiDocument(processId);

            var notFound = Assert.IsType<NotFoundObjectResult>(result);
            Assert.Contains(processId.ToString(), notFound.Value.ToString());
        }

        [Fact]
        public async Task GetSpecificKpiDocument_ValidRequest_ReturnsOk()
        {
            var processId = Guid.NewGuid();
            var dto = new SpecificDto();
            _mockSpecificKpi.Setup(s => s.GetSpecificKpiDocumentByProcessId(processId)).ReturnsAsync(dto);

            var result = await _controller.GetSpecificKpiDocument(processId);

            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(dto, okResult.Value);
        }        
    }
}
