using Infrastructure.DTOS.NonControllerDto;
using Persistence.Models.Classifier;

namespace Infrastructure.DTOS.Mappers
{
    public static class ClassifierMapper
    {
        public static TableSuggestionResponse MapToTableSuggestionResponse(ClassifierRequestDto dto)
        {
            if (dto == null)
                return null;

            return new TableSuggestionResponse
            {
                IsError = dto.IsError,
                Status = dto.Status,
                JobId = dto.JobId,
                Timestamp = dto.Timestamp,
                Message = dto.Message,
                Files = dto.Files?.Select(MapToFileInfo).ToList(),
                Tables = dto.Tables?.Select(MapToTableType).ToList()
            };
        }

        private static Persistence.Models.Classifier.FileInfo MapToFileInfo(ClassifierFileInfoDto dto)
        {
            if (dto == null)
                return null;

            return new Persistence.Models.Classifier.FileInfo
            {
                FileName = dto.FileName,
                S3Path = dto.S3Path,
                Source = dto.Source,
                FileId = dto.FileId
            };
        }

        private static TableType MapToTableType(ClassifierTableTypeDto dto)
        {
            if (dto == null)
                return null;

            return new TableType
            {
                Label = dto.Label,
                Suggestions = dto.Suggestions?.Select(MapToTableSuggestion).ToList()
            };
        }

        private static TableSuggestion MapToTableSuggestion(ClassifierTableSuggestionDto dto)
        {
            if (dto == null)
                return null;

            return new TableSuggestion
            {
                Bbox = MapToTableBoundingBox(dto.Bbox),
                Page = dto.Page,
                FileId = dto.FileId,
                Score = dto.Score
            };
        }

        private static TableBoundingBox MapToTableBoundingBox(ClassifierBboxDto dto)
        {
            if (dto == null)
                return null;

            return new TableBoundingBox
            {
                X1 = dto.X1,
                Y1 = dto.Y1,
                X2 = dto.X2,
                Y2 = dto.Y2
            };
        }

        public static ClassifierRequestDto MapToClassifierRequestDto(TableSuggestionResponse response)
        {
            if (response == null)
                return null;

            return new ClassifierRequestDto
            {
                IsError = response.IsError,
                Status = response.Status,
                JobId = response.JobId,
                Timestamp = response.Timestamp,
                Message = response.Message,
                Files = response.Files?.Select(MapToClassifierFileInfoDto).ToList(),
                Tables = response.Tables?.Select(MapToClassifierTableTypeDto).ToList()
            };
        }

        private static ClassifierFileInfoDto MapToClassifierFileInfoDto(Persistence.Models.Classifier.FileInfo fileInfo)
        {
            if (fileInfo == null)
                return null;

            return new ClassifierFileInfoDto
            {
                FileName = fileInfo.FileName,
                S3Path = fileInfo.S3Path,
                Source = fileInfo.Source,
                FileId = fileInfo.FileId
            };
        }

        private static ClassifierTableTypeDto MapToClassifierTableTypeDto(TableType tableType)
        {
            if (tableType == null)
                return null;

            return new ClassifierTableTypeDto
            {
                Label = tableType.Label,
                Suggestions = tableType.Suggestions?.Select(MapToClassifierTableSuggestionDto).ToList()
            };
        }

        private static ClassifierTableSuggestionDto MapToClassifierTableSuggestionDto(TableSuggestion suggestion)
        {
            if (suggestion == null)
                return null;

            return new ClassifierTableSuggestionDto
            {
                Bbox = MapToClassifierBboxDto(suggestion.Bbox),
                Page = suggestion.Page,
                FileId = suggestion.FileId,
                Score = suggestion.Score
            };
        }

        private static ClassifierBboxDto MapToClassifierBboxDto(TableBoundingBox bbox)
        {
            if (bbox == null)
                return null;

            return new ClassifierBboxDto
            {
                X1 = bbox.X1,
                Y1 = bbox.Y1,
                X2 = bbox.X2,
                Y2 = bbox.Y2
            };
        }
    }
}