using Infrastructure.DTOS.Mappers;
using Infrastructure.DTOS.NonControllerDto;
using Persistence.Models.Classifier;
using System;
using System.Collections.Generic;
using Xunit;
using FileInfo = Persistence.Models.Classifier.FileInfo;

namespace UnitTests.Infrastructure.DTOS.Mappers
{
    public class ClassifierMapperTests
    {
        [Fact]
        public void MapToTableSuggestionResponse_ValidInput_ShouldMapCorrectly()
        {
            // Arrange
            var dto = new ClassifierRequestDto
            {
                IsError = false,
                Status = "success",
                JobId = "test-job-id",
                Timestamp = DateTime.UtcNow.ToString("o"),
                Message = "Test message",
                Files = new List<ClassifierFileInfoDto>
                {
                    new ClassifierFileInfoDto
                    {
                        FileName = "test.pdf",
                        S3Path = "s3://test/test.pdf",
                        Source = "upload",
                        FileId = "file-id-1"
                    }
                },
                Tables = new List<ClassifierTableTypeDto>
                {
                    new ClassifierTableTypeDto
                    {
                        Label = "Income Statement",
                        Suggestions = new List<ClassifierTableSuggestionDto>
                        {
                            new ClassifierTableSuggestionDto
                            {
                                Page = 1,
                                FileId = "file-id-1",
                                Score = 0.95,
                                Bbox = new ClassifierBboxDto
                                {
                                    X1 = 100,
                                    Y1 = 200,
                                    X2 = 400,
                                    Y2 = 600
                                }
                            }
                        }
                    }
                }
            };

            // Act
            var result = ClassifierMapper.MapToTableSuggestionResponse(dto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(dto.IsError, result.IsError);
            Assert.Equal(dto.Status, result.Status);
            Assert.Equal(dto.JobId, result.JobId);
            Assert.Equal(dto.Message, result.Message);
            
            // Verify Files mapping
            Assert.Single(result.Files);
            var file = result.Files[0];
            var dtoFile = dto.Files[0];
            Assert.Equal(dtoFile.FileName, file.FileName);
            Assert.Equal(dtoFile.S3Path, file.S3Path);
            Assert.Equal(dtoFile.Source, file.Source);
            Assert.Equal(dtoFile.FileId, file.FileId);

            // Verify Tables mapping
            Assert.Single(result.Tables);
            var table = result.Tables[0];
            var dtoTable = dto.Tables[0];
            Assert.Equal(dtoTable.Label, table.Label);
            Assert.Single(table.Suggestions);

            // Verify Table Suggestion details
            var suggestion = table.Suggestions[0];
            var dtoSuggestion = dtoTable.Suggestions[0];
            Assert.Equal(dtoSuggestion.Page, suggestion.Page);
            Assert.Equal(dtoSuggestion.Score, suggestion.Score);
            Assert.Equal(dtoSuggestion.FileId, suggestion.FileId);
            
            // Verify Bounding Box
            Assert.NotNull(suggestion.Bbox);
            Assert.Equal(dtoSuggestion.Bbox.X1, suggestion.Bbox.X1);
            Assert.Equal(dtoSuggestion.Bbox.Y1, suggestion.Bbox.Y1);
            Assert.Equal(dtoSuggestion.Bbox.X2, suggestion.Bbox.X2);
            Assert.Equal(dtoSuggestion.Bbox.Y2, suggestion.Bbox.Y2);
        }

        [Fact]
        public void MapToClassifierRequestDto_ValidInput_ShouldMapCorrectly()
        {
            // Arrange
            var response = new TableSuggestionResponse
            {
                IsError = false,
                Status = "success",
                JobId = "test-job-id",
                Timestamp = DateTime.UtcNow.ToString("o"),
                Message = "Test message",
                Files = new List<FileInfo>
                {
                    new FileInfo
                    {
                        FileName = "test.pdf",
                        S3Path = "s3://test/test.pdf",
                        Source = "upload",
                        FileId = "file-id-1"
                    }
                },
                Tables = new List<TableType>
                {
                    new TableType
                    {
                        Label = "Income Statement",
                        Suggestions = new List<TableSuggestion>
                        {
                            new TableSuggestion
                            {
                                Page = 1,
                                FileId = "file-id-1",
                                Score = 0.95,
                                Bbox = new TableBoundingBox
                                {
                                    X1 = 100,
                                    Y1 = 200,
                                    X2 = 400,
                                    Y2 = 600
                                }
                            }
                        }
                    }
                }
            };

            // Act
            var result = ClassifierMapper.MapToClassifierRequestDto(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(response.IsError, result.IsError);
            Assert.Equal(response.Status, result.Status);
            Assert.Equal(response.JobId, result.JobId);
            Assert.Equal(response.Message, result.Message);

            // Verify Files mapping
            Assert.Single(result.Files);
            var file = result.Files[0];
            var responseFile = response.Files[0];
            Assert.Equal(responseFile.FileName, file.FileName);
            Assert.Equal(responseFile.S3Path, file.S3Path);
            Assert.Equal(responseFile.Source, file.Source);
            Assert.Equal(responseFile.FileId, file.FileId);

            // Verify Tables mapping
            Assert.Single(result.Tables);
            var table = result.Tables[0];
            var responseTable = response.Tables[0];
            Assert.Equal(responseTable.Label, table.Label);
            Assert.Single(table.Suggestions);
            
            // Verify Table Suggestion details
            var suggestion = responseTable.Suggestions[0];
            var resultSuggestion = table.Suggestions[0];
            Assert.Equal(suggestion.Page, resultSuggestion.Page);
            Assert.Equal(suggestion.Score, resultSuggestion.Score);
            Assert.Equal(suggestion.FileId, resultSuggestion.FileId);

            // Verify Bounding Box
            Assert.NotNull(resultSuggestion.Bbox);
            Assert.Equal(suggestion.Bbox.X1, resultSuggestion.Bbox.X1);
            Assert.Equal(suggestion.Bbox.Y1, resultSuggestion.Bbox.Y1);
            Assert.Equal(suggestion.Bbox.X2, resultSuggestion.Bbox.X2);
            Assert.Equal(suggestion.Bbox.Y2, resultSuggestion.Bbox.Y2);
        }

        [Fact]
        public void MapToTableSuggestionResponse_NullInput_ShouldReturnNull()
        {
            // Act
            var result = ClassifierMapper.MapToTableSuggestionResponse(null);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void MapToClassifierRequestDto_NullInput_ShouldReturnNull()
        {
            // Act
            var result = ClassifierMapper.MapToClassifierRequestDto(null);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void MapToTableSuggestionResponse_EmptyLists_ShouldMapCorrectly()
        {
            // Arrange
            var dto = new ClassifierRequestDto
            {
                IsError = false,
                Status = "success",
                JobId = "test-job-id",
                Timestamp = DateTime.UtcNow.ToString("o"),
                Message = "Test message",
                Files = new List<ClassifierFileInfoDto>(),
                Tables = new List<ClassifierTableTypeDto>()
            };

            // Act
            var result = ClassifierMapper.MapToTableSuggestionResponse(dto);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Files);
            Assert.Empty(result.Files);
            Assert.NotNull(result.Tables);
            Assert.Empty(result.Tables);
        }

        [Fact]
        public void MapToClassifierRequestDto_EmptyLists_ShouldMapCorrectly()
        {
            // Arrange
            var response = new TableSuggestionResponse
            {
                IsError = false,
                Status = "success",
                JobId = "test-job-id",
                Timestamp = DateTime.UtcNow.ToString("o"),
                Message = "Test message",
                Files = new List<FileInfo>(),
                Tables = new List<TableType>()
            };

            // Act
            var result = ClassifierMapper.MapToClassifierRequestDto(response);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Files);
            Assert.Empty(result.Files);
            Assert.NotNull(result.Tables);
            Assert.Empty(result.Tables);
        }        [Theory]
        [InlineData((string)null)]
        [InlineData("")]
        public void MapToTableSuggestionResponse_NullOrEmptyStrings_ShouldMapCorrectly(string value)
        {
            // Arrange
            var dto = new ClassifierRequestDto
            {
                IsError = false,
                Status = value,
                JobId = value,
                Timestamp = value,
                Message = value,
                Files = new List<ClassifierFileInfoDto>
                {
                    new ClassifierFileInfoDto
                    {
                        FileName = value,
                        S3Path = value,
                        Source = value,
                        FileId = value
                    }
                }
            };

            // Act
            var result = ClassifierMapper.MapToTableSuggestionResponse(dto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(value, result.Status);
            Assert.Equal(value, result.JobId);
            Assert.Equal(value, result.Timestamp);
            Assert.Equal(value, result.Message);
            Assert.Single(result.Files);
            Assert.Equal(value, result.Files[0].FileName);
            Assert.Equal(value, result.Files[0].S3Path);
            Assert.Equal(value, result.Files[0].Source);
            Assert.Equal(value, result.Files[0].FileId);
        }
    }
}
