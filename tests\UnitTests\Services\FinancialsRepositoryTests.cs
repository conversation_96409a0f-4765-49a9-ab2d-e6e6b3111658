using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Moq;
using MongoDB.Driver;
using Persistence.Models;
using Persistence.MongoDb;
using Xunit;
using System.Collections.Generic;

namespace UnitTests.Services
{
    public class FinancialsRepositoryTests
    {
        private readonly Mock<IMongoCollection<Financials>> _mockCollection;
        private readonly Mock<IOptions<MongoDbSettings>> _mockOptions;
        private readonly Mock<IMongoDatabase> _mockDatabase;
        private readonly Mock<IMongoClient> _mockClient;
        private readonly FinancialsRepository _repository;
        private readonly MongoDbSettings _settings;

        public FinancialsRepositoryTests()
        {
            _mockCollection = new Mock<IMongoCollection<Financials>>();
            _mockOptions = new Mock<IOptions<MongoDbSettings>>();
            _mockDatabase = new Mock<IMongoDatabase>();
            _mockClient = new Mock<IMongoClient>();
            _settings = new MongoDbSettings { ConnectionString = "mongodb://localhost:27017", DatabaseName = "TestDb" };
            _mockOptions.Setup(x => x.Value).Returns(_settings);

            // Setup MongoClient to return our mock database
            _mockClient.Setup(c => c.GetDatabase(_settings.DatabaseName, null)).Returns(_mockDatabase.Object);
            _mockDatabase.Setup(d => d.GetCollection<Financials>(It.IsAny<string>(), null)).Returns(_mockCollection.Object);

            // Use a derived class to inject mocks
            _repository = new FinancialsRepository(_mockOptions.Object);
            typeof(Repository<Financials>)
                .GetField("_collection", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic)
                .SetValue(_repository, _mockCollection.Object);
        }

        [Fact]
        public async Task AddFinancialsAsync_ShouldSetIdAndIsDeletedAndCallCreateAsync()
        {
            var details = new Financials { Name = "Test", ProcessID = Guid.NewGuid() };
            _mockCollection.Setup(x => x.InsertOneAsync(details, null, default(CancellationToken))).Returns(Task.CompletedTask).Verifiable();

            var id = await _repository.AddFinancialsAsync(details);

            Assert.NotEqual(Guid.Empty, id);
            Assert.False(details.IsDeleted);
        }

        [Fact]
        public async Task UpdateFinancialsByProcessIdAsync_ShouldReturnTrueIfModified()
        {
            var processId = Guid.NewGuid();
            var details = new Financials { Id = Guid.NewGuid(), ProcessID = processId, IsDeleted = false };
            var updateResult = new Mock<ReplaceOneResult>();
            updateResult.SetupGet(x => x.ModifiedCount).Returns(1);
            _mockCollection.Setup(x => x.ReplaceOneAsync(
                It.IsAny<FilterDefinition<Financials>>(),
                details,
                It.IsAny<ReplaceOptions>(),
                It.IsAny<CancellationToken>())).ReturnsAsync(updateResult.Object);

            var result = await _repository.UpdateFinancialsByProcessIdAsync(processId, details);
            Assert.True(result);
        }

        [Fact]
        public async Task UpdateFinancialsByProcessIdAsync_ShouldReturnFalseIfNotModified()
        {
            var processId = Guid.NewGuid();
            var details = new Financials { Id = Guid.NewGuid(), ProcessID = processId, IsDeleted = false };
            var updateResult = new Mock<ReplaceOneResult>();
            updateResult.SetupGet(x => x.ModifiedCount).Returns(0);
            _mockCollection.Setup(x => x.ReplaceOneAsync(
                It.IsAny<FilterDefinition<Financials>>(),
                details,
                It.IsAny<ReplaceOptions>(),
                It.IsAny<CancellationToken>())).ReturnsAsync(updateResult.Object);

            var result = await _repository.UpdateFinancialsByProcessIdAsync(processId, details);
            Assert.False(result);
        }
    }
} 