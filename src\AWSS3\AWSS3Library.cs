﻿using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using AWSS3.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Runtime;
using System.Text;

namespace AWSS3;

/// <summary>
/// Provides methods to upload, retrieve, and delete files in an AWS S3 bucket.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="AWSS3Upload"/> class.
/// </remarks>
/// <param name="s3Client">The Amazon S3 client used to interact with the S3 service.</param>
/// <param name="bucketName">The name of the S3 bucket.</param>
public class AWSS3Library(IOptions<S3Setting> options, IAmazonS3 s3Client, ILogger<AWSS3Library> logger) : IAWSS3Library
{

    /// The Amazon S3 client used to interact with the S3 service.   
    private readonly IAmazonS3 _s3Client = s3Client;
    private readonly string _bucketName = options.Value.BucketName;
    private readonly string _keyPrefix = options.Value.KeyPrefix;

    /// Logger for logging information and errors.
    private readonly ILogger _logger = logger;

    /// <summary>
    /// Uploads a file to the specified S3 bucket.
    /// </summary>
    /// <param name="key">The key under which the file is stored in the S3 bucket.</param>
    /// <param name="fileStream">The stream containing the file data to upload.</param>
    /// <returns>A task that represents the asynchronous upload operation.</returns>
    public async Task<string?> UploadFileAsync(string key, IFormFile file)
    {
        try
        {
            using (Stream stream = file.OpenReadStream())
            {
                TransferUtilityUploadRequest uploadRequest = new()
                {
                    InputStream = stream,
                    Key =$"{ _keyPrefix}/{key}",
                    BucketName = _bucketName,
                };

                TransferUtility fileTransferUtility = new(_s3Client);
                await fileTransferUtility.UploadAsync(uploadRequest);
            }
            GetPreSignedUrlRequest urlRequest = new()
            {
                BucketName = _bucketName,
                Key = $"{_keyPrefix}/{key}",
                Expires = DateTime.UtcNow.AddHours(1) // URL valid for 1 hour
            };
            return await _s3Client.GetPreSignedURLAsync(urlRequest);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload file: {FileName}", file.FileName);
            return null;
        }
    }

    /// <summary>
    /// Retrieves a file from the specified S3 bucket.
    /// </summary>
    /// <param name="key">The key of the file to retrieve.</param>
    /// <returns>A task that represents the asynchronous retrieval operation, containing the file stream.</returns>
    public async Task<string> GetFileAsync(string key)
    {
        try
        {
            GetObjectRequest getRequest = new()
            {
                BucketName = _bucketName,
                Key = key
            };
            using GetObjectResponse response = await _s3Client.GetObjectAsync(getRequest);
            MemoryStream memoryStream = new();
            await response.ResponseStream.CopyToAsync(memoryStream);
            memoryStream.Position = 0;
            byte[] fileBytes = memoryStream.ToArray();
            string result = Convert.ToBase64String(fileBytes);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get file with key: {Key}", key);
            throw new InvalidOperationException($"An error occurred while retrieving the file with key '{key}' from S3.", ex);
        }
    }
    /// <summary>
    /// Downloads a file object directly from S3 storage
    /// </summary>
    /// <param name="keyPath">The S3 key path of the file to download</param>
    /// <returns>The S3 GetObjectResponse containing the file stream and metadata, or null if the request fails</returns>
    public async Task<GetObjectResponse?> DownloadFileObject(string keyPath)
    {
        GetObjectResponse response = await _s3Client.GetObjectAsync(_bucketName, $"{_keyPrefix}/{keyPath}");
        if (response.HttpStatusCode == HttpStatusCode.OK)
            return response;
        else
            return null;
    }

    /// <summary>
    /// Deletes a file from the specified S3 bucket.
    /// </summary>
    /// <param name="key">The key of the file to delete.</param>
    /// <returns>A task that represents the asynchronous delete operation.</returns>
    public async Task DeleteFileAsync(string key)
    {
        DeleteObjectRequest deleteRequest = new()
        {
            BucketName = _bucketName,
            Key = $"{_keyPrefix}/{key}"
        };
        _ = await _s3Client.DeleteObjectAsync(deleteRequest);
    }

    /// <summary>
    /// Generates a pre-signed URL for accessing a file in the S3 bucket.
    /// </summary>
    /// <param name="key">The key of the file for which to generate the pre-signed URL.</param>
    /// <returns>A task that represents the asynchronous operation, containing the pre-signed URL as a string.</returns>
    public string GetPresignedUrl(string key)
    {

        GetPreSignedUrlRequest urlRequest = new()
        {
            BucketName = _bucketName,
            Key = key,
            Expires = DateTime.UtcNow.AddHours(1) // URL valid for 1 hour

        };

        return _s3Client.GetPreSignedURL(urlRequest);

    }
    public S3Setting GetBucketDetails()
    {
        return options.Value;
    }
    public async Task<StringBuilder> GetFileContentAsTextAsync(string filePath)
    {
        try
        {
            string normalizedKey = filePath.Replace($"s3://{_bucketName}/", "");
            GetObjectRequest getRequest = new()
            {
                BucketName = _bucketName,
                Key = normalizedKey,
            };
            using GetObjectResponse response = await _s3Client.GetObjectAsync(getRequest);
            using MemoryStream memoryStream = new();
            await response.ResponseStream.CopyToAsync(memoryStream);
            memoryStream.Position = 0; 
            using StreamReader reader = new(memoryStream);
            StringBuilder result = new();
            string? line;
            while ((line = await reader.ReadLineAsync()) != null)
            {
                result.AppendLine(line);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex.Message);
            throw;
        }
    }

}