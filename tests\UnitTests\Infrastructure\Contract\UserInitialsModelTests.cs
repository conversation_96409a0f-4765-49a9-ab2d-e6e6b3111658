using Infrastructure.Contract;
using Xunit;

namespace UnitTests.Infrastructure.Contract
{
    public class UserInitialsModelTests
    {
        [Fact]
        public void UserInitialsModel_DefaultValues_ShouldBeSetCorrectly()
        {
            // Act
            var userInitialsModel = new UserInitialsModel();

            // Assert
            Assert.Equal(0, userInitialsModel.UserId);
            Assert.Null(userInitialsModel.Initials);
        }

        [Fact]
        public void UserInitialsModel_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var userInitialsModel = new UserInitialsModel
            {
                UserId = 12345,
                Initials = "JD"
            };

            // Assert
            Assert.Equal(12345, userInitialsModel.UserId);
            Assert.Equal("JD", userInitialsModel.Initials);
        }

        [Fact]
        public void UserInitialsModel_WithEdgeCaseValues_ShouldHandleCorrectly()
        {
            // Act
            var userInitialsModel = new UserInitialsModel
            {
                UserId = -1,
                Initials = ""
            };

            // Assert
            Assert.Equal(-1, userInitialsModel.UserId);
            Assert.Equal("", userInitialsModel.Initials);
        }
    }
}