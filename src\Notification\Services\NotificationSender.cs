using Microsoft.AspNetCore.SignalR;
using Notification.Hubs;
using Notification.Models;

namespace Notification.Services
{
    /// <summary>
    /// Simple service for sending notifications via SignalR
    /// </summary>
    public class NotificationSender
    {
        private readonly IHubContext<NotificationHub> _hubContext;

        /// <summary>
        /// Initializes a new instance of the NotificationSender class
        /// </summary>
        /// <param name="hubContext">The SignalR hub context</param>
        public NotificationSender(IHubContext<NotificationHub> hubContext)
        {
            _hubContext = hubContext;
        }

        /// <summary>
        /// Send a notification to all connected clients
        /// </summary>
        /// <param name="notification">The notification to send</param>
        public async Task SendToAllAsync(NotificationMessage notification)
        {
            await _hubContext.Clients.All.SendAsync("ReceiveNotification", notification);
        }

        /// <summary>
        /// Send a notification to a specific user
        /// </summary>
        /// <param name="userId">The user ID to send notification to</param>
        /// <param name="notification">The notification to send</param>
        public async Task SendToUserAsync(string userId, NotificationMessage notification)
        {
            await _hubContext.Clients.User(userId).SendAsync("ReceiveNotification", notification);
        }

        /// <summary>
        /// Send a notification about a specific entity
        /// </summary>
        /// <param name="entityType">Type of entity (e.g., "document", "portfolio")</param>
        /// <param name="entityId">ID of the entity</param>
        /// <param name="notification">The notification to send</param>
        public async Task SendEntityNotificationAsync(string entityType, string entityId, NotificationMessage notification)
        {
            // Update entity information in the notification
            notification.EntityType = entityType;
            notification.EntityId = entityId;
            
            // Send to all clients
            await _hubContext.Clients.All.SendAsync("ReceiveNotification", notification);
        }
    }
}
