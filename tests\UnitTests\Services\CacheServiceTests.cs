using DataIngestionService.Services;
using Microsoft.Extensions.Caching.Memory;
using Xunit;

namespace UnitTests.Services
{
    public class CacheServiceTests
    {
        private readonly IMemoryCache _realMemoryCache;
        private readonly CacheService _cacheService;

        public CacheServiceTests()
        {
            _realMemoryCache = new MemoryCache(new MemoryCacheOptions());
            _cacheService = new CacheService(_realMemoryCache);
        }

        [Fact]
        public void GetOrSet_ReturnsCachedValue_WhenExists()
        {
            var name = "test-key";
            var expected = "cached-value";
            _realMemoryCache.Set(name, expected);

            var result = _cacheService.GetOrSet(name, () => "new-value");

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetOrSetAsync_ReturnsCachedValue_WhenExists()
        {
            var name = "test-key";
            var expected = "cached-value";
            _realMemoryCache.Set(name, expected);

            var result = await _cacheService.GetOrSetAsync(name, () => Task.FromResult("new-value"));

            Assert.Equal(expected, result);
        }

        [Fact]
        public void Remove_RemovesValueFromCache()
        {
            var name = "test-key";
            _realMemoryCache.Set(name, "value");

            _cacheService.Remove(name);

            Assert.False(_realMemoryCache.TryGetValue(name, out _));
        }

        [Fact]
        public void RemoveMany_RemovesAllValuesFromCache()
        {
            var keys = new[] { "key1", "key2", "key3" };
            foreach (var key in keys)
                _realMemoryCache.Set(key, "value");

            _cacheService.RemoveMany(keys);

            foreach (var key in keys)
                Assert.False(_realMemoryCache.TryGetValue(key, out _));
        }

        [Fact]
        public void GetOrSet_SetsValue_WhenNotInCache()
        {
            var name = "test-key";
            var expected = "new-value";

            var result = _cacheService.GetOrSet(name, () => expected);

            Assert.Equal(expected, result);
            Assert.True(_realMemoryCache.TryGetValue(name, out var cached));
            Assert.Equal(expected, cached);
        }

        [Fact]
        public async Task GetOrSetAsync_SetsValue_WhenNotInCache()
        {
            var name = "test-key";
            var expected = "new-value";

            var result = await _cacheService.GetOrSetAsync(name, () => Task.FromResult(expected));

            Assert.Equal(expected, result);
            Assert.True(_realMemoryCache.TryGetValue(name, out var cached));
            Assert.Equal(expected, cached);
        }

        [Fact]
        public async Task GetOrSet_UsesCustomExpiration()
        {
            var name = "test-key";
            var expected = "new-value";
            var customExpiration = TimeSpan.FromMilliseconds(100);

            _cacheService.GetOrSet(name, () => expected, customExpiration);

            Assert.True(_realMemoryCache.TryGetValue(name, out _));
            await Task.Delay(200);
            Assert.False(_realMemoryCache.TryGetValue(name, out _));
        }

        [Fact]
        public async Task GetOrSetAsync_UsesCustomExpiration()
        {
            var name = "test-key";
            var expected = "new-value";
            var customExpiration = TimeSpan.FromMilliseconds(100);

            await _cacheService.GetOrSetAsync(name, () => Task.FromResult(expected), customExpiration);

            Assert.True(_realMemoryCache.TryGetValue(name, out _));
            await Task.Delay(200);
            Assert.False(_realMemoryCache.TryGetValue(name, out _));
        }

        [Fact]
        public void GetOrSet_WithNullKey_ShouldThrowArgumentNullException()
        {
            // Arrange
            string? nullKey = null;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => 
                _cacheService.GetOrSet(nullKey!, () => "value"));
        }

        [Fact]
        public async Task GetOrSetAsync_WithNullKey_ShouldThrowArgumentNullException()
        {
            // Arrange
            string? nullKey = null;

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => 
                _cacheService.GetOrSetAsync(nullKey!, () => Task.FromResult("value")));
        }

        [Fact]
        public void RemoveMany_WithEmptyArray_ShouldNotThrow()
        {
            // Arrange
            var emptyKeys = Array.Empty<string>();

            // Act & Assert
            var exception = Record.Exception(() => _cacheService.RemoveMany(emptyKeys));
            Assert.Null(exception);
        }
    }
}