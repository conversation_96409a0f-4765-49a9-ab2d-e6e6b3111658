using DataIngestionService.Constants;
using DataIngestionService.Helpers;
using Xunit;

namespace UnitTests.Helpers
{
    public class ResponseFactoryTests
    {
        [Fact]
        public void CreateJobStatusResponse_ShouldReturnCorrectResponse()
        {
            // Arrange
            var jobId = Guid.NewGuid();
            var message = "Test error message";

            // Act
            var response = ResponseFactory.CreateJobStatusResponse(jobId, message);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsError);
            Assert.Equal(ApiConstants.ERROR_STATUS, response.Status);
            Assert.Equal(message, response.Message);
            Assert.Equal(jobId, response.JobId);
            Assert.NotNull(response.Timestamp);
            // Verify timestamp format
            Assert.True(DateTime.TryParse(response.Timestamp, out _));
        }

        [Fact]
        public void CreateDocumentUploadResponse_ShouldReturnCorrectResponse()
        {
            // Arrange
            var message = "Test error message";

            // Act
            var response = ResponseFactory.CreateDocumentUploadResponse(message);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsError);
            Assert.Equal(ApiConstants.ERROR_STATUS, response.Status);
            Assert.Equal(message, response.Message);
            Assert.NotNull(response.Timestamp);
            Assert.NotNull(response.Files);
            Assert.Empty(response.Files);
            // Verify timestamp format
            Assert.True(DateTime.TryParse(response.Timestamp, out _));
        }

        [Fact]
        public void CreateExtractResponse_ShouldReturnCorrectResponse()
        {
            // Arrange
            var message = "Test error message";

            // Act
            var response = ResponseFactory.CreateExtractResponse(message);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsError);
            Assert.Equal(ApiConstants.ERROR_STATUS, response.Status);
            Assert.Equal(message, response.Message);
            Assert.NotNull(response.Timestamp);
            // Verify timestamp format
            Assert.True(DateTime.TryParse(response.Timestamp, out _));
        }

        [Fact]
        public void CreateExtractionDataResponse_ShouldReturnCorrectResponse()
        {
            // Arrange
            var message = "Test error message";

            // Act
            var response = ResponseFactory.CreateExtractionDataResponse(message);

            // Assert
            Assert.NotNull(response);
            Assert.Equal(ApiConstants.ERROR_STATUS_LOWERCASE, response.Status);
            Assert.Equal(message, response.Message);
            Assert.False(response.IsSuccessStatusCode);
        }

        [Fact]
        public void CreateJobStatusResponse_WithEmptyMessage_ShouldHandleGracefully()
        {
            // Arrange
            var jobId = Guid.NewGuid();
            var message = string.Empty;

            // Act
            var response = ResponseFactory.CreateJobStatusResponse(jobId, message);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsError);
            Assert.Equal(ApiConstants.ERROR_STATUS, response.Status);
            Assert.Equal(string.Empty, response.Message);
            Assert.Equal(jobId, response.JobId);
            Assert.NotNull(response.Timestamp);
        }

        [Fact]
        public void CreateDocumentUploadResponse_WithNullMessage_ShouldHandleGracefully()
        {
            // Arrange
            string? message = null;

            // Act
            var response = ResponseFactory.CreateDocumentUploadResponse(message!);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.IsError);
            Assert.Equal(ApiConstants.ERROR_STATUS, response.Status);
            Assert.Null(response.Message);
            Assert.NotNull(response.Timestamp);
            Assert.NotNull(response.Files);
            Assert.Empty(response.Files);
        }
    }
}
