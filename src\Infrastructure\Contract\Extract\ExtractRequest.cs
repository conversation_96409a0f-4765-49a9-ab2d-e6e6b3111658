using System.Text.Json.Serialization;

namespace Infrastructure.Contract.Extract
{
    public class ExtractRequest
    {
        [JsonPropertyName("client_id")]
        public string ClientId { get; set; } = string.Empty;

        [JsonPropertyName("session_id")]
        public string SessionId { get; set; } = string.Empty;

        [JsonPropertyName("job_type")]
        public string JobType { get; set; } = "spread";

        [JsonPropertyName("notes_extraction")]
        public string NotesExtraction { get; set; } = "false";

        [JsonPropertyName("is_llm_agent")]
        public string IsLlmAgent { get; set; } = "false";

        [JsonPropertyName("job_engine")]
        public string JobEngine { get; set; } = "acuity";

        [JsonPropertyName("company_id")]
        public string CompanyId { get; set; } = string.Empty;

        [JsonPropertyName("company_name")]
        public string CompanyName { get; set; } = string.Empty;

        [JsonPropertyName("template_id")]
        public string? TemplateId { get; set; } = string.Empty;

        [JsonPropertyName("template")]
        public string Template { get; set; } = string.Empty;

        [JsonPropertyName("as_reported")]
        public bool AsReported { get; set; } = true;

        [JsonPropertyName("ticker")]
        public string? Ticker { get; set; } = string.Empty;

        [JsonPropertyName("kpi_json")]
        public string KpiJson { get; set; } = string.Empty;

        [JsonPropertyName("files")]
        public List<DocumentFile> Files { get; set; } = new();
    }
}