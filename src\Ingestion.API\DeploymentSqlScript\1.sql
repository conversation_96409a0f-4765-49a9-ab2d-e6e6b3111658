GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Status')
BEGIN
CREATE TABLE [dbo].[Status] (
    [Id] UNIQUEIDENTIFIER NOT NULL PRIMARY KEY DEFAULT NEWID(),
    [Name] NVARCHAR(MAX) NOT NULL,
	[State] NVARCHAR(MAX) NOT NULL,
    [CreatedOn] DATETIME NOT NULL DEFAULT GETUTCDATE(),
    [CreatedBy] INT NOT NULL,
    [ModifiedOn] DATETIME NULL,
    [ModifiedBy] INT NULL,
    [IsDeleted] BIT NOT NULL DEFAULT 0
)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'File Draft before Extraction' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], [State], [Name], [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'File Draft before Extraction', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction in progress' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], [State], [Name],  [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction in progress', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Completed' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction Completed', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Draft' AND Name = 'Active Draft')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction Draft', 'Active Draft', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Data Ingestion in progress' AND Name = 'In Progress')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Data Ingestion in progress', 'In Progress', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Data Ingestion Completed' AND Name = 'Completed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Data Ingestion Completed', 'Completed', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Data Ingestion Failed' AND Name = 'Failed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Data Ingestion Failed', 'Failed', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Failed' AND Name = 'Failed')
BEGIN
    INSERT INTO [dbo].[Status] ([Id], State, Name, [CreatedOn], [CreatedBy], [IsDeleted])
    VALUES (NEWID(), 'Extraction Failed', 'Failed', GETUTCDATE(), 1, 0)
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Jobs')
BEGIN
    CREATE TABLE Jobs (
        Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        JobId UNIQUEIDENTIFIER NOT NULL,
        ParentJobId UNIQUEIDENTIFIER NOT NULL,
        ProcessId UNIQUEIDENTIFIER NOT NULL,
        StatusId UNIQUEIDENTIFIER NOT NULL,
        TenantId UNIQUEIDENTIFIER NOT NULL,
        CreatedOn DATETIME NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy INT NOT NULL,
        ModifiedOn DATETIME NULL,
        ModifiedBy INT NULL,
        IsDeleted BIT NOT NULL DEFAULT 0
    )
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DataIngestionDocuments')
BEGIN
    CREATE TABLE DataIngestionDocuments (
        Id UNIQUEIDENTIFIER PRIMARY KEY NOT NULL,
        ProcessId UNIQUEIDENTIFIER NOT NULL,
		TenantId UNIQUEIDENTIFIER NULL,
        S3Path NVARCHAR(MAX) NOT NULL,
        FileName NVARCHAR(255) NOT NULL,
        Type NVARCHAR(100) NOT NULL,
        DiMappingId UNIQUEIDENTIFIER NOT NULL,
        CreatedOn DATETIME NOT NULL DEFAULT GETUTCDATE(),
        CreatedBy INT NOT NULL,
        ModifiedOn DATETIME NULL,
        ModifiedBy INT NULL,
        IsDeleted BIT NOT NULL DEFAULT 0
    )
END
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'DIMappingDocumentsDetails')
BEGIN
CREATE TABLE DIMappingDocumentsDetails (
    DiMappingId UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(), 
    CompanyId INT NOT NULL,
    SourceTypeId INT NOT NULL, 
    FeatureId INT NOT NULL,
	Quarter Varchar(50) NULL,
	Month INT NULL,
	Year INT NOT NULL,
    CreatedOn DATETIME NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy INT NOT NULL,
    ModifiedOn DATETIME NULL,
    ModifiedBy INT NULL,
    IsDeleted BIT NOT NULL DEFAULT 0
);
END
GO
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'DIMappingDocumentsDetails') 
    AND NOT EXISTS (SELECT * FROM sys.columns WHERE name = 'PeriodType' AND object_id = OBJECT_ID('DIMappingDocumentsDetails'))
BEGIN
    ALTER TABLE DIMappingDocumentsDetails
    ADD PeriodType VARCHAR(100) NULL;
END
GO
IF EXISTS (SELECT 1 FROM [dbo].[Status] WHERE State = 'Extraction Completed' AND Name = 'In Progress')
BEGIN
	Update [Status] SET Name='Completed' WHERE State = 'Extraction Completed' AND Name = 'In Progress'
END
GO