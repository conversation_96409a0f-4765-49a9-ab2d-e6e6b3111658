using Persistence.MongoDb;
using Xunit;

namespace UnitTests.Infrastructure
{
    public class MongoDbSettingsTests
    {
        [Fact]
        public void CanSetAndGetProperties()
        {
            var settings = new MongoDbSettings
            {
                ConnectionString = "mongodb://localhost:27017",
                DatabaseName = "TestDb"
            };
            Assert.Equal("mongodb://localhost:27017", settings.ConnectionString);
            Assert.Equal("TestDb", settings.DatabaseName);
        }

        [Fact]
        public void MongoDbSettings_DefaultValues_ShouldBeNull()
        {
            // Act
            var settings = new MongoDbSettings();

            // Assert
            Assert.Null(settings.ConnectionString);
            Assert.Null(settings.DatabaseName);
        }

        [Fact]
        public void MongoDbSettings_WithEmptyValues_ShouldAcceptEmptyStrings()
        {
            // Act
            var settings = new MongoDbSettings
            {
                ConnectionString = "",
                DatabaseName = ""
            };

            // Assert
            Assert.Equal("", settings.ConnectionString);
            Assert.Equal("", settings.DatabaseName);
        }

        [Fact]
        public void MongoDbSettings_WithComplexConnectionString_ShouldHandleCorrectly()
        {
            // Arrange
            var complexConnectionString = "mongodb://username:<EMAIL>:27017/database?retryWrites=true&w=majority";
            var databaseName = "production_db";

            // Act
            var settings = new MongoDbSettings
            {
                ConnectionString = complexConnectionString,
                DatabaseName = databaseName
            };

            // Assert
            Assert.Equal(complexConnectionString, settings.ConnectionString);
            Assert.Equal(databaseName, settings.DatabaseName);
        }
    }
} 