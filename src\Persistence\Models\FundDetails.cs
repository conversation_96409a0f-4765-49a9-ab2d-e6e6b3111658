﻿namespace Persistence.Models
{
    public class FundDetails:BaseEntity
    {
        [Key]
        public int FundId { get; set; }
        public string FundName { get; set; }
        public int? StrategyId { get; set; }
        public string? StrategyDescription { get; set; }
        public int? SectorId { get; set; }
        public int? RegionId { get; set; }
        public int? CountryId { get; set; }
        public int? StateId { get; set; }
        public int? CityId { get; set; }
        public string? VintageYear { get; set; }
        public int? AccountTypeId { get; set; }
        public int? CurrencyId { get; set; }
        public decimal? TargetCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public decimal? FundSize { get; set; }
        public decimal? GPCommitment { get; set; }
        public decimal? PreferredReturnPercent { get; set; }
        public decimal? CarriedInterestPercent { get; set; }
        public decimal? GPCatchupPercent { get; set; }
        public decimal? ManagementFee { get; set; }
        public decimal? ManagementFeeOffset { get; set; }
        public string? FundTerm { get; set; }
        public string? MaximumExtensionToFundTerm { get; set; }
        public DateTime? FundClosingDate { get; set; }
        public decimal? OrgExpenses { get; set; }
        public string? Clawback { get; set; }
        public string? VintageYear_Comment { get; set; }
        public string? TargetCommitment_Comment { get; set; }
        public string? MaximumCommitment_Comment { get; set; }
        public string? FundSize_Comment { get; set; }
        public string? GPCommitment_Comment { get; set; }
        public string? PreferredReturnPercent_Comment { get; set; }
        public string? CarriedInterestPercent_Comment { get; set; }
        public string? GPCatchupPercent_Comment { get; set; }
        public string? ManagementFee_Comment { get; set; }
        public string? ManagementFeeOffset_Comment { get; set; }
        public string? FundTerm_Comment { get; set; }
        public string? MaximumExtensionToFundTerm_Comment { get; set; }
        public string? FundClosingDate_Comment { get; set; }
        public string? OrgExpenses_Comment { get; set; }
        public string? Clawback_Comment { get; set; }
        public bool? IsActive { get; set; }
        public string EncryptedFundId { get; set; }
    }
}
