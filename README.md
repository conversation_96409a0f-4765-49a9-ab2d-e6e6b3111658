# Beat Foliosure API Ingestion

This project provides a document ingestion API for the Beat Foliosure platform. It handles file uploads to AWS S3, document extraction and processing via the Alexandria API platform, and maintains job status tracking for asynchronous processing.

## Project Overview

Beat Foliosure API Ingestion is a .NET 8.0 application designed to:

- Upload documents to AWS S3 storage
- Process financial documents through an AI extraction service
- Track processing jobs and their statuses
- Provide document download capabilities
- Map extracted financial data to standardized formats

## Architecture

The solution follows a clean architecture approach with the following projects:

- **API**: ASP.NET Core Web API project that exposes endpoints for document operations
- **AWSS3**: Library for AWS S3 interactions (upload, download, delete)
- **DataIngestionService**: Core business logic for document processing
- **Infrastructure**: DTOs, contracts, and shared infrastructure components
- **Persistence**: Data access layer with Entity Framework Core and repositories

## Key Features

- Document upload with S3 storage
- Financial document data extraction via Alexandria API
- Job status tracking with background processing
- File download from S3 with content conversion
- Rate limiting for API protection
- Health check endpoints
- Structured logging with Serilog
- Elastic APM integration for monitoring

## Setup and Configuration

### Prerequisites

- .NET 8.0 SDK
- SQL Server
- AWS Account with S3 access

### Database Setup

The project uses Entity Framework Core with SQL Server. Run the deployment script located at:
`src/API/DeploymentSqlScript/1.sql`

This will create the required database tables:
- Status
- Jobs
- DataIngestionDocuments
- DIMappingDocumentsDetails

### Configuration Files

- **appsettings.json**: Main application settings
- **S3.json**: AWS S3 bucket configuration
- **ratelimit.json**: API rate limiting settings

### AWS Configuration

Ensure appropriate AWS credentials are configured and the S3 bucket exists as specified in S3.json.

## API Endpoints

### Document Operations

- **POST /api/upload**: Upload a document to S3
- **POST /api/DownloadFile**: Download a document from S3

### Extraction Operations

- **POST /api/extraction/documents/upload**: Upload documents to the extraction service
- **POST /api/extraction/documents/extract**: Extract data from documents
- **GET /api/extraction/jobs/{jobId}/status**: Check status of an extraction job
- **GET /api/extraction/jobs/{jobId}/data**: Get extracted data from a completed job

### Job Operations

- **POST /api/job**: Create a new job
- **GET /api/status**: Get all possible job statuses

## Scheduled Jobs

The application includes a Quartz.NET scheduled job that runs every 3 minutes to check and update the status of in-progress extraction jobs.

## Health Checks

Health check endpoints are available at:
- `/healthcheck`: Basic health check status
- `/healthchecks-ui`: Health check UI dashboard

## Security

- CORS is configured with a whitelist of allowed origins
- Rate limiting is implemented to prevent abuse

## Environment-Specific Configuration

The application supports different environments through environment-specific appsettings files:
- appsettings.json (base configuration)
- appsettings.Development.json (development environment)

## Error Handling

The application includes comprehensive logging with Serilog and exception handling middleware for robust error management.

## Data Flow

1. Client uploads document via `/api/upload`
2. File is stored in AWS S3
3. Document metadata is stored in database
4. Extraction process is initiated via Alexandria API
5. Job status is tracked and updated periodically
6. Extracted data is transformed into standardized format
7. Client can retrieve extraction results when complete

## Dependencies

Major dependencies include:
- Microsoft.EntityFrameworkCore (8.0.8)
- AWSSDK packages for S3 and SecretsManager
- AspNetCoreRateLimit
- Serilog
- Elastic APM
- Quartz.NET
- Health Checks UI
- RestSharp

## Project Structure

The solution follows domain-driven design principles with clear separation of concerns:

- **Controllers**: API endpoints and request handling
- **Services**: Business logic and external integrations
- **Repositories**: Data access patterns
- **Models**: Domain entities and data structures
- **DTOs**: Data transfer objects for API communication

## Contributors

Beat Foliosure Team