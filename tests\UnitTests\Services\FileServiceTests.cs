using AWSS3.Interfaces;
using DataIngestionService.Constants;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract;
using Infrastructure.Contract.BlobStorage;
using Microsoft.Extensions.Logging;
using Moq;
using Persistence.GenericRepository;
using Persistence.Models;
using Persistence.UnitOfWork;
using Xunit;

namespace UnitTests.Services
{
    public class FileServiceTests
    {
        private readonly Mock<IAWSS3Library> _mockS3Library;
        private readonly Mock<ILogger<FileService>> _mockLogger;
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IExtract> _mockExtract;
        private readonly Mock<IGenericRepository<DataIngestionDocuments>> _mockDocRepo;
        private readonly Mock<IGenericRepository<Jobs>> _mockJobsRepo;
        private readonly Mock<IGenericRepository<DIMappingDocumentsDetails>> _mockMappingRepo;
        private readonly FileService _service;

        public FileServiceTests()
        {
            _mockS3Library = new Mock<IAWSS3Library>();
            _mockLogger = new Mock<ILogger<FileService>>();
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockExtract = new Mock<IExtract>();
            _mockDocRepo = new Mock<IGenericRepository<DataIngestionDocuments>>();
            _mockJobsRepo = new Mock<IGenericRepository<Jobs>>();
            _mockMappingRepo = new Mock<IGenericRepository<DIMappingDocumentsDetails>>();

            _mockUnitOfWork.SetupGet(u => u.DataIngestionDocumentRepository).Returns(_mockDocRepo.Object);
            _mockUnitOfWork.SetupGet(u => u.JobsRepository).Returns(_mockJobsRepo.Object);
            _mockUnitOfWork.SetupGet(u => u.DIMappingDocumentsDetailsRepository).Returns(_mockMappingRepo.Object);

            _service = new FileService(_mockS3Library.Object, _mockLogger.Object, _mockUnitOfWork.Object, _mockExtract.Object);
        }

        [Fact]
        public async Task GetDocumentFromBlobStorage_Returns_Response()
        {
            var guid = Guid.NewGuid();
            var key = $"folder/{guid}.pdf";
            var fileName = guid.ToString();
            var fileContent = "file-content";
            var doc = new DataIngestionDocuments { Id = guid, FileName = "Test.pdf", S3Path = key };
            _mockS3Library.Setup(s => s.GetFileAsync(key)).ReturnsAsync(fileContent);
            _mockDocRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(doc);

            var result = await _service.GetDocumentFromBlobStorage(new BlobStorageRequest { Key = key });

            Assert.NotNull(result);
            Assert.Equal(fileContent, result.File);
            Assert.Equal(doc.FileName, result.FileName);
            Assert.Equal(doc.S3Path, result.FileKey);
        }

        [Fact]
        public async Task GetDocumentFromBlobStorage_Throws_When_Key_Empty()
        {
            await Assert.ThrowsAsync<ArgumentException>(() => _service.GetDocumentFromBlobStorage(new BlobStorageRequest { Key = null }));
        }

        [Fact]
        public async Task GetDocumentFromBlobStorage_Logs_And_Throws_On_Exception()
        {
            var key = "folder/1234.pdf";
            _mockS3Library.Setup(s => s.GetFileAsync(key)).ThrowsAsync(new InvalidOperationException("S3 error"));
            await Assert.ThrowsAsync<InvalidOperationException>(() => _service.GetDocumentFromBlobStorage(new BlobStorageRequest { Key = key }));
        }

        [Fact]
        public async Task DeleteFile_Returns_False_If_Document_Not_Found()
        {
            _mockDocRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync((DataIngestionDocuments)null);
            var result = await _service.DeleteFile(new DeleteDocument { Id = Guid.NewGuid(), Key = "key" }, 1);
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteFile_Deletes_Document_And_Job_And_S3_And_Classifier()
        {
            var docId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var doc = new DataIngestionDocuments { Id = docId, ProcessId = processId, S3Path = "s3path", FileName = "file.pdf" };
            var mapping = new DIMappingDocumentsDetails { ProcessId = processId, ExtractionType = ApiConstants.AsIsExtraction };
            var job = new Jobs { ProcessId = processId };
            _mockDocRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(doc);
            _mockMappingRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(mapping);
            _mockJobsRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>())).ReturnsAsync(job);
            _mockExtract.Setup(e => e.DeleteClassifierDataByProcessIdAsync(processId, 1)).ReturnsAsync(true);
            _mockS3Library.Setup(s => s.DeleteFileAsync(It.IsAny<string>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            var result = await _service.DeleteFile(new DeleteDocument { Id = docId, Key = "key" }, 1);
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteFile_Deletes_Document_And_S3_And_Classifier_Without_Job()
        {
            var docId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var doc = new DataIngestionDocuments { Id = docId, ProcessId = processId, S3Path = "s3path", FileName = "file.pdf" };
            var mapping = new DIMappingDocumentsDetails { ProcessId = processId, ExtractionType = "Other" };
            _mockDocRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(doc);
            _mockMappingRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>())).ReturnsAsync(mapping);
            _mockExtract.Setup(e => e.DeleteClassifierDataByProcessIdAsync(processId, 1)).ReturnsAsync(true);
            _mockS3Library.Setup(s => s.DeleteFileAsync(It.IsAny<string>())).Returns(Task.CompletedTask);
            _mockUnitOfWork.Setup(u => u.SaveAsync()).ReturnsAsync(1);

            var result = await _service.DeleteFile(new DeleteDocument { Id = docId, Key = "key" }, 1);
            Assert.True(result);
        }

        [Fact]
        public async Task GetDocumentsByProcessId_Returns_Response()
        {
            var processId = Guid.NewGuid();
            var doc = new DataIngestionDocuments { Id = Guid.NewGuid(), ProcessId = processId, S3Path = "file.pdf", FileName = "file.pdf" };
            var s3Setting = new AWSS3.S3Setting { BucketName = "bucket", Region = "region", FolderPrefix = "folder", KeyPrefix = "prefix" };
            _mockS3Library.Setup(s => s.GetBucketDetails()).Returns(s3Setting);
            _mockDocRepo.Setup(r => r.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DataIngestionDocuments, bool>>>())).ReturnsAsync(doc);
            _mockS3Library.Setup(s => s.GetFileAsync(It.IsAny<string>())).ReturnsAsync("file-content");

            var result = await _service.GetDocumentsByProcessId(processId);
            Assert.NotNull(result);
            Assert.Equal("file-content", result.File);
        }

        [Fact]
        public async Task GetDocumentsByProcessId_Logs_And_Throws_On_Exception()
        {
            var processId = Guid.NewGuid();
            _mockS3Library.Setup(s => s.GetBucketDetails()).Throws(new Exception("fail"));
            await Assert.ThrowsAsync<Exception>(() => _service.GetDocumentsByProcessId(processId));
        }
    }
} 