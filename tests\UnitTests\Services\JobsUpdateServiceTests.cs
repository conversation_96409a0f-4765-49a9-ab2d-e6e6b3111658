using DataIngestionService.Constants;
using DataIngestionService.IServices;
using DataIngestionService.Services;
using Infrastructure.Contract;
using Infrastructure.Contract.Extract;
using Microsoft.Extensions.Logging;
using Moq;
using Notification.Models;
using Notification.Services;
using Persistence.Models;
using Persistence.UnitOfWork;
using Xunit;

namespace UnitTests.Services
{
    public class JobsUpdateServiceTests
    {
        private readonly Mock<ILogger<JobsUpdateService>> _loggerMock = new();
        private readonly Mock<IUnitOfWork> _unitOfWorkMock = new();
        private readonly Mock<IExtract> _extractMock = new();
        private readonly Mock<ICacheService> _cacheServiceMock = new();
        private readonly Mock<NotificationSender> _notificationSenderMock;

        public JobsUpdateServiceTests()
        {
            // NotificationSender requires IHubContext, so we mock its constructor with null (not used in our tests)
            _notificationSenderMock = new Mock<NotificationSender>(null as Microsoft.AspNetCore.SignalR.IHubContext<Notification.Hubs.NotificationHub>);
        }

        private JobsUpdateService CreateService() => new(
            _loggerMock.Object,
            _unitOfWorkMock.Object,
            _extractMock.Object,
            _cacheServiceMock.Object,
            _notificationSenderMock.Object
        );

        [Fact]
        public async Task GetStatus_ReturnsStatusesFromCache()
        {
            // Arrange
            var statuses = new List<Status> { new() { Id = Guid.NewGuid(), Name = "Extraction Completed", State = ApiConstants.ExtractionCompleted } };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ReturnsAsync(statuses);
            var service = CreateService();

            // Act
            var result = await service.GetStatus();

            // Assert
            Assert.Equal(statuses, result);
        }

        [Fact]
        public async Task UpdateJobsStatus_NoInProgressJobs_LogsAndReturnsTrue()
        {
            // Arrange
            var statusId = Guid.NewGuid();
            var statuses = new List<Status> {
                new() { Id = statusId, Name = "Extraction in progress", State = ApiConstants.ExtractionInProgress },
                new() { Id = Guid.NewGuid(), Name = "Extraction Completed", State = ApiConstants.ExtractionCompleted },
                new() { Id = Guid.NewGuid(), Name = "Extraction Failed", State = ApiConstants.ExtractionFailed }
            };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ReturnsAsync(statuses);
            _unitOfWorkMock.Setup(x => x.StatusRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Status, bool>>>()))
                .ReturnsAsync(statuses);
            _unitOfWorkMock.Setup(x => x.JobsRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(new List<Jobs>());
            var service = CreateService();

            // Act
            var result = await service.UpdateJobsStatus("token");

            // Assert
            Assert.True(result);
            _loggerMock.Verify(x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("No in-progress jobs found to update")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception, string>>()), Times.Once);
        }

        [Fact(Skip ="fix later")]
        public async Task UpdateJobsStatus_UpdatesJobsAndSendsNotification()
        {
            // Arrange
            var inProgressStatusId = Guid.NewGuid();
            var completedStatusId = Guid.NewGuid();
            var failedStatusId = Guid.NewGuid();
            var statuses = new List<Status> {
                new() { Id = inProgressStatusId, Name = "Extraction in progress", State = ApiConstants.ExtractionInProgress },
                new() { Id = completedStatusId, Name = "Extraction Completed", State = ApiConstants.ExtractionCompleted },
                new() { Id = failedStatusId, Name = "Extraction Failed", State = ApiConstants.ExtractionFailed }
            };
            var jobs = new List<Jobs> {
                new() { JobId = Guid.NewGuid(), ProcessId = Guid.NewGuid(), StatusId = inProgressStatusId }
            };
            var extractionTypes = new List<DIMappingDocumentsDetails> {
                new() { ProcessId = jobs[0].ProcessId, ExtractionType = ApiConstants.SpecificKPI }
            };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ReturnsAsync(statuses);
            _unitOfWorkMock.Setup(x => x.StatusRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Status, bool>>>()))
                .ReturnsAsync(statuses);
            _unitOfWorkMock.Setup(x => x.JobsRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(jobs);
            _unitOfWorkMock.Setup(x => x.DIMappingDocumentsDetailsRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>()))
                .ReturnsAsync(extractionTypes);
            _extractMock.Setup(x => x.CheckJobStatusAsync(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new JobStatusResponse { Status = ApiConstants.STATUS_IN_PROGRESS });
            _extractMock.Setup(x => x.FetchSpecificExtractionData(It.IsAny<Guid>(), It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new ExtractionDataResponse { IsSuccessStatusCode = true });
            _unitOfWorkMock.Setup(x => x.JobsRepository.UpdateBulk(It.IsAny<List<Jobs>>()));
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            _notificationSenderMock.Setup(x => x.SendToAllAsync(It.IsAny<NotificationMessage>())).Returns(Task.CompletedTask);
            var service = CreateService();

            // Act
            var result = await service.UpdateJobsStatus("token");

            // Assert
            Assert.True(result);
            _unitOfWorkMock.Verify(x => x.JobsRepository.UpdateBulk(It.IsAny<List<Jobs>>()), Times.Once);
            _unitOfWorkMock.Verify(x => x.SaveAsync(), Times.Once);
        }

        [Fact]
        public async Task UpdateJobsStatus_HandlesExceptionAndLogsError()
        {
            // Arrange
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ThrowsAsync(new Exception("Cache error"));
            var service = CreateService();

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => service.UpdateJobsStatus("token"));
            _loggerMock.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error updating jobs")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()), Times.Once);
        }

        [Fact]
        public async Task JobExists_ReturnsTrueIfExists()
        {
            // Arrange
            var processId = Guid.NewGuid();
            _unitOfWorkMock.Setup(x => x.JobsRepository.ExistsAsyncAny(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(true);
            var service = CreateService();

            // Act
            var result = await service.JobExists(processId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task UpdateJob_UpdatesJobAndReturnsStatusResponse()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var userId = 1;
            var job = new Jobs { Id = Guid.NewGuid(), ProcessId = processId, StatusId = statusId, JobId = jobId };
            var request = new CreateJobs { ProcessId = processId, StatusId = statusId, JobId = jobId };
            _unitOfWorkMock.Setup(x => x.JobsRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(job);
            _unitOfWorkMock.Setup(x => x.JobsRepository.Update(It.IsAny<Jobs>()));
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            var service = CreateService();

            // Act
            var result = await service.UpdateJob(request, userId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("Job updated successfully", result.Message);
        }

        [Fact]
        public async Task UpdateJob_ThrowsIfJobNotFound()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var userId = 1;
            var request = new CreateJobs { ProcessId = processId, StatusId = statusId, JobId = jobId };
            _unitOfWorkMock.Setup(x => x.JobsRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync((Jobs)null);
            var service = CreateService();

            // Act & Assert
            await Assert.ThrowsAsync<KeyNotFoundException>(() => service.UpdateJob(request, userId));
        }

        [Fact]
        public async Task CreateJob_CreatesJobIfNotExists()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var userId = 1;
            var request = new CreateJobs { ProcessId = processId, StatusId = statusId, JobId = jobId, TenantId = Guid.NewGuid(), ParentJobId = Guid.NewGuid() };
            _unitOfWorkMock.Setup(x => x.JobsRepository.ExistsAsyncAny(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(false);
            _unitOfWorkMock.Setup(x => x.JobsRepository.AddAsyn(It.IsAny<Jobs>())).ReturnsAsync(new Jobs());
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            var service = CreateService();

            // Act
            var result = await service.CreateJob(request, userId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("Job created successfully", result.Message);
        }

        [Fact]
        public async Task CreateJob_UpdatesJobIfExists()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var userId = 1;
            var request = new CreateJobs { ProcessId = processId, StatusId = statusId, JobId = jobId };
            _unitOfWorkMock.Setup(x => x.JobsRepository.ExistsAsyncAny(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(true);
            _unitOfWorkMock.Setup(x => x.JobsRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(new Jobs { Id = Guid.NewGuid(), ProcessId = processId, StatusId = statusId, JobId = jobId });
            _unitOfWorkMock.Setup(x => x.JobsRepository.Update(It.IsAny<Jobs>()));
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            var service = CreateService();

            // Act
            var result = await service.CreateJob(request, userId);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("Job updated successfully", result.Message);
        }

        [Fact]
        public async Task CreateJob_HandlesExceptionAndLogsError()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var jobId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var userId = 1;
            var request = new CreateJobs { ProcessId = processId, StatusId = statusId, JobId = jobId };
            _unitOfWorkMock.Setup(x => x.JobsRepository.ExistsAsyncAny(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ThrowsAsync(new Exception("DB error"));
            var service = CreateService();

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => service.CreateJob(request, userId));
            _loggerMock.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error creating job")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()), Times.Once);
        }

        [Fact]
        public async Task UpdateJobStatus_UpdatesStatusSuccessfully()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var statuses = new List<Status> {
                new() { Id = statusId, Name = "Data Ingestion Completed", State = "Data Ingestion Completed" }
            };
            var job = new Jobs { Id = Guid.NewGuid(), ProcessId = processId, StatusId = statusId, JobId = Guid.NewGuid() };
            var updateModel = new StatusUpdateModel { ProcessId = processId.ToString(), Status = "Data Ingestion Completed" };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ReturnsAsync(statuses);
            _unitOfWorkMock.Setup(x => x.StatusRepository.FindAllAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Status, bool>>>()))
                .ReturnsAsync(statuses);
            _unitOfWorkMock.Setup(x => x.JobsRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(job);
            _unitOfWorkMock.Setup(x => x.JobsRepository.Update(It.IsAny<Jobs>()));
            _unitOfWorkMock.Setup(x => x.SaveAsync()).ReturnsAsync(1);
            var service = CreateService();

            // Act
            var result = await service.UpdateJobStatus(updateModel);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal("Status updated successfully", result.Message);
        }

        [Fact]
        public async Task UpdateJobStatus_ThrowsIfJobNotFound()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var updateModel = new StatusUpdateModel { ProcessId = processId.ToString(), Status = "Data Ingestion Completed" };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ReturnsAsync(new List<Status>());
            _unitOfWorkMock.Setup(x => x.JobsRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync((Jobs)null);
            var service = CreateService();

            // Act & Assert
            await Assert.ThrowsAsync<KeyNotFoundException>(() => service.UpdateJobStatus(updateModel));
        }

        [Fact]
        public async Task UpdateJobStatus_ThrowsIfNoMatchingStatus()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var job = new Jobs { Id = Guid.NewGuid(), ProcessId = processId, StatusId = Guid.NewGuid(), JobId = Guid.NewGuid() };
            var updateModel = new StatusUpdateModel { ProcessId = processId.ToString(), Status = "Nonexistent Status" };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ReturnsAsync(new List<Status>());
            _unitOfWorkMock.Setup(x => x.JobsRepository.GetFirstOrDefaultAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Jobs, bool>>>()))
                .ReturnsAsync(job);
            var service = CreateService();

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => service.UpdateJobStatus(updateModel));
        }

        [Fact]
        public async Task UpdateJobStatus_HandlesExceptionAndLogsError()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var updateModel = new StatusUpdateModel { ProcessId = processId.ToString(), Status = "Data Ingestion Completed" };
            _cacheServiceMock.Setup(x => x.GetOrSetAsync(
                It.IsAny<string>(),
                It.IsAny<Func<Task<List<Status>>>>(),
                It.IsAny<TimeSpan>()))
                .ThrowsAsync(new Exception("Cache error"));
            var service = CreateService();

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => service.UpdateJobStatus(updateModel));
            _loggerMock.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error updating job status for ProcessId")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()), Times.Once);
        }

        [Fact(Skip = "fix later")]
        public async Task SendNotification_SendsNotificationToAll()
        {
            // Arrange
            var jobs = new List<Jobs> { new() { JobId = Guid.NewGuid(), StatusId = Guid.NewGuid() } };
            var notificationSender = new NotificationSender(null); // Use real instance, as we cannot mock non-virtual methods
            var service = new JobsUpdateService(
                _loggerMock.Object,
                _unitOfWorkMock.Object,
                _extractMock.Object,
                _cacheServiceMock.Object,
                notificationSender
            );

            // Act & Assert
            await service.SendNotification(jobs);
            // If no exception is thrown, the test passes
        }

        [Fact]
        public async Task GetExtractionTypes_ReturnsCorrectMappings()
        {
            // Arrange
            var processId1 = Guid.NewGuid();
            var processId2 = Guid.NewGuid();
            var processIds = new List<Guid> { processId1, processId2 };
            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = processId1, ExtractionType = "TypeA", IsDeleted = false },
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = processId2, ExtractionType = "TypeB", IsDeleted = false },
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = Guid.NewGuid(), ExtractionType = "TypeC", IsDeleted = false }, // not in processIds
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = processId1, ExtractionType = "TypeD", IsDeleted = true } // deleted
            };
            _unitOfWorkMock.Setup(x => x.DIMappingDocumentsDetailsRepository.FindAllAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>()))
                .ReturnsAsync((System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>> predicate) =>
                    mappings.Where(predicate.Compile()).ToList());
            var service = CreateService();

            // Act
            var result = await service.GetExtractionTypes(processIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Only two non-deleted with matching processIds
            Assert.Contains(result, x => x.ProcessId == processId1 && x.ExtractionType == "TypeA");
            Assert.Contains(result, x => x.ProcessId == processId2 && x.ExtractionType == "TypeB");
            Assert.DoesNotContain(result, x => x.IsDeleted);
            Assert.DoesNotContain(result, x => x.ProcessId != processId1 && x.ProcessId != processId2);
        }
        [Fact]
        public async Task GetExtractionTypesReturnsCorrectMappings()
        {
            // Arrange
            var processId1 = Guid.NewGuid();
            var processId2 = Guid.NewGuid();
            var processIds = new List<Guid> { processId1, processId2 };
            var mappings = new List<DIMappingDocumentsDetails>
            {
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = processId1, ExtractionType = "TypeA", IsDeleted = false },
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = processId2, ExtractionType = "TypeB", IsDeleted = false },
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = Guid.NewGuid(), ExtractionType = "TypeC", IsDeleted = false }, // not in processIds
                new DIMappingDocumentsDetails { DiMappingId = Guid.NewGuid(), ProcessId = processId1, ExtractionType = "TypeD", IsDeleted = true } // deleted
            };
            _unitOfWorkMock.Setup(x => x.DIMappingDocumentsDetailsRepository.FindAllAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>>>()))
                .ReturnsAsync((System.Linq.Expressions.Expression<Func<DIMappingDocumentsDetails, bool>> predicate) =>
                    mappings.Where(predicate.Compile()).ToList());
            var service = CreateService();

            // Act
            var result = await service.GetExtractionTypes(processIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Only two non-deleted with matching processIds
            Assert.Contains(result, x => x.ProcessId == processId1 && x.ExtractionType == "TypeA");
            Assert.Contains(result, x => x.ProcessId == processId2 && x.ExtractionType == "TypeB");
            Assert.DoesNotContain(result, x => x.IsDeleted);
            Assert.DoesNotContain(result, x => x.ProcessId != processId1 && x.ProcessId != processId2);

        }
    }
}