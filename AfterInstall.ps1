Clear-Host
Import-Module WebAdministration


if ($PSHOME -like "*SysWOW64*") {
    Write-Warning "Restarting this script under 64-bit Windows PowerShell."

    # Restart this script under 64-bit Windows PowerShell.
    #   (\SysNative\ redirects to \System32\ for 64-bit mode)

    Write-Warning $PSScriptRoot
    Write-Warning $MyInvocation.MyCommand
    Write-Warning $MyInvocation.InvocationName
    & (Join-Path ($PSHOME -replace "SysWOW64", "SysNative") powershell.exe) -File $MyInvocation.InvocationName @args

    # Exit 32-bit script.
    Exit $LastExitCode
    Write-Warning " 64-bit Windows PowerShell done."
}

if(!$PSScriptRoot){
    $PSScriptRoot = Split-Path $MyInvocation.MyCommand.Path -Parent
}

$DEPLOY_FILE= Join-Path $PSScriptRoot "build/deploy.ps1"
. $DEPLOY_FILE
check

#AppVariables Starts
# Best is to read this from CodePipeline / CodeBuild / CodeDeploy environment variable
#$stageOrEnvironment = "feature" # Options are features, dev, test, uat, prod, mvp etc.
$clientOrPod = "pod"             # Options are pod, demo, <client-code>
$applicationName = "ingestion"
$mainApplication = "foliosure"
$deploymentSourceFolderName = "FoliosureAPIIngestionTemp"
$AWS_REGION = "eu-west-1"

# Constant - Don't change
$DEFAULT_WEB_SITE = "default web site"
$APP_POOL_DOT_NET_VERSION = "v4.0"

# TODO: Change this path - Application specific - Begin
$WEBSITE_ROOT_PATH = "E:\inetpub\wwwroot\foliosure\"
# $env:DEPLOYMENT_GROUP_NAME="BEAT-FOLIOSURE-DEMO"

switch ($env:DEPLOYMENT_GROUP_NAME) {
    "NON-PROD-DEV" {
		$stageOrEnvironment = "feature"
		$sqlDBName = "foliosure_feature"
        $secretKey = "nprd/foliosure/pod/dev"
        $deploymenturl="http://localhost/foliosure/feature/pod/ingestion/health"
        $healthcheckurl = "https://dev.beatapps.net/foliosure/feature/pod/ingestion/healthcheck"
    }
    "NON-PROD-TEST" {
        $stageOrEnvironment = "test"
		$sqlDBName = "foliosure_test"
        $secretKey = "nprd/foliosure/pod/test"
        $deploymenturl="http://localhost/foliosure/test/pod/ingestion/health"
        $healthcheckurl = "https://test.beatapps.net/foliosure/test/pod/ingestion/healthcheck"
    }
    "NON-PROD-TEST-POD-B" {
        $stageOrEnvironment = "test"
        $clientOrPod = "pod-b"
		$sqlDBName = "foliosure_test"
        $secretKey = "nprd/foliosure/pod/test"
        $deploymenturl="http://localhost/foliosure/test/pod/services/health"
        $healthcheckurl = "https://test.beatapps.net/foliosure/test/pod-b/services/healthcheck"
    }
     "NON-PROD-PERF1" {
        $stageOrEnvironment = "perf1"
		$sqlDBName = "Beat-foliosure-perf1"
        $secretKey = "nprd/foliosure/pod/perf1"
        $deploymenturl="http://localhost/foliosure/pef1/pod/services/health"
        $healthcheckurl = "http://localhost/foliosure/test/pod-b/services/healthcheck"
    }
     "NON-PROD-PERF2" {
        $stageOrEnvironment = "perf2"
		$sqlDBName = "Beat-foliosure-perf2"
        $secretKey = "nprd/foliosure/pod/perf1"
        $deploymenturl="http://localhost/foliosure/pef2/pod/services/health"
        $healthcheckurl = "http://localhost/foliosure/test/pod-b/services/healthcheck"
    }
    "NON-PROD-UAT" {
		$stageOrEnvironment = "uat"
        $clientOrPod = "pod-a"
		$sqlDBName = "foliosure_uat"
        $secretKey = "nprd/foliosure/pod/uat"
        $deploymenturl="http://localhost/foliosure/uat/pod-a/ingestion/health"
        $healthcheckurl = "https://uat.beatapps.net/foliosure/uat/pod-a/ingestion/healthcheck"
    }
    "NON-PROD-UAT-POD-B" {
		$stageOrEnvironment = "uat"
        $clientOrPod = "pod-b"
		$sqlDBName = "foliosure_uat"
        $secretKey = "nprd/foliosure/pod/uat"
        $deploymenturl="http://localhost/foliosure/uat/pod/services/health"
        $healthcheckurl = "https://uat.beatapps.net/foliosure/uat/pod-b/services/healthcheck"
    }
    "uat-taabo-foliosure" {
        $stageOrEnvironment = "uat"
        $clientOrPod = "security"
        $sqlDBName = "foliosure_uat_taabo"
        $secretKey = "nprd/foliosure/pod/uat/taaboDbConnection"
        $deploymenturl="http://localhost/foliosure/uat/security/services/health"
        $healthcheckurl = "https://test.beatapps.net/foliosure/uat/security/services/healthcheck"
    }
    "beat-foliosure-security-pod" {
        $stageOrEnvironment = "security"
        $clientOrPod = "pod"
        $sqlDBName = "foliosure_security"
        $secretKey = "nprd/foliosure/pod/security"
        $deploymenturl="http://localhost/foliosure/security/pod/services/health"
        $healthcheckurl = "https://security.beatapps.net/foliosure/security/pod/services/healthcheck"
    }
    "beat-foliosure-demo" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "demo"
		$sqlDBName = "Foliosure-demo"
        $secretKey = "prd/foliosure/pod/prod"
        $deploymenturl="http://localhost:8005/foliosure/prod/demo/ingestion/health"
        $healthcheckurl = "https://demo.beatfoliosure.com/ingestion/healthcheck"
    }
    "beat-foliosure-prod-demo2" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "demo2"
		$sqlDBName = "beat-foliosure-demo2"
        $secretKey = "prd/foliosure/pod/demo2/DbConnection"
        $deploymenturl="http://localhost:8003/foliosure/prod/demo2/ingestion/health"
        $healthcheckurl = "https://demo2.beatfoliosure.com/ingestion/healthcheck"
    }
    "beat-foliosure-enshi" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "enshi"
		$sqlDBName = "Beat-foliosure-enshi"
        $secretKey = "prd/foliosure/pod/enshi"
        $deploymenturl="http://localhost/foliosure/feature/prod/enshi/services/health"
        $healthcheckurl = "http://localhost/foliosure/feature/prod/enshi/services/healthcheck"
    }
    "beat-foliosure-prod-taabo-ch" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "taabo-ch"
        $sqlDBName = "beat-foliosure-taabo"
        $secretKey = "prd/foliosure/pod/taabo-ch/DbConnection"
        $deploymenturl="http://localhost/foliosure/feature/prod/taabo-ch/services/health"
        $healthcheckurl = "http://localhost/foliosure/feature/prod/taabo-ch/services/healthcheck"
    }
	"beat-foliosure-prod-bristol" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "bristol"
    	$sqlDBName = "beat-foliosure-bristol"
        $secretKey = "prd/foliosure/pod/bristol/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/bristol/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/bristol/services/healthcheck"
    }
     "beat-foliosure-prod-admont" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "admont"
    	$sqlDBName = "beat-foliosure-admont"
        $secretKey = "prd/foliosure/pod/admont/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/admont/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/admont/services/healthcheck"
    }
    "beat-foliosure-prod-asmt" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "asmt"
    	$sqlDBName = "beat-foliosure-asmt"
        $secretKey = "prd/foliosure/pod/asmt/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/asmt/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/asmt/services/healthcheck"
    }
	"beat-foliosure-prod-monmouth" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "monmouth"
    	$sqlDBName = "beat-foliosure-monmouth"
        $secretKey = "prd/foliosure/pod/monmouth/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/monmouth/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/monmouth/services/healthcheck"
    }
	"beat-foliosure-prod-exeter" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "exeter"
    	$sqlDBName = "beat-foliosure-exeter"
        $secretKey = "prd/foliosure/pod/exeter/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/exeter/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/exeter/services/healthcheck"
    }
	"beat-foliosure-prod-pizarro" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "pizarro"
    	$sqlDBName = "beat-foliosure-pizarro"
        $secretKey = "prd/foliosure/pod/pizarro/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/pizarro/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/pizarro/services/healthcheck"
    }
	"beat-foliosure-prod-trial" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "trial"
    	$sqlDBName = "beat-foliosure-trial"
        $secretKey = "prd/foliosure/pod/trial/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/trial/ingestion/health"
        $healthcheckurl = "http://localhost/foliosure/prod/trial/ingestion/healthcheck"
    }
    "beat-foliosure-prod-himera" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "himera"
    	$sqlDBName = "beat-foliosure-himera"
        $secretKey = "prd/foliosure/pod/himera/DbConnection"
        $deploymenturl="http://localhost/foliosure/prod/himera/services/health"
        $healthcheckurl = "http://localhost/foliosure/prod/himera/services/healthcheck"
    }
    Default {
		$stageOrEnvironment = "feature"
		$sqlDBName = "foliosure_feature"
        $secretKey = "nprd/foliosure/pod/dev"
        $deploymenturl="http://localhost/foliosure/feature/pod/ingestion/health"
        $healthcheckurl = "https://dev.beatapps.net/foliosure/feature/pod/ingestion/healthcheck"
    }
}

$webSiteAppPoolName = "app_pool_foliosure_" + $stageOrEnvironment + "_" + $clientOrPod
$webSiteInstancePath = $WEBSITE_ROOT_PATH + $stageOrEnvironment + "\" + $clientOrPod

switch ($stageOrEnvironment) {
    "feature" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "dev" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "test" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
             "pod-b" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
     "perf1" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
     "perf2" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "uat" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
            "pod-a" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
            "pod-b" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
            "demo" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
			 "security" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "security" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "prod" {
        switch ($clientOrPod) {
            "demo" {
                $webSiteName = "demo.beatfoliosure.com"
                $webSiteBindingsPort = "8005"
            }
            "demo2" {
                $webSiteName = "demo2.beatfoliosure.com"
                $webSiteBindingsPort = "8003"
            }
            "enshi" {
                $webSiteName = "enshi.beatfoliosure.com"
                $webSiteBindingsPort = "9017"
            }
            "taabo-ch" {
                $webSiteName = "taabo-ch.beatfoliosure.com"
                $webSiteBindingsPort = "8100"
            }
			"bristol" {
                $webSiteName = "bristol.beatfoliosure.com"
                $webSiteBindingsPort = "8107"
            }
            "admont" {
                $webSiteName = "admont.beatfoliosure.com"
                $webSiteBindingsPort = "8104"
            }
            "asmt" {
                $webSiteName = "asmt.beatfoliosure.com"
                $webSiteBindingsPort = "8106"
            }
			"monmouth" {
                $webSiteName = "monmouth.beatfoliosure.com"
                $webSiteBindingsPort = "8102"
            }
			"exeter" {
                $webSiteName = "exeter.beatfoliosure.com"
                $webSiteBindingsPort = "8103"
            }
			"pizarro" {
                $webSiteName = "pizarro.beatfoliosure.com"
                $webSiteBindingsPort = "9203"
            }
			"trial" {
                $webSiteName = "trial.beatfoliosure.com"
                $webSiteBindingsPort = "8001"
            }
            "himera" {
                $webSiteName = "himera.beatfoliosure.com"
                $webSiteBindingsPort = "8105"
            }
        }
    }
    default {
        $webSiteName = "<Not set>"
        $webSiteBindingsPort = "-1"
    }
}

$PSPath = "IIS:\Sites\$webSiteName\$mainApplication\" + $stageOrEnvironment + "\" + $clientOrPod
$VirtualPath = "$webSiteName\$mainApplication\" + $stageOrEnvironment + "\" + $clientOrPod

if ($stageOrEnvironment -eq "prod" -and $webSiteName -ne $DEFAULT_WEB_SITE) {
    $PSPath = "IIS:\Sites\$webSiteName"
    $VirtualPath = "$webSiteName"
}


function TestDeployment($url){

        # First we create the request.
    $HTTP_Request = [System.Net.WebRequest]::Create($url)

    # We then get a response from the site.
    $HTTP_Response = $HTTP_Request.GetResponse()

    # We then get the HTTP code as an integer.
    $HTTP_Status = [int]$HTTP_Response.StatusCode

    If ($HTTP_Status -eq 200) {
        Write-Host "Site is OK!"
    }
    Else {
        Write-Host "The Site may be down, please check!"
    }

    # Finally, we clean up the http request by closing it.
    If ($HTTP_Response -eq $null) { } 
    Else { $HTTP_Response.Close() }
}

function AfterInstall() {

    Clear-Host

    Write-Host -ForegroundColor Magenta -Object "AfterInstall.ps1 - Script execution begins here"
    Write-Host -ForegroundColor Magenta -Object ""

    if ($webSiteBindingsPort -eq -1) {
        Write-Host -ForegroundColor Red -Object "Invalid deployment settings. Review AppVariables.ps1"
        Write-Host -ForegroundColor Magenta -Object ""
        Write-Host -ForegroundColor Magenta -Object "AfterInstall.ps1 - Script execution ends here"
        return
    }

	#DeleteFolderContent $webSiteInstancePath + "\" + $applicationName + "\DeploymentSqlScript\"
	
    DeployWebSiteToServer $webSiteInstancePath $webSiteAppPoolName $webSiteName $webSiteBindingsPort

    DeployApplicationToServer $applicationName

  

    # TODO: In production, this line should be uncommented as part of clean-up
    DeleteFolder ($WEBSITE_ROOT_PATH + "FoliosureAPIIngestionTemp")

    ReplaceAppSettings
	
	#Database CI/CD starts here
	#execute-dbScripts $sqlDBName $secretKey
	#Database CI/CD ends here

    Write-Host -ForegroundColor Magenta -Object "AfterInstall.ps1 - Script execution ends here"

    TestDeployment $healthcheckurl
}

AfterInstall