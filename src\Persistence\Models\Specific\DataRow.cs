namespace Persistence.Models.Specific
{
    public class DataRow
    {
        public string Label { get; set; } = string.Empty;
        public string LabelType { get; set; } = string.Empty;
        public string RowId { get; set; } = string.Empty;
        public string CompanyId { get; set; } = string.Empty;
        public string CompanyName { get; set; }= string.Empty;
        public string FundId { get; set; }=string.Empty;
        public bool Selected { get; set; } = false;
        public string CurrencyCode { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public Dictionary<string, KpiValue> Values { get; set; } = [];
    }
}
