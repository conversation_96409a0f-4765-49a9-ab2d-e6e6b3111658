﻿using Infrastructure.Enum;

/// <summary>
///
/// </summary>
namespace Infrastructure.DTOS.NonControllerDto
{
    public class A1TableColumn
    {
        public string ColumnKey { get; set; }
        public string? DsKey { get; set; } // Should match with CellValue.Key
        public string? Title { get; set; }
        public StatementType FilingType { get; set; }
        public ReportingPeriod ReportingPeriod { get; set; }
        public string MonthEnding { get; set; }
        public string PeriodDate { get; set; }
        public string PeriodInfo { get; set; }
    }
}