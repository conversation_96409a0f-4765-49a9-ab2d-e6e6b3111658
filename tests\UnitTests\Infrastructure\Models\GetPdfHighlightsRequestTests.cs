using Ingestion.API.Models;
using Xunit;

namespace UnitTests.Infrastructure.Models
{
    public class GetPdfHighlightsRequestTests
    {
        [Fact]
        public void IsStateValid_WithEmptyGuid_ReturnsFalse()
        {
            // Arrange
            var request = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.Empty,
                TableGroupLabel = "Revenue",
                PeriodDate = "2023-12"
            };

            // Act
            var result = request.IsStateValid();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsStateValid_WithNullOrWhitespaceTableGroupLabel_ReturnsFalse()
        {
            // Arrange & Act & Assert
            var requestWithNull = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.NewGuid(),
                TableGroupLabel = null!,
                PeriodDate = "2023-12"
            };
            Assert.False(requestWithNull.IsStateValid());

            var requestWithEmpty = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.NewGuid(),
                TableGroupLabel = "",
                PeriodDate = "2023-12"
            };
            Assert.False(requestWithEmpty.IsStateValid());

            var requestWithWhitespace = new GetPdfHighlightsRequest
            {
                ProcessId = Guid.NewGuid(),
                TableGroupLabel = "   ",
                PeriodDate = "2023-12"
            };
            Assert.False(requestWithWhitespace.IsStateValid());
        }
    }
}