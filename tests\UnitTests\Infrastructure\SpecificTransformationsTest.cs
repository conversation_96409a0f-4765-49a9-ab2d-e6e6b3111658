﻿using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Extentions;
using System.Diagnostics;
using System.Text.Json;
using Xunit;

namespace UnitTests.Infrastructure
{
    public class SpecificTransformationsTest
    {
        [Fact]
        public void ToSpecificDto_ShouldReturnNull_WhenInputIsNull()
        {
            // Arrange
            DsSpecificDto dsSpecificDto = null;

            // Act
            var result = dsSpecificDto.ToSpecificDto("fundId");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ToSpecificDto_ShouldReturnValidSpecificDto_WhenInputIsValid()
        {
            // Arrange
            var dsSpecificDto = new DsSpecificDto
            {
                KpiGroups = new List<DsSpecificKpiGroupDto>
                {
                    new DsSpecificKpiGroupDto
                    {
                        KpiType = "fund",
                        Label = "TestLabel",
                        LabelId = "TestLabelId",
                        CompanyId = "TestCompanyId",
                        CompanyName = "TestCompanyName",
                        Values = new List<DsSpecificValueDto>
                        {
                            new DsSpecificValueDto
                            {
                                Column = "2023",
                                Value = "100",
                                FileType = "pdf",
                                PageNumber=0
                            }
                        }
                    }
                },
                Files = new List<DsSpecificFileDto>
                {
                    new DsSpecificFileDto { FileName = "TestFile", S3Path = "TestPath" }
                },
                JobId = "TestJobId",
                CompanyId = "TestCompanyId",
                CompanyName = "TestCompanyName"
            };

            // Act
            var result = dsSpecificDto.ToSpecificDto("fundId");

            // Assert
            Assert.NotNull(result);
            Assert.Equal("TestJobId", result.JobId);
            Assert.Equal("TestCompanyId", result.CompanyId);
            Assert.Equal("TestCompanyName", result.CompanyName);
            Assert.Single(result.CustomSections);
        }

        [Fact]
        public void AddDocumentKpiToPeriod_ShouldAddDocumentKpi_WhenPeriodExists()
        {
            // Arrange
            var periods = new Dictionary<string, PeriodConfigurationDto>
            {
                { "2023", new PeriodConfigurationDto { PeriodId = "TestPeriodId", DocumentKpis = new List<SelectedKpiDto>() } }
            };
            var kpiGroup = new DsSpecificKpiGroupDto
            {
                Id =1,
                KpiName = "TestKpiName",
                MappingName = "TestMappingName",
                MappingId = "TestMappingId"
            };

            // Act
            periods.AddDocumentKpiToPeriod("2023", kpiGroup);

            // Assert
            Assert.Single(periods["2023"].DocumentKpis);
        }

        [Theory]
        [InlineData("usd", "$")]
        [InlineData("percentage", "%")]
        [InlineData("number", "#")]
        [InlineData("multiple", "x")]
        [InlineData("text", "Text")]
        [InlineData("unknown", "unknown")]
        public void DetermineKpiInfo_ShouldReturnCorrectSymbol(string unitType, string expected)
        {
            // Act
            var result = unitType.DetermineKpiInfo();

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void InitializeSpecificDto_ShouldReturnValidSpecificDto_WhenInputIsValid()
        {
            // Arrange
            var dsSpecificDto = new DsSpecificDto
            {
                Files = new List<DsSpecificFileDto>
                {
                    new DsSpecificFileDto { FileName = "TestFile", S3Path = "TestPath" }
                },
                JobId = "TestJobId",
                CompanyId = "TestCompanyId",
                CompanyName = "TestCompanyName"
            };

            // Act
            var result = SpecificTransformations.InitializeSpecificDto(dsSpecificDto);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("TestJobId", result.JobId);
            Assert.Equal("TestCompanyId", result.CompanyId);
            Assert.Equal("TestCompanyName", result.CompanyName);
            Assert.Single(result.Files);
        }
        public static string Read(string filePath)
        {
            if (!File.Exists(filePath))
                return string.Empty;

            return File.ReadAllText(filePath);
        }
        [Fact]
        public void ConvertToSpecificDto_ShouldReturnValidSpecificDto()
        {
            string folderName = Path.Combine("Mocks");
            string webRootPath = Path.Combine(Directory.GetCurrentDirectory(), folderName);
            string fileNameWithPath = Path.Combine(webRootPath, "DsSpecific.json");
            string dsSpecificJson = Read(fileNameWithPath);
            var dsSpecificDto = JsonSerializer.Deserialize<DsSpecificDto>(dsSpecificJson);            
            var specificDto = dsSpecificDto.ToSpecificDto("1");
            Assert.IsType<SpecificDto>(specificDto);
        }
        [Fact]
        public void GetEmptyKpiValueDto_ShouldReturnKpiValueDtoWithDefaultValues()
        {
            // Act
            var result = PdfExcelHighlightExtensions.GetEmptyKpiValueDto();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(string.Empty, result.Value);
            Assert.Equal(string.Empty, result.KpiInfo);
            Assert.Equal(string.Empty, result.Unit);
            Assert.NotNull(result.PdfHighlight);
            Assert.NotNull(result.ExcelHighlight);
            Assert.Equal(string.Empty, result.Source);
            Assert.Equal(string.Empty, result.FileType);
            Assert.Equal(string.Empty, result.UnitScale);
            Assert.Equal(0, result.PageNumber);
            // ConfidenceScore is not set in GetEmptyKpiValueDto, so it should be default(double) == 0
            Assert.Equal(0, result.ConfidenceScore);
        }

        [Fact]
        public void DetermineKpiInfo_WithNullInput_ShouldReturnNull()
        {
            // Arrange
            string? unitType = null;

            // Act
            var result = unitType.DetermineKpiInfo();

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public void ToPdfHighlightDto_WithNullPdfObject_ShouldReturnEmptyPdfHighlights()
        {
            // Arrange
            object? pdfObj = null;
            var value = "test-value";

            // Act
            var result = PdfExcelHighlightExtensions.ToPdfHighlightDto(pdfObj, value);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.PageNumber);
            Assert.NotNull(result.Bounds);
            Assert.Empty(result.Bounds);
            Assert.Null(result.Text);
        }

        [Fact]
        public void ToExcelHighlightDto_WithNullExcelObject_ShouldReturnEmptyExcelHighlights()
        {
            // Arrange
            object? excelObj = null;

            // Act
            var result = PdfExcelHighlightExtensions.ToExcelHighlightDto(excelObj);

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.Sheet);
            Assert.Null(result.Reference);
        }
    }
}
