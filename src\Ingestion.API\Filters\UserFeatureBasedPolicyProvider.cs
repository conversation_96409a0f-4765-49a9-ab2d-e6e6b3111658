﻿using Microsoft.AspNetCore.Authorization;

namespace API.Filters.CustomAuthorization
{
    public class UserFeatureBasedPolicyProvider : IAuthorizationPolicyProvider
    {
        const string UserFeature_Based_POLICY_PREFIX = "UserFeatureBased";
        public DefaultAuthorizationPolicyProvider FallbackPolicyProvider { get; }

        public Task<AuthorizationPolicy> GetDefaultPolicyAsync() => FallbackPolicyProvider.GetDefaultPolicyAsync();
        public Task<AuthorizationPolicy> GetFallbackPolicyAsync() => Task.FromResult<AuthorizationPolicy>(null);

        // Policies are looked up by string name, so expect 'parameters' (like age)
        // to be embedded in the policy names. This is abstracted away from developers
        // by the more strongly-typed attributes derived from AuthorizeAttribute
        // (like [MinimumAgeAuthorize] in this sample)
        public Task<AuthorizationPolicy> GetPolicyAsync(string policyName)
        {
            if (policyName.StartsWith(UserFeature_Based_POLICY_PREFIX, StringComparison.OrdinalIgnoreCase) )
            {
                var FeatureIdsString  = policyName.Substring(UserFeature_Based_POLICY_PREFIX.Length);

                var featureIds =  FeatureIdsString.Split(',').Select(x => int.Parse(x)).ToArray();

                var policy = new AuthorizationPolicyBuilder();
                 policy.AddRequirements(new MinimumFeaturesRequirement(featureIds));
                return Task.FromResult(policy.Build());
            }
            return Task.FromResult<AuthorizationPolicy>(new AuthorizationPolicyBuilder()
                   .AddAuthenticationSchemes("default", "identityserver")
                   .RequireAuthenticatedUser()
                   .Build());
        }
    }
    public class MinimumFeaturesRequirement : IAuthorizationRequirement
    {
        public int[] FeatureIds { get; private set; }

        public MinimumFeaturesRequirement(int[] featureId) { FeatureIds = featureId; }
    }
}
