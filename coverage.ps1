# Coverage generation script for Foliosure.Ingestion solution
Write-Host "Starting code coverage..." -ForegroundColor Green

# Clean up previous results
Remove-Item -Path "TestResults" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "coverage.json", "coverage.opencover.xml", "coverage.cobertura.xml", "coverage.info" -Force -ErrorAction SilentlyContinue

# Prepare environment
dotnet clean
dotnet restore
dotnet build --configuration Release /p:DebugType=Full

# Create code-coverage directory
$codeCoverageDir = ".\TestResults\code-coverage"
New-Item -Path $codeCoverageDir -ItemType Directory -Force | Out-Null

# Make sure coverlet.console is installed
dotnet tool install --global coverlet.console --version 3.2.0 --ignore-failed-sources

# Run the tests and collect coverage data
coverlet ".\tests\UnitTests\bin\Release\net8.0\UnitTests.dll" `
    --target "dotnet" `
    --targetargs "test ./tests/UnitTests/UnitTests.csproj --configuration Release --no-build" `
    --format "opencover" `
    --output ".\TestResults\code-coverage\coverage.opencover.xml" `
    --exclude-by-file "**/obj/**/*.cs" `
    --exclude "[*]*.Program" `
    --verbosity "detailed"

# Verify the coverage file exists
$coverageFile = ".\TestResults\code-coverage\coverage.opencover.xml"
if (-not (Test-Path $coverageFile)) {
    Write-Host "Error: Coverage file not found" -ForegroundColor Red
    exit 1
}

# Generate the HTML report
dotnet tool install --global dotnet-reportgenerator-globaltool
reportgenerator -reports:"$coverageFile" -targetdir:"./TestResults/report"

# Open the report in interactive mode only (not in CI)
if ([Environment]::UserInteractive -and -not [bool]$env:CI -and -not [bool]$env:TF_BUILD -and -not [bool]$env:CODEBUILD_BUILD_ID) {
    Invoke-Item ./TestResults/report/index.html
}

Write-Host "Coverage process complete!" -ForegroundColor Green
