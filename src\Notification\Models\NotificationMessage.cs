using System.Text.Json.Serialization;

namespace Notification.Models
{
    /// <summary>
    /// Represents a notification message to be sent via SignalR
    /// </summary>
    public class NotificationMessage
    {
        /// <summary>
        /// Gets or sets the type of notification
        /// </summary>
        public NotificationType Type { get; set; }
        
        /// <summary>
        /// Gets or sets the notification title
        /// </summary>
        public string Title { get; set; } = string.Empty;
        
        /// <summary>
        /// Gets or sets the notification content
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// Gets or sets the entity ID related to this notification (if applicable)
        /// </summary>
        public string? EntityId { get; set; }
        
        /// <summary>
        /// Gets or sets the entity type related to this notification (if applicable)
        /// </summary>
        public string? EntityType { get; set; }
        
        /// <summary>
        /// Gets or sets a timestamp for when the notification was created
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gets or sets any additional data for the notification
        /// </summary>
        public List<JobStatus> Data { get; set; } = new();
    }

    /// <summary>
    /// Represents different types of notifications
    /// </summary>
    [JsonConverter(typeof(JsonStringEnumConverter))]
    public enum NotificationType
    {
        /// <summary>
        /// Information notification
        /// </summary>
        Info,
        
        /// <summary>
        /// Success notification
        /// </summary>
        Success,
        
        /// <summary>
        /// Warning notification
        /// </summary>
        Warning,
        
        /// <summary>
        /// Error notification
        /// </summary>
        Error,

        /// <summary>
        /// Job status update notification
        /// </summary>
        JobUpdate,

        /// <summary>
        /// Document processing notification
        /// </summary>
        DocumentProcessing
    }
    public class JobStatus
    {
        public Guid JobId { get; set; }
        public Guid StatusId { get; set; }
    }
}
