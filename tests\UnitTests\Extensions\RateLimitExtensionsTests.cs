using API.Extensions;
using AspNetCoreRateLimit;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;

namespace UnitTests.Extensions
{
    public class RateLimitExtensionsTests
    {
        [Fact]
        public void AddRateLimitServices_ShouldConfigureAllRequiredServices()
        {
            // Arrange
            var services = new ServiceCollection();
            var mockConfiguration = new Mock<IConfiguration>();
            var mockRateLimitSection = new Mock<IConfigurationSection>();
            
            mockConfiguration.Setup(c => c.GetSection("RateLimitOptions")).Returns(mockRateLimitSection.Object);

            // Act
            var result = services.AddRateLimitServices(mockConfiguration.Object);

            // Assert
            Assert.Same(services, result); // Should return the same service collection for chaining

            // Verify the service provider can resolve all expected services
            var serviceProvider = services.BuildServiceProvider();
            
            // Verify memory cache is registered
            var memoryCache = serviceProvider.GetService<IMemoryCache>();
            Assert.NotNull(memoryCache);

            // Verify rate limit options are configured
            var rateLimitOptions = serviceProvider.GetService<IOptions<IpRateLimitOptions>>();
            Assert.NotNull(rateLimitOptions);

            // Verify IP policy store is registered
            var ipPolicyStore = serviceProvider.GetService<IIpPolicyStore>();
            Assert.NotNull(ipPolicyStore);
            Assert.IsType<MemoryCacheIpPolicyStore>(ipPolicyStore);

            // Verify rate limit counter store is registered
            var rateLimitCounterStore = serviceProvider.GetService<IRateLimitCounterStore>();
            Assert.NotNull(rateLimitCounterStore);
            Assert.IsType<MemoryCacheRateLimitCounterStore>(rateLimitCounterStore);

            // Verify rate limit configuration is registered
            var rateLimitConfiguration = serviceProvider.GetService<IRateLimitConfiguration>();
            Assert.NotNull(rateLimitConfiguration);
            Assert.IsType<RateLimitConfiguration>(rateLimitConfiguration);

            // Verify processing strategy is registered
            var processingStrategy = serviceProvider.GetService<IProcessingStrategy>();
            Assert.NotNull(processingStrategy);
            Assert.IsType<AsyncKeyLockProcessingStrategy>(processingStrategy);
        }

        [Fact]
        public void AddRateLimitServices_ShouldConfigureIpRateLimitOptions()
        {
            // Arrange
            var services = new ServiceCollection();
            var mockConfiguration = new Mock<IConfiguration>();
            var mockRateLimitSection = new Mock<IConfigurationSection>();
            
            mockConfiguration.Setup(c => c.GetSection("RateLimitOptions")).Returns(mockRateLimitSection.Object);

            // Act
            services.AddRateLimitServices(mockConfiguration.Object);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var options = serviceProvider.GetService<IOptions<IpRateLimitOptions>>();
            
            Assert.NotNull(options);
            Assert.NotNull(options.Value);
            
            // Verify the configuration section was used
            mockConfiguration.Verify(c => c.GetSection("RateLimitOptions"), Times.Once);
        }

        [Fact]
        public void AddRateLimitServices_WithNullConfiguration_ShouldThrowException()
        {
            // Arrange
            var services = new ServiceCollection();
            IConfiguration? nullConfiguration = null;

            // Act & Assert - The method throws NullReferenceException due to direct access without null checking
            Assert.Throws<NullReferenceException>(() => services.AddRateLimitServices(nullConfiguration!));
        }

        [Fact]
        public void AddRateLimitServices_WithNullServices_ShouldThrowArgumentNullException()
        {
            // Arrange
            IServiceCollection? nullServices = null;
            var mockConfiguration = new Mock<IConfiguration>();

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => nullServices!.AddRateLimitServices(mockConfiguration.Object));
        }

        [Fact]
        public void AddRateLimitServices_ShouldRegisterServicesWithCorrectLifetime()
        {
            // Arrange
            var services = new ServiceCollection();
            var mockConfiguration = new Mock<IConfiguration>();
            var mockRateLimitSection = new Mock<IConfigurationSection>();
            
            mockConfiguration.Setup(c => c.GetSection("RateLimitOptions")).Returns(mockRateLimitSection.Object);

            // Act
            services.AddRateLimitServices(mockConfiguration.Object);

            // Assert - Check service lifetimes
            var memoryCacheDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IMemoryCache));
            Assert.NotNull(memoryCacheDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, memoryCacheDescriptor.Lifetime);

            var ipPolicyStoreDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IIpPolicyStore));
            Assert.NotNull(ipPolicyStoreDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, ipPolicyStoreDescriptor.Lifetime);

            var rateLimitCounterStoreDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IRateLimitCounterStore));
            Assert.NotNull(rateLimitCounterStoreDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, rateLimitCounterStoreDescriptor.Lifetime);

            var rateLimitConfigurationDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IRateLimitConfiguration));
            Assert.NotNull(rateLimitConfigurationDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, rateLimitConfigurationDescriptor.Lifetime);

            var processingStrategyDescriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IProcessingStrategy));
            Assert.NotNull(processingStrategyDescriptor);
            Assert.Equal(ServiceLifetime.Singleton, processingStrategyDescriptor.Lifetime);
        }

        [Fact]
        public void AddRateLimitServices_CalledMultipleTimes_ShouldNotDuplicateServices()
        {
            // Arrange
            var services = new ServiceCollection();
            var mockConfiguration = new Mock<IConfiguration>();
            var mockRateLimitSection = new Mock<IConfigurationSection>();
            
            mockConfiguration.Setup(c => c.GetSection("RateLimitOptions")).Returns(mockRateLimitSection.Object);

            // Act - Call the extension method multiple times
            services.AddRateLimitServices(mockConfiguration.Object);
            var countAfterFirst = services.Count;
            
            services.AddRateLimitServices(mockConfiguration.Object);
            var countAfterSecond = services.Count;

            // Assert - Services should be added again (this is expected behavior for most DI extensions)
            // The container will resolve the last registered implementation
            Assert.True(countAfterSecond > countAfterFirst);
        }

        [Fact]
        public void AddRateLimitServices_ShouldAddInMemoryRateLimiting()
        {
            // Arrange
            var services = new ServiceCollection();
            var mockConfiguration = new Mock<IConfiguration>();
            var mockRateLimitSection = new Mock<IConfigurationSection>();
            
            mockConfiguration.Setup(c => c.GetSection("RateLimitOptions")).Returns(mockRateLimitSection.Object);

            // Act
            services.AddRateLimitServices(mockConfiguration.Object);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            
            // Verify that all rate limiting services work together
            var memoryCache = serviceProvider.GetRequiredService<IMemoryCache>();
            var ipPolicyStore = serviceProvider.GetRequiredService<IIpPolicyStore>();
            var rateLimitCounterStore = serviceProvider.GetRequiredService<IRateLimitCounterStore>();
            var rateLimitConfiguration = serviceProvider.GetRequiredService<IRateLimitConfiguration>();
            var processingStrategy = serviceProvider.GetRequiredService<IProcessingStrategy>();
            
            Assert.NotNull(memoryCache);
            Assert.NotNull(ipPolicyStore);
            Assert.NotNull(rateLimitCounterStore);
            Assert.NotNull(rateLimitConfiguration);
            Assert.NotNull(processingStrategy);
            
            // Verify specific implementations are used
            Assert.IsType<MemoryCacheIpPolicyStore>(ipPolicyStore);
            Assert.IsType<MemoryCacheRateLimitCounterStore>(rateLimitCounterStore);
            Assert.IsType<RateLimitConfiguration>(rateLimitConfiguration);
            Assert.IsType<AsyncKeyLockProcessingStrategy>(processingStrategy);
        }
    }
}