using DataIngestionService.IServices;
using Quartz;

namespace API.Configuration
{
    [DisallowConcurrentExecution]
    public class JobsUpdateJob(ILogger<JobsUpdateJob> logger, IServiceProvider serviceProvider) : IJob
    {
        private readonly ILogger<JobsUpdateJob> _logger = logger;
        private readonly IServiceProvider _serviceProvider = serviceProvider;

        public async Task Execute(IJobExecutionContext context)
        {
            _logger.LogInformation("JobsUpdateJob started at {Time}", DateTime.UtcNow);            
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var jobsUpdateService = scope.ServiceProvider.GetRequiredService<IJobsUpdateService>();
                await jobsUpdateService.UpdateJobsStatus(default);
                _logger.LogInformation("JobsUpdateJob completed successfully at {Time}", DateTime.UtcNow);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "<PERSON>rror executing <PERSON>sUpdateJob at {Time}", DateTime.UtcNow);
            }
        }
    }
}