﻿using Dapper;
using DapperRepository;
using System.Data;
using static Dapper.SqlMapper;

namespace Persistence.DapperRepository
{
    public class DapperGenericRepository : IDapperGenericRepository, IDisposable
    {
        private readonly IDbConnectionFactory _dbConnectionFactory;
        private readonly IDbConnection _connection;

        public DapperGenericRepository(IDbConnectionFactory dbConnectionFactory)
        {
            _dbConnectionFactory = dbConnectionFactory;
            _connection = _dbConnectionFactory.CreateConnection();
        }

        public async Task<List<T>> Query<T>(string query, object parameters = null)
        {
            var result = await _connection.QueryAsync<T>(query, parameters, commandTimeout: 12000);
            return result.ToList();
        }

        public async Task<T> QueryFirstAsync<T>(string strSql, object param)
        {
#pragma warning disable CS8603 // Possible null reference return.
            return await _connection.QueryFirstOrDefaultAsync<T>(strSql, param, commandTimeout: 12000);
#pragma warning restore CS8603 // Possible null reference return.
        }
        public async Task<int> QueryExecuteSpAsync<T>(string strSql, object param)
        {
            return await _connection.ExecuteAsync(strSql, param, commandType: CommandType.StoredProcedure, commandTimeout: 12000);
        }
        public void Dispose()
        {
            _connection?.Dispose();
        }

        public string GetConnectionString()
        {
            return _connection.ConnectionString;
        }
    }
}
