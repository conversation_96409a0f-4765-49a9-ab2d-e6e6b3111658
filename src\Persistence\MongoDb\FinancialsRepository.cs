﻿using Microsoft.Extensions.Options;
using MongoDB.Driver;
using Persistence.Models;

namespace Persistence.MongoDb
{
    public interface IFinancialsRepository : IRepository<Financials>
    {
        Task<Guid> AddFinancialsAsync(Financials details);
        Task<Financials> GetMetaDataByJobId(Guid processId);
        Task<bool> UpdateFinancialsByProcessIdAsync(Guid processId, Financials details);
        Task<List<PdfHighlight>> GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(Guid processId, string tableGroupLabel, string periodDate);
    }
    
    public class FinancialsRepository : Repository<Financials>, IFinancialsRepository
    {
        public FinancialsRepository(IOptions<MongoDbSettings> settings) : base(settings)
        {
        }
        
        public async Task<Guid> AddFinancialsAsync(Financials details)
        {
            details.Id = Guid.NewGuid();
            details.IsDeleted = false;
            await CreateAsync(details);
            return details.Id;
        }

        public async Task<Financials> GetMetaDataByJobId(Guid processId)
        {
            var filter = Builders<Financials>.Filter.And(
                Builders<Financials>.Filter.Eq(nameof(Financials.IsDeleted), false),
                Builders<Financials>.Filter.Eq(nameof(Financials.ProcessID), processId)
            );            
            return await _collection
                .Find(filter)
                .SortByDescending(x => x.ModifiedOn)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> UpdateFinancialsByProcessIdAsync(Guid processId, Financials details)
        {
            details.ModifiedOn = DateTime.UtcNow;
            details.ModifiedBy = 3;
            var filter = Builders<Financials>.Filter.And(
                Builders<Financials>.Filter.Eq(nameof(Financials.IsDeleted), false),
                Builders<Financials>.Filter.Eq(nameof(Financials.Id), details.Id),
                Builders<Financials>.Filter.Eq(nameof(Financials.ProcessID), processId)
            );
            
            var result = await _collection.ReplaceOneAsync(filter, details);
            return result.ModifiedCount > 0;
        }
        public async Task<List<PdfHighlight>> GetPdfHighlightsByProcessIdTableGroupLabelAndPeriodDate(Guid processId, string tableGroupLabel, string periodDate)
        {
            var pdfHighlights = new List<PdfHighlight>();
            var financials =  await GetMetaDataByJobId(processId);
            if (financials == null || financials.TableGroups == null)
                return pdfHighlights;

            var matchingGroups = financials.TableGroups.Where(g => g.Label == tableGroupLabel);
            foreach (var group in matchingGroups)
            {
                if (group.Tables == null) continue;
                foreach (var table in group.Tables)
                {
                    if (table.Columns == null || table.Rows == null) continue;
                    var column = table.Columns.FirstOrDefault(c => c.PeriodDate == periodDate);
                    if (column == null) continue;
                    string columnKey = column.ColumnKey;
                    foreach (var row in table.Rows.Where(x => x.Label != null && x.Label.MappingId == 21183).ToList())
                    {
                        if (row.Cells == null) continue;
                        // Only check cells whose ColumnKey matches the parent columnKey
                        var cell = row.Cells.FirstOrDefault(c => c.ColumnKey == columnKey && c.PdfHighlight != null);
                        if (cell != null)
                        {
                            pdfHighlights.Add(cell.PdfHighlight);
                        }
                    }
                }
            }
            return pdfHighlights;
        }
    }
}
;