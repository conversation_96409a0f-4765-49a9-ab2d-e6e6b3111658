﻿using DataIngestionService.Constants;
using Infrastructure.Contract;
using Persistence.Models;
using System.Text.Json;

namespace DataIngestionService.Helpers
{
    public static class ExtractHelper
    {
        public static JsonSerializerOptions GetJsonSerializerOptions()
        {
            return new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }
        public static List<PageModule>? DeserializePageModules(this string pages)
        {
                return JsonSerializer.Deserialize<List<PageModule>>(pages, GetJsonSerializerOptions());
        }
        public static List<string>? DeserializeErrors(this string error)
        {
            if (string.IsNullOrEmpty(error))
                return new List<string>();
            return JsonSerializer.Deserialize<List<string>>(error, GetJsonSerializerOptions());
        }
        public static List<string> GetAllExtractionStatuses()
        {
            return
            [
                ApiConstants.ExtractionFailed,ApiConstants.ExtractionInProgress,ApiConstants.ExtractionCompleted
            ];
        }
        public static List<string> GetAllDataIngestionStatuses()
        {
            return new List<string>
             {
                 { "Data Ingestion Completed" },
                 { "Data Ingestion in progress" },
                 { "Data Ingestion Failed" }
             };
        }

    }
}
