using Infrastructure.Contract;
using System;
using System.Collections.Generic;
using Xunit;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Text;

namespace UnitTests.Infrastructure.Contract
{
    public class ContractDtoTests
    {
        [Fact]
        public void UploadDto_ShouldHaveValidDefaults()
        {
            // Arrange & Act
            var dto = new UploadDto
            {
                Id = Guid.NewGuid(),
                TenantId = Guid.NewGuid(),
                ExtractionType = "Test"
            };

            // Assert
            Assert.NotEqual(Guid.Empty, dto.ProcessId);
            Assert.Equal(string.Empty, dto.UpdateProcessId);
            Assert.NotNull(dto.Files);
            Assert.Empty(dto.Files);
        }

        [Fact]
        public void DocumentSummaryDto_ShouldInitializeCollectionsCorrectly()
        {
            // Arrange & Act
            var dto = new DocumentSummaryDto
            {
                Id = Guid.NewGuid(),
                S3Path = "test/path",
                Name = "test.pdf",
                Url = "http://test.com/test.pdf",
                DocumentTypeId = 1,
                DocumentType = "PDF"
            };

            // Assert
            Assert.NotNull(dto.Errors);
            Assert.Empty(dto.Errors);
            Assert.Equal(string.Empty, dto.FileStatus);
        }

        [Fact]
        public void ProcessDetailsDto_ShouldInitializeCollectionsCorrectly()
        {
            // Arrange & Act
            var dto = new ProcessDetailsDto
            {
                ProcessId = Guid.NewGuid(),
                CompanyId = "123",
                CompanyName = "Test Company"
            };

            // Assert
            Assert.NotNull(dto.Documents);
            Assert.Empty(dto.Documents);
            Assert.False(dto.IsClassifiers);
        }

        [Fact]
        public void FileConfigurationDetails_ShouldHandleNullableProperties()
        {
            // Arrange & Act
            var dto = new FileConfigurationDetails
            {
                FileId = "test-file",
                DocumentTypeId = 1
            };

            // Assert
            Assert.Null(dto.DocumentType);
            Assert.Null(dto.PeriodType);
            Assert.Null(dto.Year);
            Assert.Null(dto.Month);
            Assert.Null(dto.Quarter);
        }

        [Fact]
        public void CreateJobs_ShouldInitializeCorrectly()
        {
            // Arrange
            var jobId = Guid.NewGuid();
            var processId = Guid.NewGuid();
            var tenantId = Guid.NewGuid();
            var statusId = Guid.NewGuid();
            var parentJobId = Guid.NewGuid();

            // Act
            var job = new CreateJobs
            {
                JobId = jobId,
                ProcessId = processId,
                TenantId = tenantId,
                StatusId = statusId,
                ParentJobId = parentJobId
            };

            // Assert
            Assert.Equal(jobId, job.JobId);
            Assert.Equal(processId, job.ProcessId);
            Assert.Equal(tenantId, job.TenantId);
            Assert.Equal(statusId, job.StatusId);
            Assert.Equal(parentJobId, job.ParentJobId);
        }

        [Fact]
        public void DocumentDetailDto_ShouldInheritFromCreateJobs()
        {
            // Arrange & Act
            var dto = new DocumentDetailDto
            {
                CompanyName = "Test Company",
                CompanyId = 1,
                CreatedOn = DateTime.UtcNow,
                Status = "Active",
                DocumentId = Guid.NewGuid(),
                JobId = Guid.NewGuid(),
                ProcessId = Guid.NewGuid()
            };

            // Assert
            Assert.IsAssignableFrom<CreateJobs>(dto);
            Assert.Equal("Test Company", dto.CompanyName);
            Assert.Equal(1, dto.CompanyId);
            Assert.NotEqual(default, dto.CreatedOn);
            Assert.Equal("Active", dto.Status);
        }
    }
}
