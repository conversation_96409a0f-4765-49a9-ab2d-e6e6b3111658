using API.Controllers;
using Amazon.S3.Model;
using DataIngestionService.IServices;
using Infrastructure.Contract;
using Infrastructure.Contract.BlobStorage;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.IO;
using System.Net;
using System.Security.Claims;
using Xunit;

namespace UnitTests.Controllers
{
    public class FilesControllerTests
    {
        private readonly Mock<IFileService> _mockFileService;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly Mock<ILogger<FilesController>> _mockLogger;
        private readonly FilesController _controller;

        public FilesControllerTests()
        {
            _mockFileService = new Mock<IFileService>();
            _mockHelperService = new Mock<IHelperService>();
            _mockLogger = new Mock<ILogger<FilesController>>();

            _controller = new FilesController(
                _mockFileService.Object,
                _mockHelperService.Object,
                _mockLogger.Object
            );

            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.NameIdentifier, "1")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
        }

        #region GetDocumentFromBlobStorage Tests

        [Fact]
        public async Task GetDocumentFromBlobStorage_ValidRequest_ReturnsOk()
        {
            // Arrange
            var request = new BlobStorageRequest { Key = "test-key" };
            var expectedResponse = new BlobStorageResponse 
            { 
                File = "Value1", 
                FileKey = "Value2", 
                FileName = "file.pdf" 
            };
            
            _mockFileService.Setup(f => f.GetDocumentFromBlobStorage(request))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.GetDocumentFromBlobStorage(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedResponse = Assert.IsType<BlobStorageResponse>(okResult.Value);
            Assert.Equal(expectedResponse.File, returnedResponse.File);
            Assert.Equal(expectedResponse.FileKey, returnedResponse.FileKey);
            Assert.Equal(expectedResponse.FileName, returnedResponse.FileName);
        }

        [Fact]
        public async Task GetDocumentFromBlobStorage_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            var request = new BlobStorageRequest { Key = null }; // Simulate invalid input
            _controller.ModelState.AddModelError("Key", "Required");

            // Act
            var result = await _controller.GetDocumentFromBlobStorage(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.IsType<SerializableError>(badRequestResult.Value);
        }

        #endregion

        #region DownloadFileByKey Tests


        [Fact]
        public async Task DownloadFileByKey_EmptyKey_ReturnsBadRequest()
        {
            // Arrange
            string fileKey = "";

            // Act
            var result = await _controller.DownloadFileByKey(fileKey);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.Equal("File key is required", badRequestResult.Value);
        }

        [Fact]
        public async Task DownloadFileByKey_NullKey_ReturnsBadRequest()
        {
            // Arrange
            string fileKey = null;

            // Act
            var result = await _controller.DownloadFileByKey(fileKey);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.Equal("File key is required", badRequestResult.Value);
        }

        [Fact]
        public async Task DownloadFileByKey_ServiceReturnsNull_ReturnsNotFound()
        {
            // Arrange
            string fileKey = "Documents/folder/nonexistent-file.pdf";
            
            _mockFileService.Setup(f => f.DownloadFileObject(fileKey))
                .ReturnsAsync((GetObjectResponse)null);

            // Act
            var result = await _controller.DownloadFileByKey(fileKey);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            Assert.Equal($"File with key '{fileKey}' was not found", notFoundResult.Value);
        }

        #endregion

        #region DeleteFile Tests

        [Fact]
        public async Task DeleteFile_ValidRequest_ReturnsOk()
        {
            // Arrange
            var request = new DeleteDocument { Id = Guid.NewGuid(), Key = "test-key" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).ReturnsAsync(1);
            _mockFileService.Setup(f => f.DeleteFile(request, 1)).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteFile(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value);
        }

        [Fact]
        public async Task DeleteFile_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            var request = new DeleteDocument { Id = Guid.Empty }; // Simulate invalid input
            _controller.ModelState.AddModelError("Id", "Required");

            // Act
            var result = await _controller.DeleteFile(request);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.IsType<SerializableError>(badRequestResult.Value);
        }

        [Fact]
        public async Task DeleteFile_ServiceReturnsFalse_ReturnsOkWithFalse()
        {
            // Arrange
            var request = new DeleteDocument { Id = Guid.NewGuid(), Key = "test-key" };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).ReturnsAsync(1);
            _mockFileService.Setup(f => f.DeleteFile(request, 1)).ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteFile(request);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.False((bool)okResult.Value);
        }
        
        #endregion

        #region GetDocumentsByProcessId Tests

        [Fact]
        public async Task GetDocumentsByProcessId_ValidRequest_ReturnsOk()
        {
            // Arrange
            var processId = Guid.NewGuid();
            var expectedResponse = new BlobStorageResponse 
            { 
                File = "Value1", 
                FileKey = "Value2", 
                FileName = "file.pdf" 
            };
            
            _mockFileService.Setup(f => f.GetDocumentsByProcessId(processId))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _controller.GetDocumentsByProcessId(processId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedResponse = Assert.IsType<BlobStorageResponse>(okResult.Value);
            Assert.Equal(expectedResponse.File, returnedResponse.File);
            Assert.Equal(expectedResponse.FileKey, returnedResponse.FileKey);
            Assert.Equal(expectedResponse.FileName, returnedResponse.FileName);
        }

        [Fact]
        public async Task GetDocumentsByProcessId_ServiceReturnsNull_ReturnsOkWithNull()
        {
            // Arrange
            var processId = Guid.NewGuid();
            _mockFileService.Setup(f => f.GetDocumentsByProcessId(processId))
                .ReturnsAsync((BlobStorageResponse)null);

            // Act
            var result = await _controller.GetDocumentsByProcessId(processId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            Assert.Null(okResult.Value);
        }

        #endregion
    }
}
