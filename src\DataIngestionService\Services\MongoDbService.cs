﻿using DataIngestionService.IServices;
using Infrastructure.Contract;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using Persistence.Models;
using Persistence.Models.Classifier;
using Persistence.MongoDb;
using Persistence.UnitOfWork;

namespace DataIngestionService.Services
{
    public class MongoDbService : IMongoDb
    {
        private readonly IRepository<IssuerModel> _issuerDetails;
        private readonly IRepository<Classifier> _classifier;
        private readonly IRepository<FundKpiModel> _fundKpiDetails;
        private readonly ILogger<MongoDbService> _logger;
        public MongoDbService(IRepositoryFactory repositoryFactory, ILogger<MongoDbService> logger)
        {
            _classifier = repositoryFactory.GetRepository<Classifier>();
            _issuerDetails = repositoryFactory.GetRepository<IssuerModel>();
            _fundKpiDetails = repositoryFactory.GetRepository<FundKpiModel>();
            _logger = logger;
        }
        
        public async Task<string> AddIssuerDetails(UploadDto request, int userId)
        {
            if (string.IsNullOrEmpty(request.CompanyIssuers))
                return string.Empty;
            var selectedIssuers = JsonConvert.DeserializeObject<List<IssuerKPIModel>>(request.CompanyIssuers);
            var issueDetails = new IssuerModel
            {
                FundId = request.FundId,
                CreatedBy = userId,
                IssuerKPIModel = selectedIssuers,
                ProcessId = request.ProcessId
            };
            await _issuerDetails.CreateAsync(issueDetails);
            return issueDetails.Id;
        }
        public async Task<string> AddSubPageDetails(UploadDto request, int userId)
        {
            if (string.IsNullOrEmpty(request.FundKpiDetails))
                return string.Empty;
            var subPageDetails = JsonConvert.DeserializeObject<List<SubPageFieldsModel>>(request.FundKpiDetails);
            var fundKpiDetails = new FundKpiModel
            {
                CreatedBy = userId,
                SubPageFieldsModel = subPageDetails,
                ProcessId = request.ProcessId
            };
            await _fundKpiDetails.CreateAsync(fundKpiDetails);
            return fundKpiDetails.Id;
        }
        public async Task<bool> GetClassifiersByProcessId(List<Guid> processIds)
        {
            var classifiers = await _classifier.GetAllAsync(x => processIds.Contains(x.ProcessId) && !x.IsDeleted);
            if (!classifiers.Any())
            {
                _logger.LogDebug("No classifiers found for the specified process IDs");
                return false;
            }
            bool hasIncompleteClassifiers = classifiers.Any(x => x.TableSuggestionResponse == null);
            if (hasIncompleteClassifiers)
            {
                _logger.LogDebug("Found {Count} classifiers, some with incomplete table suggestions", classifiers.Count());
                return false;
            }
            _logger.LogDebug("Found {Count} classifiers with complete table suggestions", classifiers.Count());
            return true;
        }

        public async Task<List<IssuerKPIModel>> GetIssuerDetails(Guid processId)
        {
            var issuerDetails = await _issuerDetails.GetAllAsync(x => x.ProcessId == processId && !x.IsDeleted);
            if (issuerDetails == null || !issuerDetails.Any())
            {
                _logger.LogDebug("No issuer details found for the specified process ID: {ProcessId}", processId);
                return new List<IssuerKPIModel>();
            }
            return issuerDetails.SelectMany(x => x.IssuerKPIModel).ToList();
        }

        public async Task<List<SubPageFieldsModel>> GetFundKpiDetails(Guid processId)
        {
            var fundKpiDetails = await _fundKpiDetails.GetAllAsync(x => x.ProcessId == processId && !x.IsDeleted);
            if (fundKpiDetails == null || !fundKpiDetails.Any())
            {
                _logger.LogDebug("No kpi detail details found for the specified process ID: {ProcessId}", processId);
                return new List<SubPageFieldsModel>();
            }
            return fundKpiDetails.SelectMany(x => x.SubPageFieldsModel).ToList();
        }
    }
}
