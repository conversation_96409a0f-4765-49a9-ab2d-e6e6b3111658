﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.SwaggerUI;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;

namespace API.Extensions
{
    public static class SwaggerConfig
    {
        public static void SwaggerConfiguration(this IServiceCollection services)
        {
            services
                .AddOptions<SwaggerUIOptions>()
                .Configure<IHttpContextAccessor>((swaggerUiOptions, httpContextAccessor) =>
                {
                    // 2. Take a reference of the original Stream factory which reads from Swashbuckle's embedded resources
                    var originalIndexStreamFactory = swaggerUiOptions.IndexStream;

                    // 3. Override the Stream factory
                    swaggerUiOptions.IndexStream = () =>
                    {
                        // 4. Read the original index.html file
                        using var originalStream = originalIndexStreamFactory();
                        using var originalStreamReader = new StreamReader(originalStream);
                        var originalIndexHtmlContents = originalStreamReader.ReadToEnd();

                        // 5. Get the request-specific nonce generated by NetEscapades.AspNetCore.SecurityHeaders
                        var requestSpecificNonce = RandomString(9);

                        // 6. Replace inline `<script>` and `<style>` tags by adding a `nonce` attribute to them
                        var nonceEnabledIndexHtmlContents = originalIndexHtmlContents
                            .Replace("<script>", $"<script nonce=\"{requestSpecificNonce}\">", StringComparison.OrdinalIgnoreCase)
                            .Replace("<style>", $"<style nonce=\"{requestSpecificNonce}\">", StringComparison.OrdinalIgnoreCase);

                        // 7. Return a new Stream that contains our modified contents
                        return new MemoryStream(Encoding.UTF8.GetBytes(nonceEnabledIndexHtmlContents));
                    };
                });
            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "FolioSure Ingestion API", Version = "1.0" });
                options.SwaggerDoc("v1.1", new OpenApiInfo { Title = "FolioSure Ingestion API", Version = "1.0" });
                options.OperationFilter<RemoveVersionFromParameter>();
                options.DocumentFilter<ReplaceVersionWithExactValueInPath>();
                options.SchemaFilter<SwaggerExcludeFilter>();
            });
        }
        private static string RandomString(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            byte[] data = new byte[length];
            using (RandomNumberGenerator rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(data);
            }
            char[] result = new char[length];
            for (int i = 0; i < length; i++)
            {
                result[i] = chars[data[i] % chars.Length];
            }
            return new string(result);
        }
        private class RemoveVersionFromParameter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                if (operation.Parameters.Any(S => S.Name == "version"))
                {
                    var versionParameter = operation.Parameters.Single(p => p.Name == "version");
                    operation.Parameters.Remove(versionParameter);
                }
            }
        }
        private class ReplaceVersionWithExactValueInPath : IDocumentFilter
        {
            public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
            {
                var paths = new OpenApiPaths();
                foreach (var path in swaggerDoc.Paths)
                {
                    paths.Add(path.Key.Replace("v{version}", swaggerDoc.Info.Version), path.Value);
                }
                swaggerDoc.Paths = paths;
            }
        }
        public class SwaggerExcludeFilter : ISchemaFilter
        {
            #region ISchemaFilter Members
            public void Apply(OpenApiSchema schema, SchemaFilterContext context)
            {
                if (schema?.Properties?.Count > 0)
                {
                    var excludedProperties = context.Type.GetProperties()
                                                 .Where(t =>
                                                        t.GetCustomAttribute<SwaggerExcludeAttribute>()
                                                        != null);
                    foreach (var excludedProperty in excludedProperties)
                    {
                        var keys = schema.Properties.Where(x => string.Equals(x.Key, excludedProperty.Name, StringComparison.OrdinalIgnoreCase));
                        if (keys?.Any() == true)
                            schema.Properties.Remove(keys.FirstOrDefault().Key);
                    }
                }

            }

            #endregion
        }
        [AttributeUsage(AttributeTargets.Property)]
        public class SwaggerExcludeAttribute : Attribute
        {
        }
    }
}
