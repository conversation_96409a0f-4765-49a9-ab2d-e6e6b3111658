﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ingestion.API", "src\Ingestion.API\Ingestion.API.csproj", "{D35D67DB-912E-457F-9787-55A79EEA261C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DataIngestionService", "src\DataIngestionService\DataIngestionService.csproj", "{B0A4F67A-E968-4031-9395-ADEC1B21E6D5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Infrastructure", "src\Infrastructure\Infrastructure.csproj", "{4B74B8BF-E40B-4DC1-8621-6365A491B7AE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Persistence", "src\Persistence\Persistence.csproj", "{731604EE-818F-4D90-8008-17923446C35A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AWSS3", "src\AWSS3\AWSS3.csproj", "{44612DCD-11BA-49BC-8A4C-DB30B32AB2FD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Notification", "src\Notification\Notification.csproj", "{71E89CA2-57B5-4FD1-B686-00D157F9DD19}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnitTests", "tests\UnitTests\UnitTests.csproj", "{D0A1E639-7C7B-46D6-A01C-72A7BC3B8C4D}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D35D67DB-912E-457F-9787-55A79EEA261C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D35D67DB-912E-457F-9787-55A79EEA261C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D35D67DB-912E-457F-9787-55A79EEA261C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D35D67DB-912E-457F-9787-55A79EEA261C}.Release|Any CPU.Build.0 = Release|Any CPU
		{B0A4F67A-E968-4031-9395-ADEC1B21E6D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B0A4F67A-E968-4031-9395-ADEC1B21E6D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B0A4F67A-E968-4031-9395-ADEC1B21E6D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B0A4F67A-E968-4031-9395-ADEC1B21E6D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{4B74B8BF-E40B-4DC1-8621-6365A491B7AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4B74B8BF-E40B-4DC1-8621-6365A491B7AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4B74B8BF-E40B-4DC1-8621-6365A491B7AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4B74B8BF-E40B-4DC1-8621-6365A491B7AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{731604EE-818F-4D90-8008-17923446C35A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{731604EE-818F-4D90-8008-17923446C35A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{731604EE-818F-4D90-8008-17923446C35A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{731604EE-818F-4D90-8008-17923446C35A}.Release|Any CPU.Build.0 = Release|Any CPU
		{44612DCD-11BA-49BC-8A4C-DB30B32AB2FD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44612DCD-11BA-49BC-8A4C-DB30B32AB2FD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44612DCD-11BA-49BC-8A4C-DB30B32AB2FD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44612DCD-11BA-49BC-8A4C-DB30B32AB2FD}.Release|Any CPU.Build.0 = Release|Any CPU
		{71E89CA2-57B5-4FD1-B686-00D157F9DD19}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{71E89CA2-57B5-4FD1-B686-00D157F9DD19}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{71E89CA2-57B5-4FD1-B686-00D157F9DD19}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{71E89CA2-57B5-4FD1-B686-00D157F9DD19}.Release|Any CPU.Build.0 = Release|Any CPU
		{D0A1E639-7C7B-46D6-A01C-72A7BC3B8C4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0A1E639-7C7B-46D6-A01C-72A7BC3B8C4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0A1E639-7C7B-46D6-A01C-72A7BC3B8C4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0A1E639-7C7B-46D6-A01C-72A7BC3B8C4D}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {EFB025C0-07C7-40DC-879A-26F3DADD3A8B}
	EndGlobalSection
EndGlobal
