﻿using System.ComponentModel.DataAnnotations.Schema;
namespace Persistence.Models
{
    public class Jobs:BaseEntity
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }
        public Guid JobId { get; set; }
        public Guid ProcessId { get; set; }
        public Guid StatusId { get; set; }
        public Guid TenantId { get; set; }
        public Guid ParentJobId { get; set; }
    }
}
