using DataIngestionService.Constants;
using DataIngestionService.Helpers;
using Infrastructure.Contract;
using System.Collections.Generic;
using System.Text.Json;
using Xunit;

namespace UnitTests.Helpers
{
    public class ExtractHelperTests
    {
        [Fact]
        public void GetJsonSerializerOptions_ShouldReturnCorrectOptions()
        {
            // Act
            var options = ExtractHelper.GetJsonSerializerOptions();

            // Assert
            Assert.NotNull(options);
            Assert.True(options.PropertyNameCaseInsensitive);
        }

        [Fact]
        public void DeserializePageModules_WithValidJson_ShouldReturnDeserializedList()
        {
            // Arrange
            var pageModules = new List<PageModule>
            {
                new PageModule { ModuleId = 1, ModuleName = "Module1", Items = "Items1" },
                new PageModule { ModuleId = 2, ModuleName = "Module2", Items = "Items2" }
            };
            var json = JsonSerializer.Serialize(pageModules, ExtractHelper.GetJsonSerializerOptions());

            // Act
            var result = json.DeserializePageModules();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(1, result[0].ModuleId);
            Assert.Equal("Module1", result[0].ModuleName);
            Assert.Equal("Items1", result[0].Items);
            Assert.Equal(2, result[1].ModuleId);
            Assert.Equal("Module2", result[1].ModuleName);
            Assert.Equal("Items2", result[1].Items);
        }

        [Fact]
        public void DeserializeErrors_WithNullInput_ShouldReturnEmptyList()
        {
            // Arrange
            string? error = null;

            // Act
            var result = error.DeserializeErrors();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void DeserializeErrors_WithEmptyString_ShouldReturnEmptyList()
        {
            // Arrange
            var error = string.Empty;

            // Act
            var result = error.DeserializeErrors();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void DeserializeErrors_WithValidJson_ShouldReturnDeserializedList()
        {
            // Arrange
            var errors = new List<string> { "Error1", "Error2" };
            var json = JsonSerializer.Serialize(errors, ExtractHelper.GetJsonSerializerOptions());

            // Act
            var result = json.DeserializeErrors();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("Error1", result[0]);
            Assert.Equal("Error2", result[1]);
        }

        [Fact]
        public void GetAllExtractionStatuses_ShouldReturnCorrectStatuses()
        {
            // Act
            var statuses = ExtractHelper.GetAllExtractionStatuses();

            // Assert
            Assert.NotNull(statuses);
            Assert.Equal(3, statuses.Count);
            Assert.Contains(ApiConstants.ExtractionFailed, statuses);
            Assert.Contains(ApiConstants.ExtractionInProgress, statuses);
            Assert.Contains(ApiConstants.ExtractionCompleted, statuses);
        }

        [Fact]
        public void GetAllDataIngestionStatuses_ShouldReturnCorrectStatuses()
        {
            // Act
            var statuses = ExtractHelper.GetAllDataIngestionStatuses();

            // Assert
            Assert.NotNull(statuses);
            Assert.Equal(3, statuses.Count);
            Assert.Contains("Data Ingestion Completed", statuses);
            Assert.Contains("Data Ingestion in progress", statuses);
            Assert.Contains("Data Ingestion Failed", statuses);
        }

        [Fact]
        public void DeserializePageModules_WithInvalidJson_ShouldThrowJsonException()
        {
            // Arrange
            var invalidJson = "{ invalid json structure";

            // Act & Assert
            Assert.Throws<JsonException>(() => invalidJson.DeserializePageModules());
        }

        [Fact]
        public void DeserializeErrors_WithInvalidJson_ShouldThrowJsonException()
        {
            // Arrange
            var invalidJson = "{ invalid json structure";

            // Act & Assert
            Assert.Throws<JsonException>(() => invalidJson.DeserializeErrors());
        }

        [Fact]
        public void DeserializePageModules_WithEmptyArrayJson_ShouldReturnEmptyList()
        {
            // Arrange
            var emptyArrayJson = "[]";

            // Act
            var result = emptyArrayJson.DeserializePageModules();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }
    }
}
