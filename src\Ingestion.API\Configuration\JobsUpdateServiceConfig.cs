using Quartz;

namespace API.Configuration
{
    public static class JobsUpdateServiceConfig
    {
        public static void ConfigureJobsUpdateService(this IServiceCollection services)
        {
            _=services.AddQuartz(q =>
            {
                var jobKey = new JobKey("JobsUpdateJob");
                q.AddJob<JobsUpdateJob>(opts => opts.WithIdentity(jobKey));
                q.AddTrigger(opts => opts
                    .ForJob(jobKey) 
                    .WithIdentity("JobsUpdateTrigger")
                    .WithSimpleSchedule(s => s
                        .WithIntervalInSeconds(15) 
                        .RepeatForever())
                );
            });
            _ = services.AddQuartzHostedService(opt =>
            {
                opt.WaitForJobsToComplete = true;
            });
        }
    }
}