Clear-Host
Import-Module WebAdministration

if ($PSHOME -like "*SysWOW64*") {
    Write-Warning "Restarting this script under 64-bit Windows PowerShell."

    # Restart this script under 64-bit Windows PowerShell.
    #   (\SysNative\ redirects to \System32\ for 64-bit mode)

    Write-Warning $PSScriptRoot
    Write-Warning $MyInvocation.MyCommand
    Write-Warning $MyInvocation.InvocationName
    & (Join-Path ($PSHOME -replace "SysWOW64", "SysNative") powershell.exe) -File $MyInvocation.InvocationName @args

    # Exit 32-bit script.
    Exit $LastExitCode
    Write-Warning " 64-bit Windows PowerShell done."
}

if(!$PSScriptRoot){
    $PSScriptRoot = Split-Path $MyInvocation.MyCommand.Path -Parent
}


$DEPLOY_FILE= Join-Path $PSScriptRoot "build/deploy.ps1"
. $DEPLOY_FILE
check


#AppVariables Starts
# Best is to read this from CodePipeline / CodeBuild / CodeDeploy environment variable
#$stageOrEnvironment = "feature" # Options are features, dev, test, uat, prod, mvp etc.
$clientOrPod = "pod"             # Options are pod, demo, <client-code>
$applicationName = "ingestion"
$mainApplication = "foliosure"
$deploymentSourceFolderName = "FoliosureAPIIngestionTemp"

# Constant - Don't change
$DEFAULT_WEB_SITE = "default web site"
$APP_POOL_DOT_NET_VERSION = "v4.0"

# TODO: Change this path - Application specific - Begin
$WEBSITE_ROOT_PATH = "E:\inetpub\wwwroot\foliosure\"
# $env:DEPLOYMENT_GROUP_NAME="BEAT-FOLIOSURE-DEMO"

switch ($env:DEPLOYMENT_GROUP_NAME) {
    "NON-PROD-DEV" {
        $stageOrEnvironment = "feature"
    }
    "NON-PROD-TEST" {
        $stageOrEnvironment = "test"
    }
    "beat-foliosure-security-pod" {
        $stageOrEnvironment = "security"
        $clientOrPod = "pod"
    }
     "NON-PROD-TEST-POD-B" {
        $stageOrEnvironment = "test"
        $clientOrPod = "pod-b"
    }
     "NON-PROD-PERF1" {
        $stageOrEnvironment = "perf1"
    }
      "NON-PROD-PERF2" {
        $stageOrEnvironment = "perf2"
    }
    "NON-PROD-UAT" {
        $stageOrEnvironment = "uat"
        $clientOrPod = "pod-a"
    }
     "NON-PROD-UAT-POD-B" {
        $stageOrEnvironment = "uat"
        $clientOrPod = "pod-b"
    }
	"uat-taabo-foliosure" {
        $stageOrEnvironment = "uat"
		$clientOrPod = "security"
    }
    "beat-foliosure-demo" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "demo"
    }  
    "beat-foliosure-prod-demo2" {
        $stageOrEnvironment = "prod"
        $clientOrPod = "demo2"
    }
    "beat-foliosure-enshi" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "enshi"
    }
    "beat-foliosure-prod-taabo-ch" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "taabo-ch"
     }
	"beat-foliosure-prod-bristol" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "bristol"
     }
    "beat-foliosure-prod-admont" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "admont"
     }
    "beat-foliosure-prod-asmt" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "asmt"
     }
	 "beat-foliosure-prod-monmouth" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "monmouth"
     }
	 "beat-foliosure-prod-exeter" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "exeter"
     }
	  "beat-foliosure-prod-pizarro" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "pizarro"
     }
	"beat-foliosure-prod-trial" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "trial"
     }
     "beat-foliosure-prod-himera" {
      $stageOrEnvironment = "prod"
      $clientOrPod = "himera"
     }

    Default {
        $stageOrEnvironment = "feature"
    }
}

$webSiteAppPoolName = "app_pool_foliosure_" + $stageOrEnvironment + "_" + $clientOrPod
$webSiteInstancePath = $WEBSITE_ROOT_PATH + $stageOrEnvironment + "\" + $clientOrPod

switch ($stageOrEnvironment) {
    "feature" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "dev" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "test" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
            "pod-b" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
     "perf1" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
      "perf2" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "uat" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
            "pod-a" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
            "pod-b" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
			"security" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            } 
        }
    }
     "security" {
        switch ($clientOrPod) {
            "pod" {
                $webSiteName = $DEFAULT_WEB_SITE
                $webSiteBindingsPort = "80"
            }
        }
    }
    "prod" {
        switch ($clientOrPod) {
            "demo" {
                $webSiteName = "demo.beatfoliosure.com"
                $webSiteBindingsPort = "8005"
            }
            "demo2" {
                $webSiteName = "demo2.beatfoliosure.com"
                $webSiteBindingsPort = "8003"
            }
            "enshi" {
                $webSiteName = "enshi.beatfoliosure.com"
                $webSiteBindingsPort = "9017"
            }
            "taabo-ch" {
                $webSiteName = "taabo-ch.beatfoliosure.com"
                $webSiteBindingsPort = "8100"
            }
			"bristol" {
                $webSiteName = "bristol.beatfoliosure.com"
                $webSiteBindingsPort = "8107"
            }
            "admont" {
                $webSiteName = "admont.beatfoliosure.com"
                $webSiteBindingsPort = "8104"
            }
            "asmt" {
                $webSiteName = "asmt.beatfoliosure.com"
                $webSiteBindingsPort = "8106"
            }
			"monmouth" {
                $webSiteName = "monmouth.beatfoliosure.com"
                $webSiteBindingsPort = "8102"
            }
			"exeter" {
                $webSiteName = "exeter.beatfoliosure.com"
                $webSiteBindingsPort = "8103"
            }
			"pizarro" {
                $webSiteName = "pizarro.beatfoliosure.com"
                $webSiteBindingsPort = "9203"
            }
			"trial" {
                $webSiteName = "trial.beatfoliosure.com"
                $webSiteBindingsPort = "8001"
            }
            "himera" {
                $webSiteName = "himera.beatfoliosure.com"
                $webSiteBindingsPort = "8105"
            }
        }
    }
    default {
        $webSiteName = "<Not set>"
        $webSiteBindingsPort = "-1"
    }
}

$PSPath = "IIS:\Sites\$webSiteName\$mainApplication\" + $stageOrEnvironment + "\" + $clientOrPod
$VirtualPath = "$webSiteName\$mainApplication\" + $stageOrEnvironment + "\" + $clientOrPod

if ($stageOrEnvironment -eq "prod" -and $webSiteName -ne $DEFAULT_WEB_SITE) {
    $PSPath = "IIS:\Sites\$webSiteName"
    $VirtualPath = "$webSiteName"
}

function BeforeInstall() {

    Write-Host -ForegroundColor Magenta -Object "BeforeInstall.ps1 - Script execution begins here"
    Write-Host -ForegroundColor Magenta -Object ""

    if ($webSiteBindingsPort -eq -1) {
        Write-Host -ForegroundColor Red -Object "Invalid deployment settings. Review AppVariables.ps1"
        Write-Host -ForegroundColor Magenta -Object ""
        Write-Host -ForegroundColor Magenta -Object "BeforeInstall.ps1 - Script execution ends here"
        return
    }
    else{
        Write-Host -ForegroundColor Magenta -Object "port:$webSiteBindingsPort"
    }
    DeleteFolder ($webSiteInstancePath + "\" + $applicationName)
    RemoveApplicationFromServer $applicationName

    Write-Host -ForegroundColor Magenta -Object "BeforeInstall.ps1 - Script execution ends here"
}

BeforeInstall