using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class StatusTests
    {
        [Fact]
        public void Status_DefaultInitialization_ShouldHaveDefaultValues()
        {
            // Act
            var status = new Status();

            // Assert
            Assert.Equal(Guid.Empty, status.Id);
            Assert.Null(status.Name);
            Assert.Null(status.State);
            // Base entity properties
            Assert.True(status.CreatedOn <= DateTime.UtcNow);
            Assert.True(status.CreatedOn >= DateTime.UtcNow.AddMinutes(-1));
            Assert.Equal(0, status.CreatedBy);
            Assert.Null(status.ModifiedOn);
            Assert.Null(status.ModifiedBy);
            Assert.False(status.IsDeleted);
        }

        [Fact]
        public void Status_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var id = Guid.NewGuid();
            var createdOn = DateTime.UtcNow.AddDays(-1);
            var modifiedOn = DateTime.UtcNow;

            // Act
            var status = new Status
            {
                Id = id,
                Name = "Active",
                State = "Running",
                CreatedOn = createdOn,
                CreatedBy = 123,
                ModifiedOn = modifiedOn,
                ModifiedBy = 456,
                IsDeleted = true
            };

            // Assert
            Assert.Equal(id, status.Id);
            Assert.Equal("Active", status.Name);
            Assert.Equal("Running", status.State);
            Assert.Equal(createdOn, status.CreatedOn);
            Assert.Equal(123, status.CreatedBy);
            Assert.Equal(modifiedOn, status.ModifiedOn);
            Assert.Equal(456, status.ModifiedBy);
            Assert.True(status.IsDeleted);
        }

        [Fact]
        public void Status_WithEmptyGuid_ShouldAcceptEmptyGuid()
        {
            // Act
            var status = new Status { Id = Guid.Empty };

            // Assert
            Assert.Equal(Guid.Empty, status.Id);
        }

        [Fact]
        public void Status_WithEmptyName_ShouldAcceptEmptyString()
        {
            // Act
            var status = new Status { Name = string.Empty };

            // Assert
            Assert.Equal(string.Empty, status.Name);
        }

        [Fact]
        public void Status_WithEmptyState_ShouldAcceptEmptyString()
        {
            // Act
            var status = new Status { State = string.Empty };

            // Assert
            Assert.Equal(string.Empty, status.State);
        }

        [Fact]
        public void Status_WithLongName_ShouldAcceptLongString()
        {
            // Arrange
            var longName = new string('A', 1000);

            // Act
            var status = new Status { Name = longName };

            // Assert
            Assert.Equal(longName, status.Name);
        }

        [Fact]
        public void Status_WithLongState_ShouldAcceptLongString()
        {
            // Arrange
            var longState = new string('B', 500);

            // Act
            var status = new Status { State = longState };

            // Assert
            Assert.Equal(longState, status.State);
        }

        [Fact]
        public void Status_InheritsFromBaseEntity_ShouldHaveBaseEntityProperties()
        {
            // Act
            var status = new Status();

            // Assert - Verify it's a BaseEntity
            Assert.IsAssignableFrom<BaseEntity>(status);
        }
    }
}