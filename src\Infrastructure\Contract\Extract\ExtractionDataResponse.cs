using Newtonsoft.Json;
using Persistence.Models;
using System.ComponentModel.DataAnnotations;

namespace Infrastructure.Contract.Extract
{
    public class ExtractionDataResponse : BaseEntity
    {
        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;
        
        [JsonProperty("message")]
        public string? Message { get; set; }
        
        [JsonProperty("data")]
        public object? Data { get; set; }
        public bool IsSuccessStatusCode { get; set; }

        [Key] 
        public string Id { get; set; }


        [MaxLength(100)] 
        public string Name { get; set; } 
        public string ProcessID { get; set; }
        public string JobID { get; set; }
        public string CompanyId { get; set; }
        public string CompanyName { get; set; }
        public int TypeofExtraction { get; set; }
        public List<TableGroup> TableGroups { get; set; }
        public List<Persistence.Models.File> Files { get; set; }

        public string Ticker { get; set; }
        public string? TemplateId { get; set; }
        public string CurrencyUnit { get; set; }
        public string Country { get; set; }
        public string Sector { get; set; }
        public string Industry { get; set; }
    }
}