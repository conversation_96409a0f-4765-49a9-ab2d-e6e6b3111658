using Persistence.Models;
using Xunit;

namespace UnitTests.Persistence.Models
{
    public class CellTests
    {
        [Fact]
        public void Cell_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange & Act
            var cell = new Cell
            {
                ColumnKey = "Revenue",
                Value = "1000000",
                Source = "Excel",
                PdfHighlight = new PdfHighlight(),
                Comments = new List<string> { "Comment 1", "Comment 2" },
                Format = "Currency",
                Type = "Number",
                Date = "2023-12-31",
                ColumnIndex = "A1"
            };

            // Assert
            Assert.Equal("Revenue", cell.ColumnKey);
            Assert.Equal("1000000", cell.Value);
            Assert.Equal("Excel", cell.Source);
            Assert.NotNull(cell.PdfHighlight);
            Assert.Equal(2, cell.Comments.Count);
            Assert.Equal("Currency", cell.Format);
            Assert.Equal("Number", cell.Type);
            Assert.Equal("2023-12-31", cell.Date);
            Assert.Equal("A1", cell.ColumnIndex);
        }

        [Fact]
        public void Cell_WithEmptyCommentsList_ShouldHandleCorrectly()
        {
            // Arrange & Act
            var cell = new Cell
            {
                ColumnKey = "Test",
                Value = "Test Value",
                Source = "Manual",
                Comments = new List<string>()
            };

            // Assert
            Assert.NotNull(cell.Comments);
            Assert.Empty(cell.Comments);
        }

        [Fact]
        public void Cell_DefaultInitialization_ShouldHaveDefaultValues()
        {
            // Act
            var cell = new Cell();

            // Assert
            Assert.Null(cell.ColumnKey);
            Assert.Null(cell.Value);
            Assert.Null(cell.Source);
            Assert.Null(cell.PdfHighlight);
            Assert.Null(cell.Comments);
            Assert.Null(cell.Format);
            Assert.Null(cell.Type);
            Assert.Null(cell.Date);
            Assert.Null(cell.ColumnIndex);
        }

        [Fact]
        public void Cell_WithNullPdfHighlight_ShouldAcceptNull()
        {
            // Arrange & Act
            var cell = new Cell
            {
                ColumnKey = "Test",
                Value = "Test Value",
                Source = "Manual",
                PdfHighlight = null
            };

            // Assert
            Assert.Null(cell.PdfHighlight);
        }

        [Fact]
        public void Cell_WithLargeCommentsList_ShouldHandleCorrectly()
        {
            // Arrange & Act
            var comments = Enumerable.Range(1, 100).Select(i => $"Comment {i}").ToList();
            var cell = new Cell
            {
                ColumnKey = "Test",
                Value = "Test Value",
                Source = "Manual",
                Comments = comments
            };

            // Assert
            Assert.Equal(100, cell.Comments.Count);
            Assert.Equal("Comment 1", cell.Comments.First());
            Assert.Equal("Comment 100", cell.Comments.Last());
        }
    }
}