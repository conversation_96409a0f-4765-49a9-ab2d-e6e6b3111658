version: 0.2

env:
  variables:
    DOTNET_SDK_VERSION: "8.0.100"  
    
phases:
    install:
        commands:
        - /usr/local/bin/dotnet-install.sh -v $DOTNET_SDK_VERSION
        - dotnet --list-sdks
        - test -f "global.json" && echo "Using provided global.json" || dotnet new globaljson --sdk-version $DOTNET_SDK_VERSION
    pre_build:
        commands:
        - aws s3 sync s3://beat-foliosure-vault/Configuration/IngestionConfiguration/ ./src/Ingestion.API/
        - export PATH="$PATH:/root/.dotnet/tools"
        - dotnet tool install --global dotnet-sonarscanner --version 9.0.0
    
    build:
        commands:
        - dotnet build
        - sh sonar.sh
            
    post_build:
        commands:
        - dotnet publish "./src/Ingestion.API/Ingestion.API.csproj" -p:PlaywrightPlatform=all -c Release --framework net8.0 -r win-x64 --self-contained True -o "./publish"  
artifacts:
    files:
        - ./publish/**/*
        - ./build/deploy.ps1
        - ./appspec.yml
        - ./AfterInstall.ps1
        - ./BeforeInstall.ps1
