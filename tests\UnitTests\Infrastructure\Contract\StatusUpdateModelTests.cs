using Infrastructure.Contract;
using Xunit;

namespace UnitTests.Infrastructure.Contract
{
    public class StatusUpdateModelTests
    {
        [Fact]
        public void StatusUpdateModel_DefaultValues_ShouldBeEmptyStrings()
        {
            // Act
            var statusUpdateModel = new StatusUpdateModel();

            // Assert
            Assert.Equal(string.Empty, statusUpdateModel.ProcessId);
            Assert.Equal(string.Empty, statusUpdateModel.Status);
        }

        [Fact]
        public void StatusUpdateModel_PropertiesInitialization_ShouldSetValuesCorrectly()
        {
            // Arrange
            var testProcessId = Guid.NewGuid().ToString();
            var testStatus = "In Progress";

            // Act
            var statusUpdateModel = new StatusUpdateModel
            {
                ProcessId = testProcessId,
                Status = testStatus
            };

            // Assert
            Assert.Equal(testProcessId, statusUpdateModel.ProcessId);
            Assert.Equal(testStatus, statusUpdateModel.Status);
        }

        [Fact]
        public void StatusUpdateModel_WithNullValues_ShouldAcceptNullAssignment()
        {
            // Act
            var statusUpdateModel = new StatusUpdateModel
            {
                ProcessId = null!,
                Status = null!
            };

            // Assert
            Assert.Null(statusUpdateModel.ProcessId);
            Assert.Null(statusUpdateModel.Status);
        }
    }
}