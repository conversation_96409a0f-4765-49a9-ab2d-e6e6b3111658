﻿using AWSS3.Interfaces;
using DataIngestionService.Constants;
using DataIngestionService.Helpers;
using DataIngestionService.IServices;
using Infrastructure.Contract.Extract;
using Infrastructure.DTOS.Master;
using Infrastructure.DTOS.NonControllerDto;
using Infrastructure.Extentions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Persistence.Models;
using Persistence.Models.Classifier;
using Persistence.MongoDb;
using RestSharp;

namespace DataIngestionService.Services
{
    public class ExtractDocumentService : IExtract
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<ExtractDocumentService> _logger;
        private readonly string _baseUrl;
        private readonly IFinancialsRepository _financialsRepository;
        private readonly IRepository<Classifier> _classifierRepository;
        private readonly IAWSS3Library _awsS3Library;
        private readonly ISpecificKpiTransformationService _specificKpi;
        public ExtractDocumentService(IConfiguration configuration, ILogger<ExtractDocumentService> logger,IFinancialsRepository financialsRepository,
             IRepository<Classifier> classifierRepository, IAWSS3Library awsS3Library, ISpecificKpiTransformationService specificKpi)
        {
            _configuration = configuration;
            _logger = logger;
            _baseUrl = _configuration[ApiConstants.API_SETTINGS_BASE_URL_KEY]??string.Empty;
            _financialsRepository = financialsRepository;
            _classifierRepository = classifierRepository;
            _awsS3Library = awsS3Library;
            _specificKpi = specificKpi;
        }

        protected virtual async Task<RestResponse> ProcessEndpoint(string url, Method method, string token, CancellationToken cancellationToken, object? body = null)
        {
            RestClient restClient = new();
            RestRequest request = new(url, method);
            _logger.LogError($"@@@@api base url{_baseUrl}");
            _ = request.AddHeader(ApiConstants.AUTHORIZATION_HEADER, $"{ApiConstants.BEARER_PREFIX} {token}");
            _ = request.AddHeader(ApiConstants.ACCEPT_HEADER, ApiConstants.ACCEPT_ALL);
            _ = request.AddHeader(ApiConstants.CONTENT_TYPE_HEADER, ApiConstants.APPLICATION_JSON);
            _ = request.AddHeader(ApiConstants.APP_KEY_NAME,  ApiConstants.APP_Name);
            if (body != null)
            {
                _ = request.AddJsonBody(body);
            }

            var response = await restClient.ExecuteAsync(request, cancellationToken);
            
            if (!response.IsSuccessful)
            {
                _logger.LogError("Error processing endpoint {Url}. Status: {Status}, Response: {Content}", url, response.StatusCode, response.Content);
            }

            return response;
        }
        public async Task<JobStatusResponse> CheckJobStatusAsync(Guid jobId, string token, CancellationToken cancellationToken = default)
        {
            try
            {
                string url = $"{_baseUrl}{string.Format(ApiConstants.JOB_STATUS_ENDPOINT_FORMAT, jobId)}";
                var response = await ProcessEndpoint(url, Method.Get, token, cancellationToken);
                
                var result = JsonConvert.DeserializeObject<JobStatusResponse>(response.Content!) 
                    ?? ResponseFactory.CreateJobStatusResponse(jobId, "Failed to deserialize job status response");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CheckJobStatusAsync: {Message}", ex.Message);
                return ResponseFactory.CreateJobStatusResponse(jobId, string.Format(ApiConstants.CHECK_JOB_STATUS_EXCEPTION_FORMAT, ex.Message));
            }
        }
        public async Task<ExtractionDataResponse> FetchExtractionDataAsync(Guid jobId,Guid processId, string token, CancellationToken cancellationToken = default)
        {
            try
            {
                string url = $"{_baseUrl}{string.Format(ApiConstants.EXTRACTION_DATA_ENDPOINT_FORMAT, jobId)}";
                var response = await ProcessEndpoint(url, Method.Get, token, cancellationToken);
                JsonSerializerSettings settings = new()
                {
                    DateFormatString = "dd-MM-yyyy"
                };
                DSFinancialsDto? financial = JsonConvert.DeserializeObject<DSFinancialsDto>(response.Content!, settings);
                if (financial?.output_mode == "s3")
                {
                    var s3FileContent = await _awsS3Library.GetFileContentAsTextAsync(financial.output_s3_path);
                    financial = JsonConvert.DeserializeObject<DSFinancialsDto>(s3FileContent.ToString(), settings);
                }
                FinancialsData data = financial.ToFinancialsData();
                data.Id = data.Id.Replace("spread_", "");

                var result = JsonConvert.DeserializeObject<ExtractionDataResponse>(JsonConvert.SerializeObject(data))
                    ?? ResponseFactory.CreateExtractionDataResponse("Failed to deserialize extraction data response");
                result.IsSuccessStatusCode = response.IsSuccessStatusCode;

                Financials Financials = JsonConvert.DeserializeObject<Financials>(JsonConvert.SerializeObject(data));
                Financials.JobID = jobId;
                Financials.ProcessID = processId;
                await _financialsRepository.AddFinancialsAsync(Financials);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in FetchExtractionDataAsync: {Message}", ex.Message);
                return ResponseFactory.CreateExtractionDataResponse(string.Format(ApiConstants.FETCH_EXTRACTION_DATA_EXCEPTION_FORMAT, ex.Message));
            }
        }
        public async Task<ExtractionDataResponse> FetchSpecificExtractionData(Guid jobId, Guid processId, string token, CancellationToken cancellationToken = default)
        {
            try
            {
                string url = $"{_baseUrl}{string.Format(ApiConstants.SPECIFIC_EXTRACTION_DATA, jobId)}";
                var response = await ProcessEndpoint(url, Method.Get, token, cancellationToken); 
                string content = response.Content ?? string.Empty;
                SpecificDto specificDto= await _specificKpi.TransformDsSpecificToSpecific(content, processId);
                var result = new ExtractionDataResponse
                {
                    IsSuccessStatusCode = response.IsSuccessStatusCode,
                    CompanyName=specificDto.CompanyName,
                    CompanyId=specificDto.CompanyId
                };
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in FetchSpecificExtractionData: {Message}", ex.Message);
                return ResponseFactory.CreateExtractionDataResponse(string.Format(ApiConstants.SPECIFIC_EXTRACTION_DATA, ex.Message));
            }
        }
        private async Task<Dictionary<string, Classifier>> FetchExistingClassifier(Guid processId)
        {
            var existingPages = await _classifierRepository.GetAllAsync();
            return existingPages
            .OfType<Classifier>()
                .Where(p => p.ProcessId == processId&&!p.IsDeleted)
                .ToDictionary(p => p.Id);
        }

        public async Task<Classifier> GetClassifierDataByProcessIdAsync(Guid processId)
        {
            var existingClassifiers = await FetchExistingClassifier(processId);
            if (existingClassifiers.Any())
            {
                var classifier = await _classifierRepository.GetByIdAsync(existingClassifiers.First().Value.Id);
                return classifier!;
            }
            return null!;
        }

        public async Task<string> AddOrUpdateClassifierData(TableSuggestionResponse suggestionResponse, Guid ProcessId,int userId)
        {
            var existingClassifiers = await FetchExistingClassifier(ProcessId);            
            if (existingClassifiers.Any())
            {
                var existingClassifier = existingClassifiers.First().Value;
                existingClassifier.IsDeleted = false;
                existingClassifier.ModifiedOn = DateTime.UtcNow;
                existingClassifier.ModifiedBy = userId;
                existingClassifier.TableSuggestionResponse = suggestionResponse;

                _logger.LogInformation("Updating existing classifier data with ProcessId: {ProcessId}", ProcessId);
                await _classifierRepository.UpdateAsync(existingClassifier.Id, existingClassifier);
                return existingClassifier.Id;
            }
            else
            {
                Classifier classifier = new()
                {
                    ProcessId = ProcessId,
                    TableSuggestionResponse = suggestionResponse,
                    CreatedBy= userId,
                    CreatedOn = DateTime.UtcNow,
                    IsDeleted = false,
                };
                _logger.LogInformation("Creating new classifier data with ProcessId: {ProcessId}", ProcessId);
                await _classifierRepository.CreateAsync(classifier);
                return classifier.Id;
            }
        }

        public async Task<bool> DeleteClassifierDataByProcessIdAsync(Guid processId,int userId)
        {
            try
            {
                var classifiers = await _classifierRepository.GetAllAsync(x => x.ProcessId== processId && !x.IsDeleted);
                if (!classifiers.Any())
                {
                    return false;
                }
                foreach (var classifier in classifiers)
                {
                    classifier.IsDeleted = true;
                    classifier.ModifiedOn = DateTime.UtcNow;
                    classifier.ModifiedBy = userId;
                    _logger.LogInformation("Deleting classifier data with ID: {ClassifierId} for ProcessId: {ProcessId}", classifier.Id, processId);
                    await _classifierRepository.UpdateAsync(classifier.Id, classifier);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in DeleteClassifierDataByProcessIdAsync: {Message}", ex.Message);
                return false;
            }
        }
        public async Task<bool> UpdateFinancials(FinancialsData data,Guid processId)
        {
            try
            {
                Financials? Financials = JsonConvert.DeserializeObject<Financials>(JsonConvert.SerializeObject(data));
                return await _financialsRepository.UpdateFinancialsByProcessIdAsync(processId, Financials);
            }catch(Exception ex)
            {
                _logger.LogError(ex, "Exception in UpdateFinancials: {Message}", ex.Message);
                return false;
            }
        }
    }
}
