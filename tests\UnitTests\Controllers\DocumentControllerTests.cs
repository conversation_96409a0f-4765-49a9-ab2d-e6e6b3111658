using API.Controllers;
using Contract;
using DataIngestionService;
using Infrastructure.Contract;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using System.Security.Claims;
using Xunit;

namespace UnitTests.Controllers
{
    public class DocumentControllerTests
    {
        private readonly Mock<ILogger<DocumentController>> _mockLogger;
        private readonly Mock<IUpload> _mockUploadService;
        private readonly Mock<IHelperService> _mockHelperService;
        private readonly DocumentController _controller;

        public DocumentControllerTests()
        {
            _mockLogger = new Mock<ILogger<DocumentController>>();
            _mockUploadService = new Mock<IUpload>();
            _mockHelperService = new Mock<IHelperService>();

            _controller = new DocumentController(
                _mockLogger.Object,
                _mockUploadService.Object,
                _mockHelperService.Object
            );

            var user = new ClaimsPrincipal(new ClaimsIdentity(new Claim[]
            {
                new Claim(ClaimTypes.NameIdentifier, "1")
            }, "mock"));

            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = new DefaultHttpContext { User = user }
            };
        }

        [Fact]
        public async Task Upload_ValidRequest_ReturnsOk()
        {

            var uploadDto = new UploadDto
            {
                ExtractionType = "SampleType" // Set required property
            };
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).ReturnsAsync(1);
            _mockUploadService.Setup(u => u.UploadFile(uploadDto, 1)).ReturnsAsync(new List<UploadResponseDto>());


            var result = await _controller.Upload(uploadDto);


            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.IsType<List<UploadResponseDto>>(okResult.Value);
        }

        [Fact]
        public async Task GetSpreadDetails_ValidRequest_ReturnsOk()
        {

            _mockUploadService.Setup(u => u.GetUploadedDocumentDetails(10, 0)).ReturnsAsync(new List<DocumentDetailDto>());


            var result = await _controller.GetSpreadDetails();


            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.IsType<List<DocumentDetailDto>>(okResult.Value);
        }

        [Fact]
        public async Task GetProcessDetails_ValidRequest_ReturnsOk()
        {

            var processId = Guid.NewGuid();
            _mockUploadService.Setup(u => u.GetProcessDetailsById(processId)).ReturnsAsync(new ProcessDetailsDto());


            var result = await _controller.GetProcessDetails(processId);


            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.IsType<ProcessDetailsDto>(okResult.Value);
        }
        [Fact]
        public async Task Upload_InvalidModelState_ReturnsBadRequest()
        {
            var uploadDto = new UploadDto() { ExtractionType = "" };
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.Upload(uploadDto);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsAssignableFrom<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            var errorMessages = errors["key"] as string[];
            Assert.NotNull(errorMessages);
            Assert.Contains("error", errorMessages);
        }

        [Fact]
        public async Task UpdateDocumentCollectionInformation_InvalidModelState_ReturnsBadRequest()
        {
            var docs = new List<FileConfigurationDetails>();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.UpdateDocumentCollectionInformation(docs);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsAssignableFrom<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            var errorMessages = errors["key"] as string[];
            Assert.NotNull(errorMessages);
            Assert.Contains("error", errorMessages);
        }

        [Fact]
        public async Task GetSpreadDetails_InvalidModelState_ReturnsBadRequest()
        {
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.GetSpreadDetails();

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsAssignableFrom<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            var errorMessages = errors["key"] as string[];
            Assert.NotNull(errorMessages);
            Assert.Contains("error", errorMessages);
        }
        [Fact]
        public async Task GetProcessDetails_InvalidModelState_ReturnsBadRequest()
        {
            var processId = Guid.NewGuid();
            _controller.ModelState.AddModelError("key", "error");

            var result = await _controller.GetProcessDetails(processId);

            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var errors = Assert.IsAssignableFrom<SerializableError>(badRequestResult.Value);
            Assert.True(errors.ContainsKey("key"));
            var errorMessages = errors["key"] as string[];
            Assert.NotNull(errorMessages);
            Assert.Contains("error", errorMessages);
        }
        [Fact]
        public async Task UpdateDocumentCollectionInformation_ValidRequest_ReturnsOk()
        {
            var docs = new List<FileConfigurationDetails>();
            _mockHelperService.Setup(h => h.GetCurrentUserId(It.IsAny<ClaimsPrincipal>())).ReturnsAsync(1);
            _mockUploadService.Setup(u => u.UpdateDocumentConfigurations(docs, 1)).ReturnsAsync(true);

            var result = await _controller.UpdateDocumentCollectionInformation(docs);

            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.True((bool)okResult.Value);
        }

        [Fact]
        public async Task GetDocumentProcessingStatusCount_ReturnsOk()
        {
            var statusCount = new StatusCount { CompletedCount = 1, FailedCount = 0, InProgressCount = 0 };
            _mockUploadService.Setup(u => u.GetDocumentProcessingStatusCount()).ReturnsAsync(statusCount);

            var result = await _controller.GetDocumentProcessingStatusCount();

            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            Assert.Equal(statusCount, okResult.Value);
        }
    }
}