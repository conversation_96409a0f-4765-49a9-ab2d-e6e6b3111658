using System.Text.Json.Serialization;

namespace Infrastructure.DTOS.NonControllerDto
{
    public class SpecificDto
    {
        [JsonPropertyName("job_id")]
        public string JobId { get; set; }

        [JsonPropertyName("company_id")]
        public string CompanyId { get; set; }

        [JsonPropertyName("company_name")]
        public string CompanyName { get; set; }
        [JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; } = string.Empty;
        [JsonPropertyName("unit")]
        public string Unit { get; set; } = string.Empty;

        [JsonPropertyName("files")]
        public List<SpecificFileDto> Files { get; set; } = [];
        [JsonPropertyName("customSections")]
        public List<CustomSectionDto> CustomSections { get; set; } = new();
    }

    public class KpiOptionDto
    {
        [JsonPropertyName("text")]
        public string Text { get; set; }

        [JsonPropertyName("value")]
        public string Value { get; set; }

        [JsonPropertyName("kpiInfo")]
        public string KpiInfo { get; set; }

        [JsonPropertyName("kpiId")]
        public int KpiId { get; set; }
        [JsonPropertyName("methodologyId")]
        public int MethodologyId { get; set; } = 0;
    }

    public class PeriodConfigurationDto
    {
        [JsonPropertyName("periodId")]
        public string PeriodId { get; set; }

        [JsonPropertyName("label")]
        public string Label { get; set; }

        [JsonPropertyName("columns")]
        public int Columns { get; set; }

        [JsonPropertyName("columnIndex")]
        public int ColumnIndex { get; set; }

        [JsonPropertyName("selectedKpis")]
        public List<SelectedKpiDto> SelectedKpis { get; set; } = [];

        [JsonPropertyName("documentKpis")]
        public List<SelectedKpiDto> DocumentKpis { get; set; } = [];
    }

    public class SelectedKpiDto
    {
        [JsonPropertyName("id")]
        public int Id { get; set; } = 0;
        [JsonPropertyName("kpiId")]
        public int KpiId { get; set; }
        [JsonPropertyName("text")]
        public string Text { get; set; }
        [JsonPropertyName("value")]
        public string Value { get; set; }
        [JsonPropertyName("periodId")]
        public string PeriodId { get; set; }
        [JsonPropertyName("kpiInfo")]
        public string KpiInfo { get; set; }
        [JsonPropertyName("mappingName")]
        public string MappingName { get; set; }
        [JsonPropertyName("mappingId")]
        public string MappingId { get; set; }
        [JsonPropertyName("methodologyId")]
        public int MethodologyId { get; set; } = 0;
    }

    public class DataRowDto
    {
        [JsonPropertyName("label")]
        public string Label { get; set; }
        [JsonPropertyName("labelType")]
        public string LabelType { get; set; }
        [JsonPropertyName("rowId")]
        public string RowId { get; set; }
        [JsonPropertyName("companyId")]
        public string CompanyId { get; set; }
        [JsonPropertyName("companyName")]
        public string CompanyName { get; set; }
        [JsonPropertyName("fundId")]
        public string FundId { get; set; } = string.Empty;
        [JsonPropertyName("selected")]
        public bool Selected { get; set; } = false;
        [JsonPropertyName("currencyCode")]
        public string CurrencyCode { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;

        [JsonPropertyName("values")]
        public Dictionary<string, KpiValueDto> Values { get; set; } = new Dictionary<string, KpiValueDto>();
    }

    public class KpiValueDto
    {
        [JsonPropertyName("value")]
        public string Value { get; set; }
        [JsonPropertyName("kpiInfo")]
        public string KpiInfo { get; set; }
        [JsonPropertyName("unit")]
        public string Unit { get; set; }
        [JsonPropertyName("pdfHighlight")]
        public PdfHighlightDto PdfHighlight { get; set; }
        [JsonPropertyName("excelHighlight")]
        public ExcelHighlightDto ExcelHighlight { get; set; }
        [JsonPropertyName("source")]
        public string Source { get; set; }
        [JsonPropertyName("fileType")]
        public string FileType { get; set; }
        [JsonPropertyName("unit_scale")]
        public string UnitScale { get; set; }
        [JsonPropertyName("confidence_score")]
        public double ConfidenceScore { get; set; }
        [JsonPropertyName("pageNumber")]
        public int PageNumber { get; set; }
    }

    public class PdfHighlightDto
    {
        [JsonPropertyName("pageNumber")]
        public int PageNumber { get; set; }

        [JsonPropertyName("bounds")]
        public List<double> Bounds { get; set; } = [];
        [JsonPropertyName("text")]
        public string Text { get; set; }
        [JsonPropertyName("pageHeight")]
        public int PageHeight { get; set; }
        [JsonPropertyName("pageWidth")]
        public int PageWidth { get; set; }
    }
    public class ExcelHighlightDto
    {
        [JsonPropertyName("sheet")]
        public string Sheet { get; set; }
        [JsonPropertyName("reference")]
        public string Reference { get; set; }
    }
    public class SpecificFileDto
    {
        [JsonPropertyName("file_name")]
        public string FileName { get; set; }
        [JsonPropertyName("s3_path")]
        public string S3Path { get; set; }
    }
    public class CustomSectionDto
    {
        [JsonPropertyName("sectionType")]
        public string SectionType { get; set; } = string.Empty;
        [JsonPropertyName("periods")]
        public List<PeriodConfigurationDto> Periods { get; set; } = new();
        [JsonPropertyName("data")]
        public List<DataRowDto> Data { get; set; } = new();
        [JsonPropertyName("options")]
        public List<KpiOptionDto> Options { get; set; } = new();
    }

}