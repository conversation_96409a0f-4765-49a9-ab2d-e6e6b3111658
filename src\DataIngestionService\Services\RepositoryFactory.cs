﻿using DataIngestionService.IServices;
using Microsoft.Extensions.Options;
using Persistence.MongoDb;
using System.Collections.Concurrent;

namespace DataIngestionService.Services
{
    public class RepositoryFactory : IRepositoryFactory
    {
        private readonly IOptions<MongoDbSettings> _settings;
        private readonly ConcurrentDictionary<Type, object> _repositories;
        public RepositoryFactory(IOptions<MongoDbSettings> settings)
        {
            _settings = settings;
            _repositories = new ConcurrentDictionary<Type, object>();
        }
        public IRepository<T> GetRepository<T>() where T : class
        {
            var type = typeof(T);
            return (IRepository<T>)_repositories.GetOrAdd(type, t => new Repository<T>(_settings));
        }
    }
}
