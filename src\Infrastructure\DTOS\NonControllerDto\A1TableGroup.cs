﻿using System.Collections.Generic;

/// <summary>
///
/// </summary>
namespace Infrastructure.DTOS.NonControllerDto
{
    public class A1TableGroup
    {
        public string Label { get; set; } //Income Statment/ Cash Flow/ Balance Sheet
        public List<A1Table> Tables { get; set; } //I am considering that the notes will have multiple tables and others (Income statetement,Cash flow, balance sheet) will always have single table.
    }
}