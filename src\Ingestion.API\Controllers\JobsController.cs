﻿using DataIngestionService.Constants;
using DataIngestionService.IServices;
using Infrastructure.Contract;
using Infrastructure.Contract.Extract;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Persistence.MongoDb;

namespace API.Controllers
{
    [Route("api")]
    [ApiController]
    [Authorize(Policy = JwtBearerDefaults.AuthenticationScheme)]
    public class JobsController(IJobsUpdateService jobsUpdate, ILogger<JobsController> logger, IHelperService helperService) : ControllerBase
    {
        private readonly ILogger<JobsController> _logger = logger;
        private readonly IJobsUpdateService _jobsUpdate = jobsUpdate;
        private readonly IHelperService _helperService = helperService;

        [HttpPost("job")]
        public async Task<ActionResult<ExtractResponse>> CreateJob(CreateJobs request)
        {
            _logger.LogInformation("Creating new job with request: {@Request}", request);
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Invalid model state for job creation");
                return BadRequest(ModelState);
            }
            int userId = await _helperService.GetCurrentUserId(User);
            _logger.LogInformation("Creating job for user: {UserId}", userId);
            var response = await _jobsUpdate.CreateJob(request, userId);
            _logger.LogInformation("Job created successfully with response: {@Response}", response);
            return Ok(response);
        }

        [HttpGet("status")]
        public async Task<ActionResult> GetStatus()
        {
            _logger.LogInformation("Getting job status");
            var response = await _jobsUpdate.GetStatus();
            _logger.LogInformation("Retrieved job status: {@Response}", response);
            return Ok(response);
        }

        [HttpPost("status-update")]
        public async Task<ActionResult> StatusUpdate(StatusUpdateModel statusUpdateModel)
        {
            _logger.LogInformation("Updating job status with model: {@StatusUpdateModel}", statusUpdateModel);
            var response = await _jobsUpdate.UpdateJobStatus(statusUpdateModel);
            _logger.LogInformation("Job status updated successfully: {@Response}", response);
            return Ok(response);
        }
    }
}
