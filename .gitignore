﻿################################################################################
# This .gitignore file was automatically created by Microsoft(R) Visual Studio.
################################################################################

/.vs/beat-foliosure-dataingestion
/src/API/obj
/src/DataIngestionService/obj
/src/Infrastructure/obj
/src/API/bin/Debug/net8.0
/src/DataIngestionService/bin/Debug/net8.0
/src/Infrastructure/bin/Debug/net8.0
/.vs
/src/Persistence/bin/Debug/net8.0
/src/AWSS3/bin/Debug/net8.0
/src/AWSS3/obj
/src/Persistence/obj
/src/API/Logs
/src/Ingestion.API/bin/Debug/net8.0
/src/Ingestion.API/obj
/src/Ingestion.API/Logs
/src/DataIngestionService/bin/Release/net8.0
/src/Infrastructure/bin/Release/net8.0
/src/Ingestion.API/bin/Release/net8.0
/src/Persistence/bin/Release/net8.0
/src/AWSS3/bin/Release/net8.0
/tests/UnitTests/obj
/tests/UnitTests/bin
/tests/UnitTests/TestResults
.sonarqube
src/AWSS3/bin
src/DataIngestionService/bin
src/Infrastructure/bin
src/Ingestion.API/bin
src/Persistence/bin
/TestResults
/src/Notification/obj
/src/Notification/bin/Debug/net8.0

# Exclude SonarQube files
sonar.ps1
sonar.sh
/src/Notification/bin/Release/net8.0
/tools
