﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AWSSDK.S3" Version="3.7.413" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
	</ItemGroup>
</Project>
