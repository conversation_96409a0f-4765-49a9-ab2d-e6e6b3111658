using Infrastructure.Contract;
using Infrastructure.DTOS.NonControllerDto;

namespace Infrastructure.Extentions
{
    public static class SpecificTransformations
    {
        public static SpecificDto ToSpecificDto(this DsSpecificDto dsSpecificDto, string fundId)
        {
            if (dsSpecificDto == null)
                return null;

            var specificDto = InitializeSpecificDto(dsSpecificDto);
            // Group KPIs by their type
            var groupedKpis = GroupKpisByType(dsSpecificDto);
          
            // Add dynamic extracted sections for all other types
            var customSections = new List<CustomSectionDto>();
            foreach (var group in groupedKpis)
            {
                var periods = new Dictionary<string, PeriodConfigurationDto>();
                var data = new List<DataRowDto>();
                ProcessConsolidatedData(group.ToList(), periods, data, fundId, dsSpecificDto.CompanyName);
                if (!customSections.Any(cs => cs.SectionType.Equals(group.Key, StringComparison.OrdinalIgnoreCase)))
                {
                    customSections.Add(new CustomSectionDto
                    {
                        SectionType = group.Key,
                        Periods = periods.Values.OrderBy(p => p.ColumnIndex).ToList(),
                        Data = data,
                    });
                }
            }
            specificDto.CustomSections = customSections;

            return specificDto;
        }
        private static IEnumerable<IGrouping<string, DsSpecificKpiGroupDto>> GroupKpisByType(DsSpecificDto dsSpecificDto)
        {
            return dsSpecificDto.KpiGroups
                .Where(kg => !string.IsNullOrEmpty(kg.KpiType))
                .GroupBy(kg => kg.KpiType);
        }
        private static void ProcessConsolidatedData(List<DsSpecificKpiGroupDto> kpiGroups, Dictionary<string, PeriodConfigurationDto> periods,
            List<DataRowDto> data, string fundId = null,string fundName=null)
        {
            int columnIndexCounter = periods.Count > 0 ? periods.Values.Max(p => p.ColumnIndex) : 0; // Start from the max ColumnIndex or 0
                                                                                                     // Group by CompanyId
            var groupedByCompany = kpiGroups.GroupBy(k => k.CompanyId);
            var result = new Dictionary<string, CompanyUnitInfo>();
            foreach (var companyGroup in groupedByCompany)
            {
                // Create a single DataRowDto for the company
                var firstKpiGroup = companyGroup.First();
                var dataRow = CreateDataRowDto(companyGroup.First(), fundId, fundName);
                GetCompanyUnitInfo(result, companyGroup);
                AssignUnitAndCurrencyIfExists(result, firstKpiGroup.CompanyId, dataRow);
                foreach (var kpiGroup in companyGroup)
                {
                    if (!kpiGroup.Values.Any())
                        kpiGroup.Values = PdfExcelHighlightExtensions.DsSpecificValueDtoEmpty();
                    foreach (var value in kpiGroup.Values)
                    {
                        var periodLabel = value.Column;
                        var periodId = Guid.NewGuid().ToString();
                        if (!periods.ContainsKey(periodLabel))
                        {
                            periods[periodLabel] = new PeriodConfigurationDto
                            {
                                PeriodId = periodId,
                                Label = periodLabel,
                                ColumnIndex = ++columnIndexCounter
                            };
                        }
                        else
                        {
                            periodId = periods[periodLabel].PeriodId;
                        }

                        periods.AddDocumentKpiToPeriod(periodLabel, kpiGroup);

                        var kpiId = kpiGroup.KpiId;
                        var valueKey = $"{periodId}_{kpiId}";

                        var kpiValue = value.ToKpiValueDto();
                        kpiValue.PdfHighlight = string.IsNullOrEmpty(value.FileType) ? PdfExcelHighlightExtensions.GetEmptyPdfHighlights() : value.Pdf.ToPdfHighlightDto(value.Value);
                        kpiValue.ExcelHighlight = string.IsNullOrEmpty(value.FileType) ? PdfExcelHighlightExtensions.GetEmptyExcelHighlights() : value.Excel.ToExcelHighlightDto();

                        dataRow.Values[valueKey] = kpiValue;
                    }
                }
                if (dataRow.Values.Count > 0)
                    data.Add(dataRow);
            }
        }
        public static void AddDocumentKpiToPeriod(this Dictionary<string, PeriodConfigurationDto> periods, string periodLabel, DsSpecificKpiGroupDto kpiGroup)
        {
            if (!periods.ContainsKey(periodLabel))
                return;
            var period = periods[periodLabel];
            // Check if the KPI ID already exists in DocumentKpis
            if (period.DocumentKpis.Any(dk => dk.KpiId == kpiGroup.KpiId))
                return;
            var documentKpi = new SelectedKpiDto
            {
                Text = kpiGroup.KpiName,
                Value = kpiGroup.KpiName,
                MappingName = kpiGroup.MappingName,
                MappingId = kpiGroup.MappingId,
                KpiId = kpiGroup.KpiId,
                PeriodId = period.PeriodId,
                Id = kpiGroup.Id,
            };

            period.DocumentKpis.Add(documentKpi);
        }

        public static string DetermineKpiInfo(this string unitType)
        {
            return unitType?.ToLower() switch
            {
                "usd" => "$",
                "percentage" => "%",
                "number" => "#",
                "multiple" => "x",
                "text" => "Text",
                _ => unitType
            };
        }
        public static SpecificDto InitializeSpecificDto(DsSpecificDto dsSpecificDto)
        {
            return new SpecificDto
            {
                CustomSections = [],
                Files = dsSpecificDto.Files.Select(f => f.FileDtoTransformation()).ToList() ?? [],
                JobId = dsSpecificDto.JobId,
                CompanyId = dsSpecificDto.CompanyId,
                CompanyName = dsSpecificDto.CompanyName
            };
        }
        public static void GetCompanyUnitInfo(Dictionary<string, CompanyUnitInfo> result, IGrouping<string, DsSpecificKpiGroupDto> group)
        {
            var unitScales = group
                    .SelectMany(k => k.Values ?? new List<DsSpecificValueDto>())
                    .Select(v => v.UnitScale)
                    .Where(u => !string.IsNullOrEmpty(u) && !u.Equals("N/A", StringComparison.OrdinalIgnoreCase))
                    .Distinct()
                    .ToList();

            var currencySymbols = group
                .SelectMany(k => k.Values ?? new List<DsSpecificValueDto>())
                .Select(v => v.CurrencySymbol)
                .Where(c => !string.IsNullOrEmpty(c) && !c.Equals("N/A", StringComparison.OrdinalIgnoreCase))
                .Distinct()
                .ToList();

            result[group.Key] = new CompanyUnitInfo
            {
                UnitScales = unitScales,
                CurrencySymbols = currencySymbols
            };
        }
        private static DataRowDto CreateDataRowDto(DsSpecificKpiGroupDto firstKpiGroup, string fundId,string fundName)
        {
            return new DataRowDto
            {
                Label = !string.IsNullOrEmpty(firstKpiGroup.CompanyName)? firstKpiGroup.CompanyName:fundName,
                LabelType = firstKpiGroup.LabelType,
                RowId = Guid.NewGuid().ToString(),
                CompanyId = firstKpiGroup.CompanyId,
                CompanyName = firstKpiGroup.CompanyName,
                FundId = fundId,
                CurrencyCode = string.Empty,
                Selected = false,
                Values = new Dictionary<string, KpiValueDto>()
            };
        }
        private static void AssignUnitAndCurrency(DataRowDto dataRow, CompanyUnitInfo companyUnitInfo)
        {
            dataRow.Unit = companyUnitInfo?.UnitScales?.FirstOrDefault() ?? string.Empty;
            dataRow.CurrencyCode = companyUnitInfo?.CurrencySymbols?.FirstOrDefault() ?? string.Empty;
        }
        private static void AssignUnitAndCurrencyIfExists(
    Dictionary<string, CompanyUnitInfo> result,
    string companyId,
    DataRowDto dataRow)
        {
            if (result.TryGetValue(companyId, out var companyUnitInfo))
            {
                AssignUnitAndCurrency(dataRow, companyUnitInfo);
            }
        }
    }

    public static class PdfExcelHighlightExtensions
    {
        public static PdfHighlightDto ToPdfHighlightDto(this object pdfObj, string value)
        {
            if (pdfObj == null) return GetEmptyPdfHighlights();
            // Try to cast to expected type (dynamic fallback)
            dynamic pdf = pdfObj;
            return new PdfHighlightDto
            {
                PageNumber = pdf.PageNumber,
                Bounds = new List<double>
                {
                    pdf.Bbox.X1,
                    pdf.Bbox.Y1,
                    pdf.Bbox.X2,
                    pdf.Bbox.Y2
                },
                Text = value
            };
        }
        public static ExcelHighlightDto ToExcelHighlightDto(this object excelObj)
        {
            if (excelObj == null) return GetEmptyExcelHighlights();
            dynamic excel = excelObj;
            return new ExcelHighlightDto
            {
                Sheet = excel.Sheet,
                Reference = excel.Reference
            };
        }

        public static PdfHighlightDto GetEmptyPdfHighlights()
        {
            return new PdfHighlightDto();
        }

        public static ExcelHighlightDto GetEmptyExcelHighlights()
        {
            return new ExcelHighlightDto();
        }
        public static KpiValueDto GetEmptyKpiValueDto()
        {
            return new KpiValueDto
            {
                Value = string.Empty,
                KpiInfo = string.Empty,
                Unit = string.Empty,
                PdfHighlight = GetEmptyPdfHighlights(),
                ExcelHighlight = GetEmptyExcelHighlights(),
                Source = string.Empty,
                FileType = string.Empty,
                UnitScale = string.Empty,
                PageNumber = 0
            };
        }
        public static SpecificFileDto FileDtoTransformation(this DsSpecificFileDto dsSpecificDto)
        {
            return new SpecificFileDto
            {
                FileName = dsSpecificDto.FileName,
                S3Path = dsSpecificDto.S3Path
            };
        }
        public static KpiValueDto ToKpiValueDto(this DsSpecificValueDto value)
        {
            return new KpiValueDto
            {
                Value = value.Value,
                Unit = value.CurrencySymbol,
                Source = Path.GetFileNameWithoutExtension(Path.GetFileName(value.FilePath)),
                FileType = value.FileType,
                UnitScale = value.UnitScale,
                ConfidenceScore = value.ConfidenceScore,
                KpiInfo = value.CurrencySymbol.DetermineKpiInfo(),
                PageNumber = value.FileType == "excel" ? 0 : (int)value.PageNumber,
            };
        }
        public static List<DsSpecificValueDto> DsSpecificValueDtoEmpty()
        {
            return [new DsSpecificValueDto() {
                Column=string.Empty,
                Value=string.Empty,
                UnitScale=string.Empty,
                Analysis=new(),
                CurrencySymbol=string.Empty,
                Excel=new(),
                Pdf=new(),
                PageNumber=0,
                FileId=string.Empty,
                FileType=string.Empty,
            }];
        }
    }
}
