﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="12.0.1" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
		<PackageReference Include="Microsoft.AspNetCore.Http.Features" Version="5.0.17" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
		<PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
		<PackageReference Include="MongoDB.Bson" Version="3.2.1" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\AWSS3\AWSS3.csproj" />
	  <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
	  <ProjectReference Include="..\Notification\Notification.csproj" />
	  <ProjectReference Include="..\Persistence\Persistence.csproj" />
	</ItemGroup>
</Project>
