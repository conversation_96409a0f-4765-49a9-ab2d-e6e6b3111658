﻿using DataIngestionService;
using Infrastructure.Contract;
using Ingestion.API.Helpers;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace API.Controllers
{
    [Route("api")]
    [Authorize(Policy = JwtBearerDefaults.AuthenticationScheme)]
    public class DocumentController(ILogger<DocumentController> logger, IUpload uploadService,IHelperService helperService) : ControllerBase
    {
        private readonly ILogger<DocumentController> _logger = logger;
        private readonly IUpload _uploadService = uploadService;
        private readonly IHelperService _helperService = helperService;

        [HttpPost("upload")]
        public async Task<ActionResult> Upload(UploadDto upload)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            _logger.LogInformation("Starting file upload processing");
            int userId = await _helperService.GetCurrentUserId(User);
            var response = await _uploadService.UploadFile(upload, userId);
            return Ok(response);
        }

        [HttpPost("update-file-configs")]
        public async Task<ActionResult> UpdateDocumentCollectionInformation([FromBody] List<FileConfigurationDetails> documentstoUpdate)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            _logger.LogInformation("Starting file upload processing");
            int userId = await _helperService.GetCurrentUserId(User);
            var response = await _uploadService.UpdateDocumentConfigurations(documentstoUpdate, userId);
            return Ok(response);
        }
        [HttpGet("spread-details")]
        public async Task<ActionResult> GetSpreadDetails(int limit = 10, int offset = 0)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            if (limit <= 0) limit = 10;
            if (offset < 0) offset = 0;
            _logger.LogInformation("Starting to fetch spread details");
            var response = await _uploadService.GetUploadedDocumentDetails(limit,offset);
            _logger.LogInformation("Successfully retrieved spread details");
            return Ok(response);
        }
        [HttpGet("process-details/{processId}")]
        public async Task<ActionResult> GetProcessDetails(Guid processId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            _logger.LogInformation("Starting to fetch process details for ProcessId: {ProcessId}", processId);
            var response = await _uploadService.GetProcessDetailsById(processId);
            _logger.LogInformation("Successfully retrieved process details for ProcessId: {ProcessId}", processId);
            return Ok(response);
        }
        [HttpGet("status-count")]
        public async Task<ActionResult<StatusCount>> GetDocumentProcessingStatusCount()
        {
            var result = await _uploadService.GetDocumentProcessingStatusCount();
            return Ok(result);
        }
    }
}
