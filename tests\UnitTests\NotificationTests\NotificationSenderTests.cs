using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Moq;
using Notification.Hubs;
using Notification.Models;
using Notification.Services;
using Xunit;

namespace UnitTests.NotificationTests
{
    public class NotificationSenderTests
    {
        private readonly Mock<IHubContext<NotificationHub>> _mockHubContext;
        private readonly Mock<IHubClients> _mockHubClients;
        private readonly Mock<IClientProxy> _mockClientProxy;
        private readonly NotificationSender _notificationSender;

        public NotificationSenderTests()
        {
            _mockHubContext = new Mock<IHubContext<NotificationHub>>();
            _mockHubClients = new Mock<IHubClients>();
            _mockClientProxy = new Mock<IClientProxy>();

            _mockHubContext.Setup(x => x.Clients).Returns(_mockHubClients.Object);
            _mockHubClients.Setup(x => x.All).Returns(_mockClientProxy.Object);
            _mockHubClients.Setup(x => x.User(It.IsAny<string>())).Returns(_mockClientProxy.Object);

            _notificationSender = new NotificationSender(_mockHubContext.Object);
        }

        [Fact]
        public async Task SendToAllAsync_ShouldSendNotificationToAllClients()
        {
            // Arrange
            var notification = new NotificationMessage
            {
                Type = NotificationType.Info,
                Title = "Test Notification",
                Message = "This is a test notification"
            };

            // Act
            await _notificationSender.SendToAllAsync(notification);

            // Assert
            _mockClientProxy.Verify(
                x => x.SendCoreAsync(
                    "ReceiveNotification",
                    It.Is<object[]>(o => o[0] == notification),
                    default),
                Times.Once);
        }

        [Fact]
        public async Task SendToUserAsync_ShouldSendNotificationToSpecificUser()
        {
            // Arrange
            var user = "testUser";
            var notification = new NotificationMessage
            {
                Type = NotificationType.Success,
                Title = "User Test",
                Message = "This is a user-specific test notification"
            };

            // Act
            await _notificationSender.SendToUserAsync(user, notification);

            // Assert
            _mockHubClients.Verify(x => x.User(user), Times.Once);
            _mockClientProxy.Verify(
                x => x.SendCoreAsync(
                    "ReceiveNotification",
                    It.Is<object[]>(o => o[0] == notification),
                    default),
                Times.Once);
        }

        [Fact]
        public async Task SendEntityNotificationAsync_ShouldUpdateEntityInfoAndSendToAll()
        {
            // Arrange
            var entityType = "document";
            var entityId = "doc123";
            var notification = new NotificationMessage
            {
                Type = NotificationType.DocumentProcessing,
                Title = "Document Processing",
                Message = "Processing started"
            };

            // Act
            await _notificationSender.SendEntityNotificationAsync(entityType, entityId, notification);

            // Assert
            Assert.Equal(entityType, notification.EntityType);
            Assert.Equal(entityId, notification.EntityId);
            _mockClientProxy.Verify(
                x => x.SendCoreAsync(
                    "ReceiveNotification",
                    It.Is<object[]>(o => o[0] == notification),
                    default),
                Times.Once);
        }
    }
}
