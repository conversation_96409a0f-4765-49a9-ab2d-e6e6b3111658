﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <ImplicitUsings>enable</ImplicitUsings>
	  <GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>
	  <StaticWebAssetsDisableProjectPrecompressedFiles>true</StaticWebAssetsDisableProjectPrecompressedFiles>
	  <StaticWebAssetsEnabled>false</StaticWebAssetsEnabled>
	  <GenerateStaticWebAssetsManifest>false</GenerateStaticWebAssetsManifest>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
    <PackageReference Include="AspNetCoreRateLimit" Version="5.0.0" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.400.113" />
    <PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.31.0" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.0.1" />
    <PackageReference Include="MongoDB.Bson" Version="3.2.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
	<PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.400" />
	<PackageReference Include="Quartz.Extensions.Hosting" Version="3.13.1" />
	<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
	  <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.0.1" />
    <PackageReference Include="System.Runtime.Caching" Version="8.0.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AWSS3\AWSS3.csproj" />
    <ProjectReference Include="..\DataIngestionService\DataIngestionService.csproj" />
    <ProjectReference Include="..\Persistence\Persistence.csproj" />
    <ProjectReference Include="..\Notification\Notification.csproj" />
  </ItemGroup>

</Project>
