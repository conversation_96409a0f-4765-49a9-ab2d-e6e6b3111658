using Microsoft.Extensions.Options;
using MongoDB.Bson;
using MongoDB.Driver;
using Moq;
using Persistence.MongoDb;
using System.Linq.Expressions;
using Xunit;

namespace UnitTests.Persistence
{
    public class MongoRepositoryTests
    {
        private readonly Mock<IRepository<TestEntity>> _mockRepository;

        public MongoRepositoryTests()
        {
            _mockRepository = new Mock<IRepository<TestEntity>>();
        }

        [Fact]
        public async Task GetAllAsync_WithoutFilter_ShouldReturnAllDocuments()
        {
            // Arrange
            var expectedEntities = new List<TestEntity>
            {
                new TestEntity { Id = ObjectId.GenerateNewId().ToString(), Name = "Test1" },
                new TestEntity { Id = ObjectId.GenerateNewId().ToString(), Name = "Test2" }
            };

            _mockRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(expectedEntities);

            // Act
            var result = await _mockRepository.Object.GetAllAsync();

            // Assert
            Assert.Equal(expectedEntities.Count, result.Count());
            Assert.Equal("Test1", result.First().Name);
            Assert.Equal("Test2", result.Last().Name);
            _mockRepository.Verify(r => r.GetAllAsync(), Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_WithFilter_ShouldReturnFilteredDocuments()
        {
            // Arrange
            Expression<Func<TestEntity, bool>> filter = x => x.Name == "Test1";
            var expectedEntities = new List<TestEntity>
            {
                new TestEntity { Id = ObjectId.GenerateNewId().ToString(), Name = "Test1" }
            };

            _mockRepository.Setup(r => r.GetAllAsync(filter)).ReturnsAsync(expectedEntities);

            // Act
            var result = await _mockRepository.Object.GetAllAsync(filter);

            // Assert
            Assert.Single(result);
            Assert.Equal("Test1", result.First().Name);
            _mockRepository.Verify(r => r.GetAllAsync(filter), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ShouldReturnEntity()
        {
            // Arrange
            var objectId = ObjectId.GenerateNewId();
            var expectedEntity = new TestEntity { Id = objectId.ToString(), Name = "Test1" };

            _mockRepository.Setup(r => r.GetByIdAsync(objectId.ToString())).ReturnsAsync(expectedEntity);

            // Act
            var result = await _mockRepository.Object.GetByIdAsync(objectId.ToString());

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedEntity.Id, result.Id);
            Assert.Equal(expectedEntity.Name, result.Name);
            _mockRepository.Verify(r => r.GetByIdAsync(objectId.ToString()), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ShouldReturnNull()
        {
            // Arrange
            var invalidId = "invalid-id";
            _mockRepository.Setup(r => r.GetByIdAsync(invalidId)).ReturnsAsync((TestEntity?)null);

            // Act
            var result = await _mockRepository.Object.GetByIdAsync(invalidId);

            // Assert
            Assert.Null(result);
            _mockRepository.Verify(r => r.GetByIdAsync(invalidId), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_WithValidEntity_ShouldCallRepository()
        {
            // Arrange
            var entity = new TestEntity { Name = "Test1" };
            _mockRepository.Setup(r => r.CreateAsync(entity)).Returns(Task.CompletedTask);

            // Act
            await _mockRepository.Object.CreateAsync(entity);

            // Assert
            _mockRepository.Verify(r => r.CreateAsync(entity), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithValidIdAndEntity_ShouldCallRepository()
        {
            // Arrange
            var objectId = ObjectId.GenerateNewId();
            var entity = new TestEntity { Id = objectId.ToString(), Name = "UpdatedTest" };

            _mockRepository.Setup(r => r.UpdateAsync(objectId.ToString(), entity)).Returns(Task.CompletedTask);

            // Act
            await _mockRepository.Object.UpdateAsync(objectId.ToString(), entity);

            // Assert
            _mockRepository.Verify(r => r.UpdateAsync(objectId.ToString(), entity), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_WithValidId_ShouldCallRepository()
        {
            // Arrange
            var objectId = ObjectId.GenerateNewId();
            _mockRepository.Setup(r => r.DeleteAsync(objectId.ToString())).Returns(Task.CompletedTask);

            // Act
            await _mockRepository.Object.DeleteAsync(objectId.ToString());

            // Assert
            _mockRepository.Verify(r => r.DeleteAsync(objectId.ToString()), Times.Once);
        }

        [Fact]
        public async Task BulkInsertAsync_WithValidEntities_ShouldCallRepository()
        {
            // Arrange
            var entities = new List<TestEntity>
            {
                new TestEntity { Name = "Test1" },
                new TestEntity { Name = "Test2" }
            };

            _mockRepository.Setup(r => r.BulkInsertAsync(entities)).Returns(Task.CompletedTask);

            // Act
            await _mockRepository.Object.BulkInsertAsync(entities);

            // Assert
            _mockRepository.Verify(r => r.BulkInsertAsync(entities), Times.Once);
        }

        [Fact]
        public async Task BulkUpdateAsync_WithValidEntitiesAndIds_ShouldCallRepository()
        {
            // Arrange
            var objectId1 = ObjectId.GenerateNewId();
            var objectId2 = ObjectId.GenerateNewId();
            var entitiesWithIds = new Dictionary<string, TestEntity>
            {
                { objectId1.ToString(), new TestEntity { Id = objectId1.ToString(), Name = "Updated1" } },
                { objectId2.ToString(), new TestEntity { Id = objectId2.ToString(), Name = "Updated2" } }
            };

            _mockRepository.Setup(r => r.BulkUpdateAsync(entitiesWithIds)).Returns(Task.CompletedTask);

            // Act
            await _mockRepository.Object.BulkUpdateAsync(entitiesWithIds);

            // Assert
            _mockRepository.Verify(r => r.BulkUpdateAsync(entitiesWithIds), Times.Once);
        }

        [Fact]
        public async Task FindOneAsync_WithFilter_ShouldReturnMatchingEntity()
        {
            // Arrange
            Expression<Func<TestEntity, bool>> filter = x => x.Name == "Test1";
            var expectedEntity = new TestEntity { Id = ObjectId.GenerateNewId().ToString(), Name = "Test1" };

            _mockRepository.Setup(r => r.FindOneAsync(filter)).ReturnsAsync(expectedEntity);

            // Act
            var result = await _mockRepository.Object.FindOneAsync(filter);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedEntity.Id, result.Id);
            Assert.Equal(expectedEntity.Name, result.Name);
            _mockRepository.Verify(r => r.FindOneAsync(filter), Times.Once);
        }

        [Fact]
        public async Task FindOneAsync_WithNoMatchingFilter_ShouldReturnNull()
        {
            // Arrange
            Expression<Func<TestEntity, bool>> filter = x => x.Name == "NonExistent";
            _mockRepository.Setup(r => r.FindOneAsync(filter)).ReturnsAsync((TestEntity?)null);

            // Act
            var result = await _mockRepository.Object.FindOneAsync(filter);

            // Assert
            Assert.Null(result);
            _mockRepository.Verify(r => r.FindOneAsync(filter), Times.Once);
        }
    }

    // Test entity for the Repository tests
    public class TestEntity
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }
}